[project]
name = "NotePlanBot"
version = "0.1.0"
description = "A bot that interfaces with NotePlan for task management"
readme = "README.md"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
requires-python = ">=3.9"
dependencies = [
    "pydantic>=2.0.0",
    "rich>=13.0.0",
    "python-dotenv>=1.0.0",
    "watchdog>=3.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/NotePlanBot"]

[tool.ruff]
line-length = 150
indent-width = 4

[tool.ruff.lint]
select = ["E", "F", "I"]
ignore = ["E501"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"

[tool.pytest.ini_options]
pythonpath = ["src"]
testpaths = ["tests"]