# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a NotePlan application data directory containing user notes, calendar data, plugins, and a Python bot project (NotePlanBot). The main development focus areas are:

1. **NotePlanBot** - A Python three-pane system that interfaces with NotePlan for AI-assisted task management
2. **NotePlan Plugins** - JavaScript/TypeScript plugins that extend NotePlan functionality

## NotePlanBot Development

### Project Structure
```
NotePlanBot/
├── run.py                      # Main entry point
├── .env                        # Environment variables (API keys)
├── pyproject.toml              # Project configuration
├── justfile                    # Command shortcuts
├── README.md                   # Project documentation
├── src/NotePlanBot/
│   ├── main.py                 # Core application orchestration
│   ├── config.py               # Global CFG singleton configuration
│   ├── todolist_monitor.py     # Bridge file monitoring with Claude Code
│   ├── visualizer.py           # Three-pane terminal UI
│   ├── editor/                 # Components for editing NotePlan notes
│   │   └── noteplan_editor.py  # Create/update notes and tasks
│   ├── reader/                 # Components for reading NotePlan notes
│   │   └── noteplan_reader.py  # Parse notes and extract tasks
│   └── files/bridge/           # Bridge files for NotePlan communication
│       ├── bridge_model.py     # Pydantic model with task parsing
│       ├── bridge.md           # Default bridge file (monitored)
│       └── config.py           # Bridge-specific configuration
```

### Development Commands
- Create virtual environment: `python -m venv .venv`
- Activate environment: `source .venv/bin/activate`
- Install dependencies: `pip install -e .`
- Run the bot: `python run.py` or `just run`
- Run visualizer: `just visualizer`
- See all commands: `just`

### Three-Pane System
The bot implements a three-pane workflow:
1. **Bridge File** (left) - User types context/activities/requests
2. **Task List** (middle) - AI-managed tasks from NotePlan
3. **AI Log** (right) - Claude Code suggestions and actions

### Bridge File Communication
- Monitor checks bridge.md every 30 seconds (configurable)
- Triggers Claude Code on > 1 LOC changes
- Parses tasks in TASKS section
- Supports both XML-style and markdown formatting

### Key Features Implemented
- ✅ Bridge file monitoring with change detection
- ✅ Claude Code subprocess integration
- ✅ NotePlan reader (reads notes, extracts tasks)
- ✅ NotePlan editor (creates/updates notes and tasks)
- ✅ Global configuration management (CFG singleton)
- ✅ Three-pane terminal visualization
- ✅ Proper Python package structure
- ✅ Project documentation and justfile

### Configuration
All settings in `.env` or via CFG singleton:
- `BRIDGE_FILE`: Path to monitored bridge file
- `TODO_CHECK_INTERVAL`: Check interval in seconds (default: 30)
- `CLAUDE_COMMAND`: Claude Code command (default: claude)
- `NOTEPLAN_TASK_NOTE`: NotePlan note for tasks
- `AUTO_CREATE_DAILY_NOTE`: Auto-create daily notes (default: true)

### Testing
- Run specific components: `python -m NotePlanBot.component_name`
- Test reader: `just test-reader`
- Test editor: `just test-editor`
- Show config: `just config`

## NotePlan Plugin Development

### Plugin Architecture
NotePlan plugins are JavaScript-based extensions that:
- Are bundled into a single `script.js` file
- Configure commands and settings via `plugin.json`
- Can include React components for complex UIs
- Support WebView for rich interfaces

### Key NotePlan API Objects
- **Editor**: Text manipulation (`insertTextAtCursor`, `updateParagraph`)
- **DataStore**: Data persistence and note access
- **CommandBar**: User interaction dialogs
- **Calendar**: Calendar note integration
- **Clipboard**: System clipboard access

### Plugin Development Workflow
1. Study existing plugins in `/Plugins/` for patterns
2. Each plugin requires:
   - `plugin.json` with metadata and command definitions
   - `script.js` with the implementation
   - Optional: README.md, CHANGELOG.md
3. Settings are stored in `data/{plugin.id}/settings.json`
4. Plugins can communicate via x-callback URLs

### Common Plugin Patterns
- Date/time automation and insertion
- Task management and sorting
- Note organization and quick capture
- UI enhancements (dashboards, themes)
- Integration with external services

## Important Notes

- This is NotePlan's application data directory, not a typical source code repository
- User notes are stored as plain text/markdown files in `/Notes/` and `/Calendar/`
- Plugin development uses JavaScript/TypeScript, not Python
- The NotePlanBot project is currently just a skeleton with no implementation
- Be cautious when modifying user data files directly