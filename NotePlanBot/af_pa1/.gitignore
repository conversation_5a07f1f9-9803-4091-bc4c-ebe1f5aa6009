# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Environment variables
.env
.env.local
.env.*.local

# Gmail authentication
token.pickle
gmail_token.pickle
credentials.json
creds.json

# Project specific
*.xlsx
!test_*.xlsx
!demo_*.xlsx
gmail_tasks.json
gmail_snooze_queue.json
project_contexts.xlsx

# Logs
*.log
logs/

# Cache
.cache/
*.db
*.db-shm
*.db-wal

# Temporary files
.tmp/
tmp/

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Backup files
*.bak
*~
*.swp