"""Test task title generation functionality.

1. Test LLM generation
2. Test fallback on error
v1
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.gmail.ui_task_manager import Task<PERSON>anager


def test_generate_task_title():
    """Test that generate_task_title returns reasonable titles.
    
    1. Test with normal email
    2. Test with receipt
    3. Test fallback
    v1
    """
    task_manager = TaskManager()
    
    # Test 1: Action-oriented email
    title = task_manager.generate_task_title(
        "Re: Q4 Budget Review Meeting",
        "We need to finalize the budget by Friday"
    )
    if not title or len(title) > 50:
        print("✗ Task title generation failed: Invalid title length")
        return False
    
    # Test 2: Long subject fallback
    long_subject = "A" * 100
    title = task_manager.generate_task_title(long_subject, "")
    if len(title) > 50:
        print("✗ Task title generation failed: Title too long")
        return False
    
    print("✓ Task title generation works")
    return True


def test_d_shortcut_helper():
    """Test the 'd' shortcut helper function.
    
    1. Test 'd' returns AI title
    2. Test custom input returns custom
    3. Test empty returns AI title
    v1
    """
    from src.tools.gmail.ui import _d_to_use_llm_title
    from unittest.mock import patch
    
    # Test 'd' shortcut
    with patch('rich.prompt.Prompt.ask', return_value='d'):
        result = _d_to_use_llm_title("AI Title")
        if result != "AI Title":
            print("✗ 'd' shortcut failed")
            return False
    
    # Test custom input
    with patch('rich.prompt.Prompt.ask', return_value='Custom Title'):
        result = _d_to_use_llm_title("AI Title")
        if result != "Custom Title":
            print("✗ Custom input failed")
            return False
    
    # Test empty input
    with patch('rich.prompt.Prompt.ask', return_value=''):
        result = _d_to_use_llm_title("AI Title")
        if result != "AI Title":
            print("✗ Empty input failed")
            return False
    
    print("✓ 'd' shortcut helper works correctly")
    return True


def main():
    """Run all tests.
    
    1. Test title generation
    2. Test 'd' shortcut
    v2
    """
    print("Testing task title generation functionality...")
    
    if not test_generate_task_title():
        print("\nFAILED: Task title generation broken")
        return False
    
    if not test_d_shortcut_helper():
        print("\nFAILED: 'd' shortcut helper broken")
        return False
    
    print("\nAll task title generation tests passed!")
    return True


if __name__ == "__main__":
    main()