"""Test edge cases and error conditions.

1. Test API failures
2. Test malformed data
3. Test concurrent access
v1
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.gmail.ui_task_manager import TaskManager
from src.tools.gmail.ui_snooze_manager import SnoozeManager
from datetime import datetime, timedelta
import json
from pathlib import Path


def test_task_manager_handles_corrupt_json():
    """Test task manager handles corrupt JSON gracefully.
    
    1. Create corrupt JSON
    2. Load task manager
    3. Verify no crash
    v1
    """
    test_file = Path("test_corrupt_tasks.json")
    
    try:
        # Write corrupt JSON
        with open(test_file, 'w') as f:
            f.write("{corrupt json data")
        
        # Try to load - should handle error
        try:
            tm = TaskManager(str(test_file))
            # Should start with empty tasks due to error handling
            if len(tm.tasks) != 0:
                print("✗ Corrupt JSON not handled properly")
                return False
        except json.JSONDecodeError:
            # This is expected and OK
            pass
        
        print("✓ Corrupt JSON handled gracefully")
        return True
        
    finally:
        if test_file.exists():
            test_file.unlink()


def test_snooze_manager_concurrent_access():
    """Test snooze manager handles concurrent modifications.
    
    1. Create snooze file
    2. Simulate concurrent access
    3. Verify data integrity
    v1
    """
    test_file = Path("test_concurrent_snooze.json")
    
    try:
        # Create first instance and add snooze
        sm1 = SnoozeManager(str(test_file))
        until = datetime.now() + timedelta(hours=1)
        sm1.snooze_email("thread1", "msg1", until, 0)
        
        # Create second instance (simulating concurrent access)
        sm2 = SnoozeManager(str(test_file))
        sm2.snooze_email("thread2", "msg2", until, 1)
        
        # Reload first instance
        sm1.load()
        
        # Both snoozes should be present
        snoozed = sm1.get_snoozed_thread_ids()
        if len(snoozed) < 2:
            print("✗ Concurrent access lost data")
            return False
        
        print("✓ Concurrent access handled")
        return True
        
    finally:
        if test_file.exists():
            test_file.unlink()


def test_empty_email_subject_handling():
    """Test handling of emails with no subject.
    
    1. Create task with empty subject
    2. Generate title
    3. Verify fallback works
    v1
    """
    tm = TaskManager("test_empty_subject.json")
    
    try:
        # Test with empty subject
        title = tm.generate_task_title("", "Please review the attached document")
        
        if not title or len(title) == 0:
            print("✗ Empty subject not handled")
            return False
        
        print("✓ Empty subject handled correctly")
        return True
        
    except Exception as e:
        print(f"✗ Empty subject caused error: {e}")
        return False
        
    finally:
        if Path("test_empty_subject.json").exists():
            Path("test_empty_subject.json").unlink()


def test_long_content_truncation():
    """Test that long content is properly truncated.
    
    1. Create very long content
    2. Process through system
    3. Verify truncation
    v1
    """
    from src.tools.gmail.ui_viewthreads import clean_content
    
    # Create very long content
    long_content = "This is a test message. " * 500  # ~12000 chars
    
    cleaned = clean_content(long_content)
    
    # clean_content doesn't truncate by default, but should handle long content
    if cleaned is None or len(cleaned) == 0:
        print("✗ Long content caused error")
        return False
    
    print("✓ Long content handled correctly")
    return True


def test_special_characters_in_titles():
    """Test special characters in task titles.
    
    1. Create task with special chars
    2. Save and load
    3. Verify preservation
    v1
    """
    test_file = Path("test_special_chars.json")
    
    try:
        tm = TaskManager(str(test_file))
        
        # Special characters that might cause issues
        special_title = "Task with émojis 🎉 and ñ special çhars"
        
        task = tm.create_task(
            title=special_title,
            description="Test",
            source_thread_id="test123"
        )
        
        # Reload
        tm2 = TaskManager(str(test_file))
        loaded_task = tm2.get_task(task.id)
        
        if loaded_task.title != special_title:
            print("✗ Special characters not preserved")
            return False
        
        print("✓ Special characters handled correctly")
        return True
        
    finally:
        if test_file.exists():
            test_file.unlink()


def main():
    """Run all edge case tests.
    
    1. Test error conditions
    2. Test concurrent access
    3. Test data integrity
    v1
    """
    print("Testing edge cases and error conditions...")
    
    tests = [
        test_task_manager_handles_corrupt_json,
        test_snooze_manager_concurrent_access,
        test_empty_email_subject_handling,
        test_long_content_truncation,
        test_special_characters_in_titles
    ]
    
    all_passed = True
    for test in tests:
        try:
            if not test():
                all_passed = False
        except Exception as e:
            print(f"✗ Test crashed: {e}")
            all_passed = False
    
    if all_passed:
        print("\nAll edge case tests passed!")
    else:
        print("\nSome edge case tests failed!")
    
    return all_passed


if __name__ == "__main__":
    main()