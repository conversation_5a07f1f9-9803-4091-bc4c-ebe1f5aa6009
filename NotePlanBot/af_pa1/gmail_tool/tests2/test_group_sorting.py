"""Test email group sorting functionality.

1. Test sorting by group ID
2. Test preserving recency within groups
3. Test ungrouped emails
v1
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.gmail.ui import cfg, current_email_group
from src.tools.gmail.models import EmailGroup


def test_group_sorting_logic():
    """Test that emails sort by group first, then recency.
    
    1. Create test data
    2. Apply sorting
    3. Verify order
    v1
    """
    # Create test threads with original order
    test_threads = [
        {'thread_id': 't1', 'subject': 'Email 1', 'original_order': 0},
        {'thread_id': 't2', 'subject': 'Email 2', 'original_order': 1},
        {'thread_id': 't3', 'subject': 'Email 3', 'original_order': 2},
        {'thread_id': 't4', 'subject': 'Email 4', 'original_order': 3},
    ]
    
    # Create test grouping
    test_group = EmailGroup(
        groups={1: "Work", 2: "Personal"},
        assignments={
            't1': 2,  # Personal
            't2': 1,  # Work
            't3': 1,  # Work
            't4': 2,  # Personal
        },
        reasoning="Test grouping"
    )
    
    # Apply sorting logic
    sorted_threads = sorted(test_threads, key=lambda t: (
        test_group.assignments.get(t['thread_id'], 999),
        t['original_order']
    ))
    
    # Verify: Work emails (t2, t3) come first, then Personal (t1, t4)
    expected_order = ['t2', 't3', 't1', 't4']
    actual_order = [t['thread_id'] for t in sorted_threads]
    
    if actual_order != expected_order:
        print(f"✗ Group sorting failed: {actual_order} != {expected_order}")
        return False
    
    print("✓ Group sorting logic works correctly")
    return True


def test_ungrouped_emails_sort_last():
    """Test that ungrouped emails appear last.
    
    1. Mix grouped and ungrouped
    2. Apply sorting
    3. Verify ungrouped last
    v1
    """
    test_threads = [
        {'thread_id': 't1', 'subject': 'Ungrouped', 'original_order': 0},
        {'thread_id': 't2', 'subject': 'Grouped', 'original_order': 1},
    ]
    
    test_group = EmailGroup(
        groups={1: "Work"},
        assignments={'t2': 1},  # t1 is ungrouped
        reasoning="Test"
    )
    
    sorted_threads = sorted(test_threads, key=lambda t: (
        test_group.assignments.get(t['thread_id'], 999),
        t['original_order']
    ))
    
    if sorted_threads[0]['thread_id'] != 't2':
        print("✗ Ungrouped emails not sorted last")
        return False
    
    print("✓ Ungrouped emails sort last")
    return True


def test_recency_preserved_within_groups():
    """Test that recency is preserved within each group.
    
    1. Create emails in same group
    2. Apply sorting
    3. Verify recency order
    v1
    """
    test_threads = [
        {'thread_id': 't1', 'subject': 'Newest', 'original_order': 0},
        {'thread_id': 't2', 'subject': 'Middle', 'original_order': 1},
        {'thread_id': 't3', 'subject': 'Oldest', 'original_order': 2},
    ]
    
    # All in same group
    test_group = EmailGroup(
        groups={1: "Work"},
        assignments={'t1': 1, 't2': 1, 't3': 1},
        reasoning="Test"
    )
    
    sorted_threads = sorted(test_threads, key=lambda t: (
        test_group.assignments.get(t['thread_id'], 999),
        t['original_order']
    ))
    
    # Should maintain original order within group
    expected_order = ['t1', 't2', 't3']
    actual_order = [t['thread_id'] for t in sorted_threads]
    
    if actual_order != expected_order:
        print("✗ Recency not preserved within groups")
        return False
    
    print("✓ Recency preserved within groups")
    return True


def main():
    """Run all group sorting tests.
    
    1. Test basic sorting
    2. Test ungrouped handling
    3. Test recency preservation
    v1
    """
    print("Testing group sorting functionality...")
    
    tests = [
        test_group_sorting_logic,
        test_ungrouped_emails_sort_last,
        test_recency_preserved_within_groups
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    if all_passed:
        print("\nAll group sorting tests passed!")
    else:
        print("\nSome group sorting tests failed!")
    
    return all_passed


if __name__ == "__main__":
    main()