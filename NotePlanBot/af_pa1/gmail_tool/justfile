# Gmail Tools Commands

# Default: show all commands
default:
    @just --list

# Run interactive Gmail UI
ui:
    source .venv/bin/activate && python3 -m src.tools.gmail.ui

# Generate draft email replies using Claude
drafts:
    source .venv/bin/activate && python3 -m src.tools.gmail.draft_replies

# Find projects mentioned in emails
find-projects:
    source .venv/bin/activate && python3 -m src.tools.gmail.find_projects

# Test context retrieval system
context:
    source .venv/bin/activate && python3 -m src.tools.gmail.context_retrieval

# Run the feature demo
demo:
    source .venv/bin/activate && python3 demo_features.py

# Run all regression tests
test:
    source .venv/bin/activate && python3 tests2/run_all_tests.py

# Run specific test suite
test-ui:
    source .venv/bin/activate && python3 tests2/test_gmail_ui.py

test-store:
    source .venv/bin/activate && python3 tests2/test_project_store.py

test-integration:
    source .venv/bin/activate && python3 tests2/test_integration.py

test-ui-enhancements:
    source .venv/bin/activate && python3 tests2/test_ui_enhancements.py

# Setup: install dependencies
setup:
    python3 -m venv .venv
    source .venv/bin/activate && pip install -r requirements.txt
    source .venv/bin/activate && pip install anthropic openpyxl pandas

# Run the original main.py (calendar/gmail analysis)
main:
    source .venv/bin/activate && python3 src/main.py

# Quick project store test
store-test:
    source .venv/bin/activate && python3 -m src.tools.gmail.storage.project_store

# View Excel project data
view-projects:
    open project_contexts.xlsx 2>/dev/null || echo "No project file yet"

# Clean test files
clean:
    rm -f test_*.xlsx
    rm -f demo_projects.xlsx
    echo "Cleaned test Excel files"

# Check environment setup
check-env:
    @echo "Checking environment..."
    @test -f .env && echo "✓ .env file exists" || echo "✗ .env file missing"
    @grep -q "OPENAI_API_KEY=" .env && echo "✓ OpenAI API key set" || echo "✗ OpenAI API key missing"
    @grep -q "ANTHROPIC_API_KEY=" .env && echo "✓ Anthropic API key set" || echo "✗ Anthropic API key missing"
    @test -d .venv && echo "✓ Virtual environment exists" || echo "✗ Virtual environment missing"
    @test -f token.pickle && echo "✓ Gmail authenticated" || echo "✗ Gmail not authenticated"

# Quick status of unread emails
status:
    source .venv/bin/activate && python3 -c "from src.tools.gmail.service import GmailService; g=GmailService(); msgs=g.list_unread_messages(max_results=5); print(f'Unread emails: {len(msgs)}')"

# Help with common tasks
help:
    @echo "Gmail Tools Quick Reference:"
    @echo ""
    @echo "Main Tools:"
    @echo "  just ui          - Browse emails interactively"
    @echo "  just drafts      - Generate email replies with Claude"
    @echo "  just find-projects - Extract project info from emails"
    @echo ""
    @echo "Testing:"
    @echo "  just test        - Run all tests"
    @echo "  just demo        - See feature demonstration"
    @echo ""
    @echo "Setup:"
    @echo "  just setup       - Install dependencies"
    @echo "  just check-env   - Verify environment"
    @echo ""
    @echo "UI Navigation:"
    @echo "  1-10            - Open thread by number"
    @echo "  11, 12, ...     - Open thread 1, 2, ..."
    @echo "  3               - Group emails using AI"
    @echo "  4x              - Snooze thread x (e.g., 41 snoozes thread 1)"
    @echo "  5x              - Create task from thread x"
    @echo "    'd'           - Use AI-suggested task title"
    @echo "  n/p             - Next/previous page"
    @echo "  c               - Column settings"
    @echo "  q               - Quit"