#!/usr/bin/env python3
"""
Personal Assistant (PA) Command Center
Combines Gmail and NotePlan calendar/task management
"""

import os
import sys
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

# Add gmail_tool to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'gmail_tool'))

from src.tools.gmail.service import GmailService
from src.NotePlanBot.reader.noteplan_reader import Note<PERSON><PERSON><PERSON>eader
from src.NotePlanBot.editor.noteplan_editor import NotePlanEditor

class PersonalAssistant:
    def __init__(self):
        self.gmail = GmailService()
        self.noteplan_reader = NotePlanReader()
        self.noteplan_editor = NotePlanEditor()
        
    def get_email_summary(self, max_results=10):
        """Get summary of unread emails"""
        print(f"\n📧 EMAIL SUMMARY (Unread: {len(self.gmail.list_unread_messages(max_results=max_results))})")
        print("-" * 60)
        
        messages = self.gmail.list_unread_messages(max_results=max_results)
        for i, msg in enumerate(messages, 1):
            thread = self.gmail.get_thread(msg['threadId'])
            subject = thread[0].get('subject', 'No subject')
            sender = thread[0].get('from', 'Unknown')
            print(f"{i}. {subject[:50]}... - {sender}")
            
    def get_today_schedule(self):
        """Get today's calendar events and tasks"""
        today = datetime.now().strftime("%Y%m%d")
        calendar_path = Path(f"../Calendar/{today}.txt")
        
        print(f"\n📅 TODAY'S SCHEDULE ({datetime.now().strftime('%A, %B %d, %Y')})")
        print("-" * 60)
        
        if calendar_path.exists():
            content = self.noteplan_reader.read_note(str(calendar_path))
            lines = content.split('\n')
            
            # Extract events (lines starting with time)
            events = []
            tasks = []
            for line in lines:
                if line.strip():
                    if any(line.startswith(f"{h:02d}:") for h in range(24)):
                        events.append(line)
                    elif line.startswith("- [ ]") or line.startswith("- [x]"):
                        tasks.append(line)
            
            if events:
                print("\nEvents:")
                for event in events:
                    print(f"  {event}")
            else:
                print("\nNo scheduled events")
                
            if tasks:
                print("\nTasks:")
                for task in tasks[:5]:  # Show first 5 tasks
                    print(f"  {task}")
                if len(tasks) > 5:
                    print(f"  ... and {len(tasks) - 5} more tasks")
            else:
                print("\nNo tasks for today")
        else:
            print("No calendar entry for today")
            
    def get_upcoming_events(self, days=7):
        """Get upcoming events for the next N days"""
        print(f"\n📆 UPCOMING EVENTS (Next {days} days)")
        print("-" * 60)
        
        event_count = 0
        for i in range(1, days + 1):
            date = datetime.now() + timedelta(days=i)
            date_str = date.strftime("%Y%m%d")
            calendar_path = Path(f"../Calendar/{date_str}.txt")
            
            if calendar_path.exists():
                content = self.noteplan_reader.read_note(str(calendar_path))
                lines = content.split('\n')
                
                events_found = False
                for line in lines:
                    if line.strip() and any(line.startswith(f"{h:02d}:") for h in range(24)):
                        if not events_found:
                            print(f"\n{date.strftime('%A, %B %d')}:")
                            events_found = True
                        print(f"  {line}")
                        event_count += 1
                        
        if event_count == 0:
            print("No upcoming events scheduled")
            
    def quick_actions_menu(self):
        """Show quick actions menu"""
        print("\n⚡ QUICK ACTIONS")
        print("-" * 60)
        print("1. Open Gmail UI")
        print("2. Draft email replies")
        print("3. Create task from email")
        print("4. Add calendar event")
        print("5. View full email list")
        print("6. Search notes")
        print("0. Exit")
        
        choice = input("\nSelect action (0-6): ")
        
        if choice == "1":
            subprocess.run(["just", "ui"], cwd="gmail_tool")
        elif choice == "2":
            subprocess.run(["just", "drafts"], cwd="gmail_tool")
        elif choice == "3":
            print("Opening Gmail UI - use option 5x to create tasks from emails")
            subprocess.run(["just", "ui"], cwd="gmail_tool")
        elif choice == "4":
            self.add_calendar_event()
        elif choice == "5":
            self.get_email_summary(max_results=25)
        elif choice == "6":
            query = input("Search notes for: ")
            self.search_notes(query)
            
    def add_calendar_event(self):
        """Quick add calendar event"""
        date_str = input("Date (YYYYMMDD or 'today'): ")
        if date_str.lower() == 'today':
            date_str = datetime.now().strftime("%Y%m%d")
            
        time_str = input("Time (HH:MM): ")
        event_text = input("Event description: ")
        
        calendar_path = f"../Calendar/{date_str}.txt"
        line = f"{time_str} {event_text}"
        
        # Add to calendar note
        if Path(calendar_path).exists():
            content = self.noteplan_reader.read_note(calendar_path)
            lines = content.split('\n')
            
            # Insert in time order
            inserted = False
            for i, existing_line in enumerate(lines):
                if existing_line.startswith(time_str):
                    lines.insert(i + 1, line)
                    inserted = True
                    break
                elif any(existing_line.startswith(f"{h:02d}:") for h in range(24)):
                    existing_time = existing_line.split()[0]
                    if existing_time > time_str:
                        lines.insert(i, line)
                        inserted = True
                        break
                        
            if not inserted:
                lines.append(line)
                
            self.noteplan_editor.update_note(calendar_path, '\n'.join(lines))
        else:
            self.noteplan_editor.create_note(calendar_path, line)
            
        print(f"✅ Event added: {time_str} {event_text}")
        
    def search_notes(self, query):
        """Search through all notes"""
        notes_dir = Path("../Notes")
        results = []
        
        for note_path in notes_dir.rglob("*.txt"):
            try:
                content = note_path.read_text()
                if query.lower() in content.lower():
                    # Get context around match
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if query.lower() in line.lower():
                            results.append({
                                'file': note_path.name,
                                'path': str(note_path),
                                'line': line.strip(),
                                'context': lines[max(0, i-1):min(len(lines), i+2)]
                            })
            except:
                pass
                
        print(f"\n🔍 SEARCH RESULTS for '{query}'")
        print("-" * 60)
        
        if results:
            for i, result in enumerate(results[:10], 1):
                print(f"\n{i}. {result['file']}")
                print(f"   {result['line']}")
        else:
            print("No results found")

def main():
    """PA Dashboard"""
    pa = PersonalAssistant()
    
    print("🤖 PERSONAL ASSISTANT DASHBOARD")
    print("=" * 60)
    
    # Show summary
    pa.get_email_summary(max_results=5)
    pa.get_today_schedule()
    pa.get_upcoming_events(days=3)
    
    # Show actions
    while True:
        pa.quick_actions_menu()
        
        another = input("\nAnother action? (y/n): ")
        if another.lower() != 'y':
            break
            
    print("\n👋 PA signing off!")

if __name__ == "__main__":
    main()