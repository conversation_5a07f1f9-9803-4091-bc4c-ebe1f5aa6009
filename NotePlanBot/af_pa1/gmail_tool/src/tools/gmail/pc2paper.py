"""PC2Paper letter sending functionality.

Send physical <NAME_EMAIL> service.
"""
from typing import Optional
from .service import gmail_service


def send_section21_notice(
    recipient_name: str,
    recipient_address: str,
    cover_letter_body: str,
    attachment_path: str,
    cc_email: Optional[str] = None
) -> Optional[str]:
    """Send Section 21 notice via PC2Paper service.
    
    Args:
        recipient_name: Name of the recipient
        recipient_address: Full postal address (will be formatted for subject line)
        cover_letter_body: The body of the cover letter
        attachment_path: Path to the PDF attachment (Form 6A)
        cc_email: Optional CC email address
    
    Returns:
        Message ID if successful, None if failed
    """
    # Format address for PC2Paper subject line
    # Address must be on subject line with each line separated by commas
    # and must have the country at the end
    address_parts = [part.strip() for part in recipient_address.split(',')]
    
    # Ensure United Kingdom is at the end if not already present
    if not any('united kingdom' in part.lower() or 'uk' in part.lower() for part in address_parts):
        address_parts.append('United Kingdom')
    
    # Build subject line: Name, Address Line 1, Address Line 2, ..., Country
    subject_parts = [recipient_name] + address_parts
    subject = ', '.join(subject_parts)
    
    # Send the email with attachment
    return gmail_service.send_message_with_attachment(
        to='<EMAIL>',
        subject=subject,
        body=cover_letter_body,
        attachment_paths=[attachment_path],
        cc=cc_email
    )


def send_pc2paper_letter(
    recipient_name: str,
    recipient_address: str,
    letter_body: str,
    attachment_paths: list[str] = None,
    cc_email: Optional[str] = None
) -> Optional[str]:
    """Send a general letter via PC2Paper service.
    
    Args:
        recipient_name: Name of the recipient
        recipient_address: Full postal address (will be formatted for subject line)
        letter_body: The body of the letter
        attachment_paths: List of PDF attachment paths (optional)
        cc_email: Optional CC email address
    
    Returns:
        Message ID if successful, None if failed
    """
    # Format address for PC2Paper subject line
    address_parts = [part.strip() for part in recipient_address.split(',')]
    
    # Ensure United Kingdom is at the end if not already present
    if not any('united kingdom' in part.lower() or 'uk' in part.lower() for part in address_parts):
        address_parts.append('United Kingdom')
    
    # Build subject line: Name, Address Line 1, Address Line 2, ..., Country
    subject_parts = [recipient_name] + address_parts
    subject = ', '.join(subject_parts)
    
    # Send the email with attachments if provided
    if attachment_paths:
        return gmail_service.send_message_with_attachment(
            to='<EMAIL>',
            subject=subject,
            body=letter_body,
            attachment_paths=attachment_paths,
            cc=cc_email
        )
    else:
        # Send without attachments
        return gmail_service.send_message(
            to='<EMAIL>',
            subject=subject,
            body=letter_body
        )