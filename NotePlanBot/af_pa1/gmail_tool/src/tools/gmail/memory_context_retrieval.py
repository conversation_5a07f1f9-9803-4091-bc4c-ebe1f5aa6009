"""Hierarchical LLM context retrieval system.

1. Two-tier retrieval
2. Parallel processing
3. Intelligent ranking
v1
"""
import asyncio
import os
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from anthropic import AsyncAnthropic
from openai import AsyncOpenAI
from dotenv import load_dotenv
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from .storage.project_store import ProjectContextStore, DocumentContext
from .storage.context import context_manager


load_dotenv()
console = Console()


@dataclass
class RetrievedContext:
    """Retrieved context with relevance.
    
    1. Source information
    2. Content
    3. Relevance score
    v1
    """
    source_type: str  # project, document, email, person
    source_id: str
    content: str
    relevance_score: float
    metadata: Dict


class HierarchicalContextRetriever:
    """Two-tier hierarchical context retrieval.
    
    1. Tier 1: Parallel relevance checking
    2. Tier 2: Intelligent ranking
    3. Summarization
    v1
    """
    
    def __init__(self):
        """Initialize retriever.
        
        1. Set up clients
        2. Load stores
        v1
        """
        self.project_store = ProjectContextStore()
        self.email_context = context_manager
        self.claude = AsyncAnthropic()
        self.openai = AsyncOpenAI()
    
    async def check_project_relevance(self, query: str, project_name: str) -> Optional[RetrievedContext]:
        """Check if project is relevant to query.
        
        1. Get project context
        2. Check relevance
        3. Return if relevant
        v1
        """
        project_ctx = self.project_store.get_project_context(project_name)
        if not project_ctx:
            return None
        
        project = project_ctx['project']
        
        # Build context string
        context_parts = [
            f"Project: {project.name}",
            f"Description: {project.description}",
            f"Status: {project.status}",
            f"Priority: {project.priority}/5"
        ]
        
        # Add RACI info
        if project_ctx['raci']:
            raci_info = []
            for entry in project_ctx['raci'][:5]:
                raci_info.append(f"{entry.person_name} ({entry.role})")
            context_parts.append(f"Team: {', '.join(raci_info)}")
        
        # Add document summaries
        if project_ctx['documents']:
            context_parts.append("\nRelated Documents:")
            for doc in project_ctx['documents'][:3]:
                context_parts.append(f"- {doc.doc_type}: {doc.summary[:100]}...")
        
        full_context = "\n".join(context_parts)
        
        # Check relevance using OpenAI (cheaper for filtering)
        prompt = f"""Is this project context relevant to the query?

Query: {query}

Project Context:
{full_context}

Answer with: RELEVANT or NOT_RELEVANT followed by a confidence score (0-1).
Example: RELEVANT 0.8"""

        try:
            response = await self.openai.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
                max_tokens=20
            )
            
            result = response.choices[0].message.content.strip()
            if "RELEVANT" in result and "NOT_RELEVANT" not in result:
                score = float(result.split()[-1]) if len(result.split()) > 1 else 0.5
                
                return RetrievedContext(
                    source_type="project",
                    source_id=project_name,
                    content=full_context,
                    relevance_score=score,
                    metadata={
                        "priority": project.priority,
                        "status": project.status,
                        "stakeholders": len(project.key_stakeholders)
                    }
                )
        except Exception as e:
            console.print(f"[red]Error checking project {project_name}: {e}[/red]")
        
        return None
    
    async def check_document_relevance(self, query: str, document: DocumentContext) -> Optional[RetrievedContext]:
        """Check if document is relevant to query.
        
        1. Build document context
        2. Check relevance
        3. Return if relevant
        v1
        """
        # Build context
        context_parts = [
            f"Document: {document.filepath}",
            f"Type: {document.doc_type}",
            f"Project: {document.project_name}",
            f"Summary: {document.summary}",
            f"Key Points: {' | '.join(document.key_points[:3])}"
        ]
        
        full_context = "\n".join(context_parts)
        
        # Quick relevance check
        query_lower = query.lower()
        quick_match = (
            query_lower in document.summary.lower() or
            any(query_lower in point.lower() for point in document.key_points)
        )
        
        if not quick_match:
            # Do LLM check only if no quick match
            prompt = f"""Is this document relevant to the query?

Query: {query}

Document:
{full_context}

Answer with: RELEVANT or NOT_RELEVANT followed by a confidence score (0-1)."""

            try:
                response = await self.openai.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0,
                    max_tokens=20
                )
                
                result = response.choices[0].message.content.strip()
                if "NOT_RELEVANT" in result:
                    return None
                
                score = float(result.split()[-1]) if len(result.split()) > 1 else 0.5
            except:
                score = 0.3
        else:
            score = 0.8  # High score for direct matches
        
        return RetrievedContext(
            source_type="document",
            source_id=document.filepath,
            content=full_context,
            relevance_score=score * document.relevance_score,  # Combine with doc's own score
            metadata={
                "doc_type": document.doc_type,
                "project": document.project_name,
                "last_updated": document.last_updated
            }
        )
    
    async def tier1_parallel_retrieval(self, query: str) -> List[RetrievedContext]:
        """Tier 1: Check all sources in parallel.
        
        1. Check projects
        2. Check documents
        3. Return all relevant
        v1
        """
        tasks = []
        
        # Check all projects
        for project_name in self.project_store.projects.keys():
            tasks.append(self.check_project_relevance(query, project_name))
        
        # Check all documents
        for document in self.project_store.documents:
            tasks.append(self.check_document_relevance(query, document))
        
        # Run all checks in parallel
        with console.status("[cyan]Scanning context sources...[/cyan]"):
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out None and exceptions
        relevant_contexts = []
        for result in results:
            if isinstance(result, RetrievedContext):
                relevant_contexts.append(result)
        
        return relevant_contexts
    
    async def tier2_intelligent_ranking(self, query: str, contexts: List[RetrievedContext]) -> List[RetrievedContext]:
        """Tier 2: Rank contexts using Claude.
        
        1. Build ranking prompt
        2. Use Claude to rank
        3. Return ordered list
        v1
        """
        if len(contexts) <= 1:
            return contexts
        
        # Build context descriptions
        context_descriptions = []
        for i, ctx in enumerate(contexts):
            desc = f"""Context {i+1} ({ctx.source_type}):
{ctx.content[:200]}...
Initial relevance: {ctx.relevance_score:.2f}"""
            context_descriptions.append(desc)
        
        prompt = f"""Rank these contexts by relevance to the query. Consider:
1. Direct topic relevance
2. Specificity and detail level
3. Recency and current status
4. Practical usefulness

Query: {query}

Contexts:
{chr(10).join(context_descriptions)}

Rank the contexts from most to least relevant. Output ONLY the context numbers in order, separated by commas.
Example: 3,1,2,4"""

        try:
            response = await self.claude.messages.create(
                model="claude-3-5-sonnet-20250514",
                max_tokens=100,
                temperature=0,
                messages=[{"role": "user", "content": prompt}]
            )
            
            # Parse ranking
            ranking_str = response.content[0].text.strip()
            rankings = [int(x.strip()) - 1 for x in ranking_str.split(',') if x.strip().isdigit()]
            
            # Reorder contexts
            ranked_contexts = []
            for idx in rankings:
                if 0 <= idx < len(contexts):
                    ranked_contexts.append(contexts[idx])
            
            # Add any missed contexts at the end
            for ctx in contexts:
                if ctx not in ranked_contexts:
                    ranked_contexts.append(ctx)
            
            return ranked_contexts
            
        except Exception as e:
            console.print(f"[red]Error ranking contexts: {e}[/red]")
            # Fall back to score-based sorting
            return sorted(contexts, key=lambda x: x.relevance_score, reverse=True)
    
    async def retrieve_context(self, query: str, max_results: int = 5) -> List[RetrievedContext]:
        """Main retrieval method.
        
        1. Tier 1: Parallel retrieval
        2. Tier 2: Intelligent ranking
        3. Return top results
        v1
        """
        # Tier 1: Get all relevant contexts
        relevant_contexts = await self.tier1_parallel_retrieval(query)
        
        if not relevant_contexts:
            return []
        
        console.print(f"[green]Found {len(relevant_contexts)} relevant contexts[/green]")
        
        # Tier 2: Rank contexts
        if len(relevant_contexts) > 1:
            with console.status("[cyan]Ranking contexts...[/cyan]"):
                ranked_contexts = await self.tier2_intelligent_ranking(query, relevant_contexts)
        else:
            ranked_contexts = relevant_contexts
        
        # Return top results
        return ranked_contexts[:max_results]
    
    async def summarize_contexts(self, query: str, contexts: List[RetrievedContext]) -> str:
        """Summarize retrieved contexts.
        
        1. Combine contexts
        2. Generate summary
        3. Include missing info
        v1
        """
        if not contexts:
            return "No relevant context found."
        
        # Build combined context
        combined_parts = []
        for i, ctx in enumerate(contexts):
            combined_parts.append(f"[{ctx.source_type.upper()}] {ctx.content}")
        
        combined_context = "\n\n".join(combined_parts)
        
        prompt = f"""Summarize the relevant context for this query.

Query: {query}

Retrieved Context:
{combined_context}

Provide:
1. A concise summary of the most relevant information
2. Key points that directly address the query
3. Any important connections between the contexts
4. What additional context might be helpful (if any)

Keep the summary focused and practical."""

        try:
            response = await self.claude.messages.create(
                model="claude-3-5-sonnet-20250514",
                max_tokens=500,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            console.print(f"[red]Error summarizing contexts: {e}[/red]")
            return "Error generating summary."


async def main():
    """Test context retrieval.
    
    1. Create retriever
    2. Test queries
    3. Display results
    v1
    """
    retriever = HierarchicalContextRetriever()
    
    # Test queries
    test_queries = [
        "What is the Safety System project about?",
        "Who is working on LLM features?",
        "What are the recent design documents?"
    ]
    
    for query in test_queries:
        console.print(f"\n[bold cyan]Query: {query}[/bold cyan]")
        
        # Retrieve context
        contexts = await retriever.retrieve_context(query, max_results=3)
        
        if contexts:
            console.print(f"\n[green]Top {len(contexts)} relevant contexts:[/green]")
            for i, ctx in enumerate(contexts):
                console.print(f"\n{i+1}. [{ctx.source_type}] {ctx.source_id}")
                console.print(f"   Relevance: {ctx.relevance_score:.2f}")
                console.print(f"   Preview: {ctx.content[:100]}...")
            
            # Get summary
            console.print("\n[yellow]Generating summary...[/yellow]")
            summary = await retriever.summarize_contexts(query, contexts)
            console.print(f"\n[cyan]Summary:[/cyan]\n{summary}")
        else:
            console.print("[yellow]No relevant context found.[/yellow]")
        
        console.print("\n" + "-" * 80)


if __name__ == '__main__':
    asyncio.run(main())