"""Handle email reply workflow.

1. Process unread emails
2. Generate and review drafts
3. Send replies
v1
"""
from typing import List, Dict, Optional
from ..service import gmail_service
from ..gmail_send_email import draft_assistant
from ..storage.context import context_manager


def process_unread_emails(max_emails: int = 10) -> List[Dict]:
    """Process unread emails and generate drafts.
    
    1. Get unread messages
    2. Generate drafts
    3. Return results
    v1
    """
    results = []
    
    # Get unread messages
    messages = gmail_service.list_unread_messages(max_results=max_emails)
    
    for msg in messages:
        # Get thread context
        thread_context = context_manager.get_thread_context(msg['thread_id'])
        
        # Generate draft and context
        draft, context = draft_assistant.analyze_and_draft(
            msg,
            thread_context=thread_context
        )
        
        results.append({
            'message': msg,
            'draft': draft,
            'context': context
        })
    
    return results


def send_reply(
    message: Dict,
    draft: str,
    mark_read: bool = True
) -> Optional[str]:
    """Send a reply to an email.
    
    1. Format reply
    2. Send message
    3. Update status
    v1
    """
    # Send the reply
    message_id = gmail_service.send_message(
        to=message['from'],
        subject=message['subject'],
        body=draft,
        reply_to=message
    )
    
    # Mark original as read if requested
    if mark_read and message_id:
        gmail_service.mark_as_read(message['id'])
    
    return message_id


def process_and_reply(
    max_emails: int = 10,
    auto_send: bool = False
) -> List[Dict]:
    """Process emails and optionally send replies.
    
    1. Process unread emails
    2. Generate drafts
    3. Send if auto_send
    v1
    """
    results = process_unread_emails(max_emails=max_emails)
    
    if auto_send:
        for result in results:
            message_id = send_reply(
                message=result['message'],
                draft=result['draft']
            )
            result['sent_message_id'] = message_id
    
    return results 