"""Main entry point for Gmail operations.

1. Handle email analysis
2. <PERSON><PERSON> replies
3. Track threads
v2
"""
from typing import List, Dict
from datetime import datetime
from rich.console import Console
import colorama
from .service import GmailService
from .storage.alex_context import alex_context
from .storage.context import context_manager
from .message_types import determine_recipient_type, RecipientType


console = Console()
gmail_service = GmailService()


def format_days_ago(date_str: str) -> str:
    """Format date as days ago.
    
    1. Parse date string
    2. Calculate days
    3. Format output
    v1
    """
    try:
        date = datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %z')
        days = (datetime.now(date.tzinfo) - date).days
        return f"{days}d ago"
    except ValueError as e:
        return date_str


def colorize_name(name: str, is_alex: bool = False) -> str:
    """Colorize participant name.
    
    1. Check if Alex
    2. Apply color
    3. Reset formatting
    v1
    """
    if is_alex:
        return f"{colorama.Fore.GREEN}{name}{colorama.Style.RESET_ALL}"
    return f"{colorama.Fore.YELLOW}{name}{colorama.Style.RESET_ALL}"


def analyze_inbox(
    max_messages: int = 10,
    days_back: int = 7,
    show_calendar: bool = False
) -> List[Dict]:
    """Analyze inbox messages.
    
    1. Get unread messages
    2. Analyze content
    3. Generate report
    v2
    """
    console.print("\n[cyan]Analyzing Gmail Inbox[/cyan]")
    console.print("=" * 50)
    
    # Get messages
    console.print("\nFetching recent messages...")
    messages = gmail_service.list_unread_messages(
        max_results=max_messages,
        days_back=days_back
    )
    
    if not messages:
        console.print("[yellow]No unread messages found.[/yellow]")
        return []
    
    # Show messages by project
    console.print("\n[cyan]MESSAGES BY PROJECT[/cyan]")
    console.print("=" * 50)
    
    # Group by project
    by_project = {}
    for msg in messages:
        # Get project context if available
        project = "Uncategorized"  # Default project
        thread_id = msg['thread_id']
        thread_context = context_manager.get_thread_context(thread_id)
        if thread_context and thread_context.project:
            project = thread_context.project
        
        if project not in by_project:
            by_project[project] = []
        by_project[project].append(msg)
    
    # Show project summaries
    for project, items in by_project.items():
        # Get project context if available
        project_ctx = alex_context.get_project_context(project)
        priority = project_ctx.priority if project_ctx else 0
        
        console.print(f"\n[magenta]{project}[/magenta] (Priority: {priority})")
        for msg in sorted(items, key=lambda x: x['date'], reverse=True):
            # Show labels
            label_str = ' '.join(msg['labels']) if msg['labels'] else ''
            
            # Determine if Alex is TO or CC
            recipient_type = determine_recipient_type(
                {'to': msg['to'], 'cc': msg.get('cc', [])}, 
                alex_context.email
            )
            recipient_indicator = {
                RecipientType.TO: "→",
                RecipientType.CC: "•",
                RecipientType.NONE: " "
            }[recipient_type]
            
            # Format sender name
            sender_name = msg['from'].split('<')[0].strip()
            is_alex = alex_context.email in msg['from']
            colored_sender = colorize_name(sender_name, is_alex)
            
            console.print(
                f"\n[{format_days_ago(msg['date'])}] "
                f"{recipient_indicator} "
                f"{label_str}\n"
                f"From: {colored_sender}\n"
                f"Subject: {msg['subject']}"
            )
            
            # Show thread preview if available
            thread_messages = gmail_service.get_thread_messages(msg['thread_id'])
            if thread_messages:
                console.print("\nThread Preview:")
                for thread_msg in thread_messages[-2:]:  # Show last 2 messages
                    sender = thread_msg['from'].split('<')[0].strip()
                    is_alex_msg = alex_context.email in thread_msg['from']
                    colored_thread_sender = colorize_name(sender, is_alex_msg)
                    
                    # Clean and truncate content
                    content = thread_msg['content']
                    if content.startswith('<!DOCTYPE'):
                        content = "HTML message..."
                    else:
                        content = content.replace('\n', ' ').strip()[:100]
                    
                    console.print(f"  {colored_thread_sender}: {content}...")
    
    return messages


def main():
    """Main entry point.
    
    1. Run analysis
    2. Show report
    3. Handle user input
    v2
    """
    try:
        analyses = analyze_inbox(max_messages=10, days_back=7)
        
        if analyses:
            console.print("\n[cyan]Actions:[/cyan]")
            console.print("1. Review and send replies")
            console.print("2. Mark all as read")
            console.print("3. Exit")
            
            choice = input("\nEnter choice (1-3): ")
            if choice == '1':
                console.print("\n[yellow]Reply sending not implemented yet[/yellow]")
            elif choice == '2':
                console.print("\n[yellow]Mark as read not implemented yet[/yellow]")
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")


if __name__ == '__main__':
    main() 