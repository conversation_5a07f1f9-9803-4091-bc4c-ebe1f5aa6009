"""Snooze management for Gmail UI.

1. Track snoozed emails
2. Time-based filtering
3. Persist to JSON
v1
"""
import json
from pathlib import Path
from typing import List, Set
from datetime import datetime
from .models import SnoozedEmail


class SnoozeManager:
    """Manage snoozed emails.
    
    1. Load/save queue
    2. Add/remove snoozes
    3. Check expiry
    v1
    """
    
    def __init__(self, filepath: str = "gmail_snooze_queue.json"):
        """Initialize snooze manager.
        
        1. Set filepath
        2. Load queue
        v1
        """
        self.filepath = Path(filepath)
        self.snoozed: List[SnoozedEmail] = []
        self.load()
    
    def load(self) -> None:
        """Load snooze queue from JSON.
        
        1. Read file if exists
        2. Parse entries
        v1
        """
        if self.filepath.exists():
            with open(self.filepath, 'r') as f:
                data = json.load(f)
                self.snoozed = [SnoozedEmail(**entry) for entry in data]
    
    def save(self) -> None:
        """Save snooze queue to JSON.
        
        1. Convert to dict
        2. Write JSON
        v1
        """
        with open(self.filepath, 'w') as f:
            json.dump([entry.model_dump() for entry in self.snoozed], f, indent=2, default=str)
    
    def snooze_email(
        self,
        thread_id: str,
        message_id: str,
        until: datetime,
        position: int
    ) -> None:
        """Add email to snooze queue.
        
        1. Create entry
        2. Add to queue
        3. Save
        v1
        """
        # Remove existing snooze for this thread
        self.snoozed = [s for s in self.snoozed if s.thread_id != thread_id]
        
        # Add new snooze
        entry = SnoozedEmail(
            thread_id=thread_id,
            message_id=message_id,
            snooze_until=until,
            original_position=position
        )
        self.snoozed.append(entry)
        self.save()
    
    def unsnooze_email(self, thread_id: str) -> bool:
        """Remove email from snooze queue.
        
        1. Find entry
        2. Remove if found
        3. Save
        v1
        """
        original_len = len(self.snoozed)
        self.snoozed = [s for s in self.snoozed if s.thread_id != thread_id]
        
        if len(self.snoozed) < original_len:
            self.save()
            return True
        return False
    
    def get_snoozed_thread_ids(self) -> Set[str]:
        """Get currently snoozed thread IDs.
        
        1. Check expiry
        2. Return active snoozes
        v1
        """
        now = datetime.now()
        active_snoozes = set()
        expired = []
        
        for entry in self.snoozed:
            if entry.snooze_until > now:
                active_snoozes.add(entry.thread_id)
            else:
                expired.append(entry.thread_id)
        
        # Remove expired entries
        if expired:
            self.snoozed = [s for s in self.snoozed if s.thread_id not in expired]
            self.save()
        
        return active_snoozes
    
    def is_snoozed(self, thread_id: str) -> bool:
        """Check if thread is snoozed.
        
        1. Get active snoozes
        2. Check membership
        v1
        """
        return thread_id in self.get_snoozed_thread_ids()