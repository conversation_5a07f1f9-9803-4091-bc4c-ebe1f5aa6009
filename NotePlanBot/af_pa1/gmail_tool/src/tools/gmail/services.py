"""Singleton service manager for Gmail operations.

1. Centralized service instantiation
2. Connection reuse
3. Error handling
v1
"""
from functools import lru_cache
from typing import Optional
from .service import GmailService
from .gmail_auth import gmail_auth


@lru_cache(maxsize=1)
def get_gmail_service() -> GmailService:
    """Get or create singleton Gmail service.
    
    1. Check cache
    2. Create if needed
    3. Return instance
    v1
    """
    return GmailService()


# Global instance for backward compatibility
gmail_service = get_gmail_service()