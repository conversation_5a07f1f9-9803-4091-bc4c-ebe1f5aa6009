"""Async utilities for Gmail tools.

1. LLM operation context manager
2. Error handling patterns
3. Status display helpers
v1
"""
from contextlib import asynccontextmanager
from typing import AsyncIterator, Optional
from .common import get_console


@asynccontextmanager
async def llm_operation(message: str, error_message: Optional[str] = None) -> AsyncIterator[None]:
    """Standard pattern for LLM operations with status.
    
    1. Show status
    2. Handle errors
    3. Clean up
    v1
    
    Usage:
        async with llm_operation("Analyzing emails..."):
            result = await some_llm_call()
    """
    console = get_console()
    
    with console.status(f"[cyan]{message}[/cyan]"):
        try:
            yield
        except Exception as e:
            error_msg = error_message or f"Error: {e}"
            console.print(f"[red]{error_msg}[/red]")
            raise


@asynccontextmanager  
async def batch_operation(operation_name: str, count: int) -> AsyncIterator[None]:
    """Context manager for batch operations.
    
    1. Show progress
    2. Handle batches
    3. Report completion
    v1
    """
    console = get_console()
    
    console.print(f"[cyan]Starting {operation_name} for {count} items...[/cyan]")
    try:
        yield
        console.print(f"[green]Completed {operation_name}[/green]")
    except Exception as e:
        console.print(f"[red]Failed {operation_name}: {e}[/red]")
        raise