"""Task management for Gmail UI.

1. Create tasks from emails
2. Persist to JSON
3. Basic CRU operations
4. LLM-powered task title generation
v2
"""
import json
import uuid
import os
from pathlib import Path
from typing import List, Optional
from datetime import datetime
from .models import Task, TaskStatus
import anthropic
from dotenv import load_dotenv

load_dotenv()


class TaskManager:
    """Manage tasks created from emails.
    
    1. Load/save tasks
    2. CRUD operations
    3. Query methods
    v1
    """
    
    def __init__(self, filepath: str = "gmail_tasks.json"):
        """Initialize task manager.
        
        1. Set filepath
        2. Load existing tasks
        v1
        """
        self.filepath = Path(filepath)
        self.tasks: List[Task] = []
        self.load()
    
    def load(self) -> None:
        """Load tasks from JSON file.
        
        1. Read file if exists
        2. Parse tasks
        v1
        """
        if self.filepath.exists():
            with open(self.filepath, 'r') as f:
                data = json.load(f)
                self.tasks = [Task(**task_data) for task_data in data]
    
    def save(self) -> None:
        """Save tasks to JSON file.
        
        1. Convert to dict
        2. Write JSON
        v1
        """
        with open(self.filepath, 'w') as f:
            json.dump([task.model_dump() for task in self.tasks], f, indent=2, default=str)
    
    def create_task(
        self,
        title: str,
        description: str,
        source_thread_id: str,
        deadline: Optional[datetime] = None
    ) -> Task:
        """Create new task from email.
        
        1. Generate ID
        2. Create task
        3. Save
        v1
        """
        task = Task(
            id=str(uuid.uuid4())[:8],
            title=title,
            description=description,
            deadline=deadline,
            source_thread_id=source_thread_id
        )
        self.tasks.append(task)
        self.save()
        return task
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID.
        
        1. Find task
        2. Return if found
        v1
        """
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None
    
    def update_task_status(self, task_id: str, status: TaskStatus) -> bool:
        """Update task status.
        
        1. Find task
        2. Update status
        3. Save
        v1
        """
        task = self.get_task(task_id)
        if task:
            task.status = status
            self.save()
            return True
        return False
    
    def get_active_tasks(self) -> List[Task]:
        """Get non-completed tasks.
        
        1. Filter by status
        2. Sort by deadline
        v1
        """
        active = [
            task for task in self.tasks
            if task.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]
        ]
        # Sort by deadline (None last)
        active.sort(key=lambda t: (t.deadline is None, t.deadline))
        return active
    
    def get_tasks_by_thread(self, thread_id: str) -> List[Task]:
        """Get tasks for a thread.
        
        1. Filter by thread
        2. Return list
        v1
        """
        return [
            task for task in self.tasks
            if task.source_thread_id == thread_id
        ]
    
    def generate_task_title(self, email_subject: str, email_content: str = "") -> str:
        """Generate task title using Claude Haiku.
        
        1. Call Haiku for suggestions
        2. Return concise title
        v1
        """
        client = anthropic.Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        
        prompt = f"""Given this email subject and content, generate a concise action-oriented task title (max 50 chars).
Focus on the specific action needed, not just restating the subject.

Email subject: {email_subject}
Email preview: {email_content[:200]}

Return only the task title, nothing else."""
        
        try:
            response = client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=100,
                temperature=0.3,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            suggested_title = response.content[0].text.strip()
            # Ensure max 50 chars
            if len(suggested_title) > 50:
                suggested_title = suggested_title[:47] + "..."
            
            return suggested_title
            
        except Exception as e:
            print(f"Error generating title: {e}")
            # Fallback to truncated subject
            return email_subject[:50] if len(email_subject) > 50 else email_subject