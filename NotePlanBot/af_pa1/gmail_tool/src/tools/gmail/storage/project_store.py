"""Project context store using Excel format.

1. Store project metadata
2. Manage RACI matrices  
3. Track document contexts
v1
"""
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import os
from pathlib import Path


@dataclass
class ProjectInfo:
    """Core project information.
    
    1. Basic metadata
    2. Priority and status
    3. Key contacts
    v1
    """
    name: str
    description: str
    priority: int  # 1-5
    status: str  # active, pending, completed
    start_date: str
    end_date: Optional[str]
    key_stakeholders: List[str]
    primary_contact: str
    tags: List[str]


@dataclass
class RACIEntry:
    """RACI matrix entry.
    
    1. Project and person
    2. Role assignment
    3. Notes
    v1
    """
    project_name: str
    person_email: str
    person_name: str
    role: str  # R, A, C, I
    notes: Optional[str] = None


@dataclass
class DocumentContext:
    """Document context for projects.
    
    1. Document metadata
    2. LLM summaries
    3. Relevance info
    v1
    """
    project_name: str
    filepath: str
    doc_type: str  # spec, design, notes, email, meeting
    summary: str
    key_points: List[str]
    last_updated: str
    relevance_score: float  # 0-1


@dataclass
class PersonContext:
    """Person-specific context.
    
    1. Contact info
    2. Communication preferences
    3. Interaction history
    v1
    """
    email: str
    name: str
    role: str
    timezone: str
    preferred_contact: str  # email, slack, teams
    response_time_hours: int
    notes: str
    last_interaction: str


class ProjectContextStore:
    """Manage project contexts in Excel.
    
    1. Load/save Excel files
    2. Query project data
    3. Update contexts
    v1
    """
    
    def __init__(self, filepath: str = "project_contexts.xlsx"):
        """Initialize store.
        
        1. Set filepath
        2. Load existing data
        v1
        """
        self.filepath = Path(filepath)
        self.projects: Dict[str, ProjectInfo] = {}
        self.raci_matrix: List[RACIEntry] = []
        self.documents: List[DocumentContext] = []
        self.people: Dict[str, PersonContext] = {}
        
        if self.filepath.exists():
            self.load()
    
    def load(self) -> None:
        """Load data from Excel.
        
        1. Read all sheets
        2. Parse into objects
        3. Update memory
        v1
        """
        with pd.ExcelFile(self.filepath) as xls:
            # Load projects
            if 'Projects' in xls.sheet_names:
                df = pd.read_excel(xls, 'Projects')
                for _, row in df.iterrows():
                    stakeholders = row['key_stakeholders'].split(',') if pd.notna(row['key_stakeholders']) else []
                    tags = row['tags'].split(',') if pd.notna(row['tags']) else []
                    
                    project = ProjectInfo(
                        name=row['name'],
                        description=row['description'],
                        priority=row['priority'],
                        status=row['status'],
                        start_date=row['start_date'],
                        end_date=row['end_date'] if pd.notna(row['end_date']) else None,
                        key_stakeholders=[s.strip() for s in stakeholders],
                        primary_contact=row['primary_contact'],
                        tags=[t.strip() for t in tags]
                    )
                    self.projects[project.name] = project
            
            # Load RACI matrix
            if 'RACI' in xls.sheet_names:
                df = pd.read_excel(xls, 'RACI')
                for _, row in df.iterrows():
                    entry = RACIEntry(
                        project_name=row['project_name'],
                        person_email=row['person_email'],
                        person_name=row['person_name'],
                        role=row['role'],
                        notes=row['notes'] if pd.notna(row['notes']) else None
                    )
                    self.raci_matrix.append(entry)
            
            # Load documents
            if 'Documents' in xls.sheet_names:
                df = pd.read_excel(xls, 'Documents')
                for _, row in df.iterrows():
                    key_points = row['key_points'].split('|') if pd.notna(row['key_points']) else []
                    
                    doc = DocumentContext(
                        project_name=row['project_name'],
                        filepath=row['filepath'],
                        doc_type=row['doc_type'],
                        summary=row['summary'],
                        key_points=[p.strip() for p in key_points],
                        last_updated=row['last_updated'],
                        relevance_score=row['relevance_score']
                    )
                    self.documents.append(doc)
            
            # Load people
            if 'People' in xls.sheet_names:
                df = pd.read_excel(xls, 'People')
                for _, row in df.iterrows():
                    person = PersonContext(
                        email=row['email'],
                        name=row['name'],
                        role=row['role'],
                        timezone=row['timezone'],
                        preferred_contact=row['preferred_contact'],
                        response_time_hours=row['response_time_hours'],
                        notes=row['notes'],
                        last_interaction=row['last_interaction']
                    )
                    self.people[person.email] = person
    
    def save(self) -> None:
        """Save data to Excel.
        
        1. Convert to DataFrames
        2. Write sheets
        3. Save file
        v1
        """
        with pd.ExcelWriter(self.filepath, engine='openpyxl') as writer:
            # Save projects
            if self.projects:
                projects_data = []
                for project in self.projects.values():
                    data = asdict(project)
                    data['key_stakeholders'] = ', '.join(project.key_stakeholders)
                    data['tags'] = ', '.join(project.tags)
                    projects_data.append(data)
                
                df = pd.DataFrame(projects_data)
                df.to_excel(writer, sheet_name='Projects', index=False)
            
            # Save RACI matrix
            if self.raci_matrix:
                raci_data = [asdict(entry) for entry in self.raci_matrix]
                df = pd.DataFrame(raci_data)
                df.to_excel(writer, sheet_name='RACI', index=False)
            
            # Save documents
            if self.documents:
                docs_data = []
                for doc in self.documents:
                    data = asdict(doc)
                    data['key_points'] = ' | '.join(doc.key_points)
                    docs_data.append(data)
                
                df = pd.DataFrame(docs_data)
                df.to_excel(writer, sheet_name='Documents', index=False)
            
            # Save people
            if self.people:
                people_data = [asdict(person) for person in self.people.values()]
                df = pd.DataFrame(people_data)
                df.to_excel(writer, sheet_name='People', index=False)
    
    def add_project(self, project: ProjectInfo) -> None:
        """Add or update project.
        
        1. Store project
        2. Auto-save
        v1
        """
        self.projects[project.name] = project
        self.save()
    
    def add_raci_entry(self, entry: RACIEntry) -> None:
        """Add RACI matrix entry.
        
        1. Check duplicates
        2. Add entry
        3. Save
        v1
        """
        # Remove existing entry if exists
        self.raci_matrix = [
            e for e in self.raci_matrix 
            if not (e.project_name == entry.project_name and 
                   e.person_email == entry.person_email)
        ]
        self.raci_matrix.append(entry)
        self.save()
    
    def add_document(self, doc: DocumentContext) -> None:
        """Add document context.
        
        1. Add to list
        2. Save
        v1
        """
        self.documents.append(doc)
        self.save()
    
    def add_person(self, person: PersonContext) -> None:
        """Add or update person.
        
        1. Store person
        2. Save
        v1
        """
        self.people[person.email] = person
        self.save()
    
    def get_project_context(self, project_name: str) -> Dict[str, Any]:
        """Get all context for a project.
        
        1. Get project info
        2. Get RACI entries
        3. Get documents
        v1
        """
        project = self.projects.get(project_name)
        if not project:
            return None
        
        # Get RACI entries
        raci_entries = [e for e in self.raci_matrix if e.project_name == project_name]
        
        # Get documents
        documents = [d for d in self.documents if d.project_name == project_name]
        
        # Get people involved
        people_emails = set([e.person_email for e in raci_entries])
        people = {email: self.people.get(email) for email in people_emails if email in self.people}
        
        return {
            'project': project,
            'raci': raci_entries,
            'documents': documents,
            'people': people
        }
    
    def get_person_projects(self, email: str) -> List[str]:
        """Get all projects for a person.
        
        1. Find RACI entries
        2. Return project names
        v1
        """
        return list(set([
            e.project_name for e in self.raci_matrix 
            if e.person_email == email
        ]))
    
    def search_documents(self, query: str, project_name: Optional[str] = None) -> List[DocumentContext]:
        """Search document contexts.
        
        1. Filter by project if given
        2. Search in summaries
        3. Return matches
        v1
        """
        docs = self.documents
        if project_name:
            docs = [d for d in docs if d.project_name == project_name]
        
        query_lower = query.lower()
        matches = []
        
        for doc in docs:
            if (query_lower in doc.summary.lower() or
                any(query_lower in point.lower() for point in doc.key_points)):
                matches.append(doc)
        
        # Sort by relevance score
        matches.sort(key=lambda d: d.relevance_score, reverse=True)
        return matches


def main():
    """Test project context store.
    
    1. Create store
    2. Add test data
    3. Query and display
    v1
    """
    store = ProjectContextStore("test_project_contexts.xlsx")
    
    # Add test project
    project = ProjectInfo(
        name="Safety System",
        description="LLM safety guardrails and monitoring",
        priority=5,
        status="active",
        start_date="2024-01-01",
        end_date=None,
        key_stakeholders=["<EMAIL>", "<EMAIL>"],
        primary_contact="<EMAIL>",
        tags=["safety", "llm", "critical"]
    )
    store.add_project(project)
    
    # Add RACI entries
    store.add_raci_entry(RACIEntry(
        project_name="Safety System",
        person_email="<EMAIL>",
        person_name="Alex Foster",
        role="A",
        notes="Technical lead and accountable for delivery"
    ))
    
    # Add document
    store.add_document(DocumentContext(
        project_name="Safety System",
        filepath="docs/safety/design.md",
        doc_type="design",
        summary="System design for LLM safety features including content filtering and monitoring",
        key_points=["Content filtering", "Real-time monitoring", "Incident response"],
        last_updated="2024-01-15",
        relevance_score=0.9
    ))
    
    # Add person
    store.add_person(PersonContext(
        email="<EMAIL>",
        name="Alex Foster",
        role="Technical Lead",
        timezone="GMT",
        preferred_contact="email",
        response_time_hours=4,
        notes="Prefers detailed technical discussions",
        last_interaction="2024-01-20"
    ))
    
    # Query and display
    context = store.get_project_context("Safety System")
    print(f"Project: {context['project'].name}")
    print(f"RACI entries: {len(context['raci'])}")
    print(f"Documents: {len(context['documents'])}")
    print(f"People: {len(context['people'])}")


if __name__ == '__main__':
    main()