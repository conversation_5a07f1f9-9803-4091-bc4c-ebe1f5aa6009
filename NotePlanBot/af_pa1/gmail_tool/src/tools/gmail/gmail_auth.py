"""Gmail API authentication module.

1. Handle OAuth2 authentication
2. Manage token storage/refresh
3. Provide service creation
v1
"""
import os
import pickle
from typing import List, Optional
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build


class GmailAuth:
    """Handle Gmail API authentication.
    
    1. Manage credentials
    2. Handle token lifecycle
    3. Create API service
    v1
    """
    
    DEFAULT_SCOPES = [
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.modify'  # For marking as read/replied
    ]
    
    def __init__(
        self,
        token_path: str = 'gmail_token.pickle',
        creds_path: str = 'creds.json',
        scopes: Optional[List[str]] = None
    ):
        """Initialize auth manager.
        
        1. Set paths and scopes
        2. Initialize credentials
        v1
        """
        self.token_path = token_path
        self.creds_path = creds_path
        self.scopes = scopes or self.DEFAULT_SCOPES
        self._creds = None
    
    def get_service(self):
        """Get authenticated Gmail service.
        
        1. Check for existing token
        2. If no valid token, create new one
        3. Build and return service
        v1
        """
        if not self._creds or not self._creds.valid:
            self._refresh_credentials()
        
        return build('gmail', 'v1', credentials=self._creds)
    
    def _refresh_credentials(self):
        """Refresh or create new credentials.
        
        1. Try to load existing token
        2. Refresh if expired
        3. Create new if needed
        v1
        """
        # Try to load existing token
        if os.path.exists(self.token_path):
            with open(self.token_path, 'rb') as token:
                self._creds = pickle.load(token)
        
        # Handle credential refresh/creation
        if not self._creds or not self._creds.valid:
            if self._creds and self._creds.expired and self._creds.refresh_token:
                self._creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    self.creds_path,
                    self.scopes
                )
                self._creds = flow.run_local_server(port=0)
            
            # Save refreshed/new credentials
            with open(self.token_path, 'wb') as token:
                pickle.dump(self._creds, token)


# Global instance for easy access
gmail_auth = GmailAuth() 