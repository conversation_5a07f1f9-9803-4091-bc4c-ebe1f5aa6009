"""Data models for Gmail UI enhancements.

1. Task management
2. Snooze queue
3. Email grouping
v1
"""
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List, Dict
from enum import Enum


class TaskStatus(str, Enum):
    """Task status options."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Task(BaseModel):
    """Task created from email.
    
    1. Core task info
    2. Email reference
    3. Timestamps
    v1
    """
    id: str = Field(..., description="Unique task ID")
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Task description")
    deadline: Optional[datetime] = Field(None, description="Task deadline")
    source_thread_id: str = Field(..., description="Source email thread ID")
    created_at: datetime = Field(default_factory=datetime.now)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SnoozedEmail(BaseModel):
    """Snoozed email entry.
    
    1. Email reference
    2. Snooze timing
    3. Original position
    v1
    """
    thread_id: str = Field(..., description="Email thread ID")
    message_id: str = Field(..., description="Email message ID")
    snooze_until: datetime = Field(..., description="When to unsnooze")
    original_position: int = Field(..., description="Original position in list")
    snoozed_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class EmailGroup(BaseModel):
    """Email grouping result.
    
    1. Group metadata
    2. Email assignments
    3. LLM reasoning
    v1
    """
    groups: Dict[int, str] = Field(..., description="Group ID to name mapping")
    assignments: Dict[str, int] = Field(..., description="Thread ID to group ID")
    reasoning: str = Field(..., description="LLM reasoning for grouping")
    created_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }