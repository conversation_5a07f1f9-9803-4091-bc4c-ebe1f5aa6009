"""Shared console instance for Gmail tools.

1. Single console instance
2. Consistent styling
3. Reduced imports
v1
"""
from functools import lru_cache
from rich.console import Console


@lru_cache(maxsize=1)
def get_console() -> Console:
    """Get or create singleton console.
    
    1. Check cache
    2. Create if needed
    3. Return instance
    v1
    """
    return Console()


# Global instance for convenience
console = get_console()