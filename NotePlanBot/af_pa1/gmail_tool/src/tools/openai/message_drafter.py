"""OpenAI Assistant tool for drafting calendar-related messages.

1. Initialize OpenAI client and assistant
2. Handle message drafting requests
3. Process responses
v3
"""
import os
from typing import Optional
from dotenv import load_dotenv
from openai import OpenAI


# Load environment variables
load_dotenv()


class MessageDrafter:
    """Handle drafting messages using OpenAI Assistant.
    
    1. Initialize OpenAI client and assistant
    2. Draft messages for calendar events
    3. Handle response formatting
    v3
    """
    
    def __init__(self, api_key: Optional[str] = None, dry_run: bool = False):
        """Initialize OpenAI client and assistant.
        
        1. Set up OpenAI client if not dry run
        2. Initialize or get existing assistant
        3. Set dry run mode for testing
        v3
        """
        self.dry_run = dry_run
        if not dry_run:
            if not (api_key or os.getenv("OPENAI_API_KEY")):
                raise ValueError("OpenAI API key must be provided or set in OPENAI_API_KEY environment variable")
            self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
            self._setup_assistant()
    
    def _setup_assistant(self):
        """Set up or get existing OpenAI assistant.
        
        1. Check for existing assistant
        2. Create new if needed
        3. Store assistant ID
        v1
        """
        # Assistant configuration
        ASSISTANT_NAME = "Calendar Message Drafter"
        ASSISTANT_INSTRUCTIONS = """You are a helpful assistant that drafts messages about calendar events and availability.
        Your messages should be:
        1. Concise and clear
        2. Professional but friendly
        3. Appropriate for the context (formal for work meetings, casual for social events)
        """
        
        # Try to find existing assistant
        assistants = self.client.beta.assistants.list(
            order="desc",
            limit=100
        )
        
        for assistant in assistants.data:
            if assistant.name == ASSISTANT_NAME:
                self.assistant_id = assistant.id
                return
        
        # Create new assistant if not found
        assistant = self.client.beta.assistants.create(
            name=ASSISTANT_NAME,
            instructions=ASSISTANT_INSTRUCTIONS,
            model="gpt-4-1106-preview"
        )
        self.assistant_id = assistant.id

    def draft_meeting_message(self, event_details: dict) -> str:
        """Draft a message for a meeting or event.
        
        1. Format event details
        2. Send to assistant if not dry run
        3. Return drafted message
        v3
        """
        if self.dry_run:
            return f"[DRY RUN] Draft message for event: {event_details.get('summary', 'Untitled Event')}"
        
        # Create a thread with the event details
        thread = self.client.beta.threads.create(
            messages=[{
                "role": "user",
                "content": f"Please draft a message about this calendar event: {event_details}"
            }]
        )
        
        # Run the assistant
        run = self.client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=self.assistant_id
        )
        
        # Wait for completion and get messages
        while run.status in ["queued", "in_progress"]:
            run = self.client.beta.threads.runs.retrieve(
                thread_id=thread.id,
                run_id=run.id
            )
        
        messages = self.client.beta.threads.messages.list(thread_id=thread.id)
        return messages.data[0].content[0].text.value

    def draft_evening_plan(self, availability: dict) -> str:
        """Draft a message about evening availability.
        
        1. Format availability info
        2. Send to assistant if not dry run
        3. Return drafted message
        v3
        """
        if self.dry_run:
            return f"[DRY RUN] Draft message about evening plans with availability: {availability}"
        
        # Create a thread with the availability details
        thread = self.client.beta.threads.create(
            messages=[{
                "role": "user",
                "content": f"Please draft a message about my evening availability: {availability}"
            }]
        )
        
        # Run the assistant
        run = self.client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=self.assistant_id
        )
        
        # Wait for completion and get messages
        while run.status in ["queued", "in_progress"]:
            run = self.client.beta.threads.runs.retrieve(
                thread_id=thread.id,
                run_id=run.id
            )
        
        messages = self.client.beta.threads.messages.list(thread_id=thread.id)
        return messages.data[0].content[0].text.value


def create_default_drafter(dry_run: bool = False) -> MessageDrafter:
    """Create a default message drafter instance.
    
    1. Create drafter with default settings
    2. Return configured instance
    v2
    """
    return MessageDrafter(dry_run=dry_run) 