"""Main entry point for all tools.

1. Coordinate tools
2. Handle user input
3. Manage sessions
v2
"""
from rich.console import Console
from tools.gmail.analyze_threads import analyze_threads


console = Console()


def main() -> None:
    """Main entry point.
    
    1. Run Gmail analysis
    2. Handle input
    3. Run tools
    v2
    """
    try:
        analyze_threads(max_messages=10, days_back=7)
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled.[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")


if __name__ == '__main__':
    main() 