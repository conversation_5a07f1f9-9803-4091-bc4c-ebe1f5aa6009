from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime, timezone


@dataclass
class Event:
    """Calendar event data model.
    
    1. Store event metadata
    2. Handle datetime conversions
    3. Provide helper properties
    v1
    """
    # Basic info
    id: str
    title: str
    description: Optional[str] = None
    start_time: str = ''
    end_time: str = ''
    duration_minutes: Optional[int] = None
    
    # Status and flags
    status: str = 'confirmed'  # confirmed, tentative, cancelled
    transparency: str = 'opaque'  # opaque (busy) or transparent (free)
    visibility: str = 'default'  # default, public, private
    is_recurring: bool = False
    recurring_id: Optional[str] = None
    is_all_day: bool = False
    
    # Attendees and organizers
    creator_email: str = ''
    organizer_email: str = ''
    attendees: List[Dict] = None
    attendee_count: int = 0
    response_status: str = 'needsAction'  # needsAction, declined, tentative, accepted
    
    # Location and links
    location: Optional[str] = None
    conference_data: Optional[Dict] = None
    hangout_link: Optional[str] = None
    html_link: Optional[str] = None
    
    # Additional metadata
    created: Optional[datetime] = None
    updated: Optional[datetime] = None
    sequence: int = 0
    reminders: Dict = None
    attachments: List[Dict] = None
    
    @property
    def start_datetime(self) -> Optional[datetime]:
        """Get start time as datetime object."""
        if not self.start_time:
            return None
        if 'T' in self.start_time:  # DateTime
            dt = datetime.fromisoformat(self.start_time.replace('Z', '+00:00'))
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        # Date only - set to start of day in UTC
        dt = datetime.fromisoformat(self.start_time)
        return dt.replace(tzinfo=timezone.utc)
    
    @property
    def end_datetime(self) -> Optional[datetime]:
        """Get end time as datetime object."""
        if not self.end_time:
            return None
        if 'T' in self.end_time:  # DateTime
            dt = datetime.fromisoformat(self.end_time.replace('Z', '+00:00'))
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        # Date only - set to start of day in UTC
        dt = datetime.fromisoformat(self.end_time)
        return dt.replace(tzinfo=timezone.utc)
    
    @property
    def is_busy(self) -> bool:
        """Check if event blocks time."""
        return self.transparency == 'opaque'
    
    @property
    def has_guests(self) -> bool:
        """Check if event has other attendees."""
        return bool(self.attendees and len(self.attendees) > 1)
    
    @property
    def is_confirmed(self) -> bool:
        """Check if event is confirmed."""
        return self.status == 'confirmed'
    
    @property
    def is_evening_plan(self) -> bool:
        """Check if event is an evening plan.
        
        1. Check duration
        2. Check time of day
        3. Handle weekends differently
        v1
        """
        if not self.start_datetime or not self.duration_minutes:
            return False
            
        # 1. Check duration (> 2 hours)
        if self.duration_minutes <= 120:
            return False
            
        # 2. Check time of day
        hour = self.start_datetime.hour
        is_weekend = self.start_datetime.weekday() >= 5
        
        # 3. Different thresholds for weekends
        return (hour >= 14 if is_weekend else hour >= 18)
    
    def get_auto_tags(self) -> List[str]:
        """Get automatically generated tags.
        
        1. Check evening plans
        2. Add other auto tags
        v2
        """
        tags = []
        if self.is_evening_plan:
            tags.append("Evening Plans")
        return tags
    
    @property
    def filter_matches(self) -> List[str]:
        """Get combined filter matches and auto tags."""
        matches = self._filter_matches if hasattr(self, '_filter_matches') else []
        return matches + self.get_auto_tags()
    
    @filter_matches.setter
    def filter_matches(self, value: List[str]):
        """Set filter matches while preserving auto tags."""
        self._filter_matches = value
    
    @classmethod
    def from_gcal_event(cls, event: Dict) -> 'Event':
        """Create Event instance from Google Calendar event data.
        
        1. Extract basic info
        2. Extract status and flags
        3. Extract attendees and location
        v2
        """
        # Basic info
        start_time = event['start'].get('dateTime', event['start'].get('date', ''))
        end_time = event['end'].get('dateTime', event['end'].get('date', ''))
        
        # Calculate duration
        duration_minutes = None
        if start_time and end_time:
            try:
                start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                duration = end - start
                duration_minutes = int(duration.total_seconds() / 60)
            except (ValueError, TypeError):
                pass
        
        basic = {
            'id': event.get('id', ''),
            'title': event.get('summary', 'No Title'),
            'description': event.get('description'),
            'start_time': start_time,
            'end_time': end_time,
            'duration_minutes': duration_minutes,
            'is_all_day': 'date' in event['start']
        }
        
        # Status and flags
        status = {
            'status': event.get('status', 'confirmed'),
            'transparency': event.get('transparency', 'opaque'),
            'visibility': event.get('visibility', 'default'),
            'is_recurring': bool(event.get('recurringEventId')),
            'recurring_id': event.get('recurringEventId'),
        }
        
        # Attendees and organizers
        attendees = event.get('attendees', [])
        organizer = {
            'creator_email': event.get('creator', {}).get('email', ''),
            'organizer_email': event.get('organizer', {}).get('email', ''),
            'attendees': attendees,
            'attendee_count': len(attendees),
            'response_status': event.get('responseStatus', 'needsAction'),
        }
        
        # Location and links
        location = {
            'location': event.get('location'),
            'conference_data': event.get('conferenceData'),
            'hangout_link': event.get('hangoutLink'),
            'html_link': event.get('htmlLink'),
        }
        
        # Additional metadata
        def parse_datetime(dt_str: Optional[str]) -> Optional[datetime]:
            if not dt_str:
                return None
            try:
                return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                return None
        
        meta = {
            'created': parse_datetime(event.get('created')),
            'updated': parse_datetime(event.get('updated')),
            'sequence': event.get('sequence', 0),
            'reminders': event.get('reminders', {}),
            'attachments': event.get('attachments', []),
        }
        
        return cls(**basic, **status, **organizer, **location, **meta) 