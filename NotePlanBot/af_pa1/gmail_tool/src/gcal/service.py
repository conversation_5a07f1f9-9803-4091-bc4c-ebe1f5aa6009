from datetime import datetime, timedelta
from typing import List, Dict
from .auth import get_calendar_service


def get_upcoming_events(service=None, weeks: int = 3) -> List[Dict]:
    """Get calendar events for the next specified weeks.
    
    1. Calculate time range
    2. Call Calendar API
    3. Return formatted events
    v1
    """
    if service is None:
        service = get_calendar_service()
    
    # 1. Calculate time range
    now = datetime.utcnow()
    end_date = now + timedelta(weeks=weeks)
    
    # 2. Call Calendar API
    events_result = service.events().list(
        calendarId='primary',
        timeMin=now.isoformat() + 'Z',
        timeMax=end_date.isoformat() + 'Z',
        singleEvents=True,
        orderBy='startTime'
    ).execute()
    
    # 3. Return formatted events
    return events_result.get('items', []) 