import colorama
from typing import List
from datetime import datetime, timezone
import event_filters


# Initialize colorama
colorama.init()


def format_time(event: event_filters.Event) -> str:
    """Format event time for display.
    
    1. Handle all-day events
    2. Format date and time
    3. Add duration
    v2
    """
    if event.is_all_day:
        return "All day"
    
    start = event.start_datetime
    if not start:
        return "No time"
    
    # Format time
    time_str = start.strftime("%I:%M %p")
    if event.duration_minutes:
        time_str += f" ({event.duration_minutes}m)"
    
    return time_str


def get_event_color(event: event_filters.Event) -> str:
    """Get display color based on event properties.
    
    1. Check filter matches
    2. Check status
    3. Check transparency
    v2
    """
    # Filter-based colors
    if hasattr(event, 'filter_matches'):
        if any(f in event.filter_matches for f in ['sleep', 'ooo']):
            return colorama.Fore.RED
        if any(f in event.filter_matches for f in ['focus_time', 'long_meeting']):
            return colorama.Fore.YELLOW
        if any(f in event.filter_matches for f in ['quick_sync', 'routine']):
            return colorama.Fore.GREEN
        if any(f in event.filter_matches for f in ['optional', 'free']):
            return colorama.Fore.CYAN
    
    # Status-based colors
    if event.status == 'cancelled':
        return colorama.Fore.RED
    if event.status == 'tentative':
        return colorama.Fore.YELLOW
    
    # Transparency-based colors
    if event.transparency == 'transparent':
        return colorama.Fore.CYAN
    
    return colorama.Fore.WHITE


def format_attendees(event: event_filters.Event) -> str:
    """Format attendee information.
    
    1. Show count
    2. Show response status
    3. Handle no attendees
    v2
    """
    if not event.attendees:
        return "No attendees"
    
    status_counts = {
        'accepted': 0,
        'declined': 0,
        'tentative': 0,
        'needsAction': 0
    }
    
    for attendee in event.attendees:
        status = attendee.get('responseStatus', 'needsAction')
        status_counts[status] += 1
    
    parts = []
    if status_counts['accepted']:
        parts.append(f"{status_counts['accepted']}✓")
    if status_counts['declined']:
        parts.append(f"{status_counts['declined']}✗")
    if status_counts['tentative']:
        parts.append(f"{status_counts['tentative']}?")
    if status_counts['needsAction']:
        parts.append(f"{status_counts['needsAction']}•")
    
    return f"{event.attendee_count} ({' '.join(parts)})"


def display_events(events: List[event_filters.Event]):
    """Display formatted calendar events.
    
    1. Sort events by start time
    2. Format each event
    3. Show metadata
    v3
    """
    # Sort events by start time
    max_datetime = datetime.max.replace(tzinfo=timezone.utc)
    events.sort(key=lambda e: e.start_datetime or max_datetime)
    
    for event in events:
        # Get color based on event properties
        color = get_event_color(event)
        
        # Basic event info
        print(f"{color}{format_time(event)} - {event.title}{colorama.Style.RESET_ALL}")
        
        # Location and attendees
        if event.location:
            print(f"  📍 {event.location}")
        print(f"  👥 {format_attendees(event)}")
        
        # Conference links
        if event.hangout_link:
            print(f"  🎥 Meet: {event.hangout_link}")
        elif event.conference_data:
            print("  🎥 Has conference link")
        
        # Status and metadata
        status_line = []
        if event.status != 'confirmed':
            status_line.append(event.status.upper())
        if event.transparency == 'transparent':
            status_line.append('FREE')
        if event.is_recurring:
            status_line.append('RECURRING')
        if status_line:
            print(f"  ℹ️ {' | '.join(status_line)}")
        
        # Filter matches
        if hasattr(event, 'filter_matches') and event.filter_matches:
            print(f"  🏷️ {', '.join(event.filter_matches)}")
        
        # Separator between events
        print()