from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime, timezone


@dataclass
class Event:
    """Data class for calendar event metadata.
    
    1. Store basic event info
    2. Store attendee/organizer info
    3. Store status and flags
    4. Store location and links
    v2
    """
    # Basic info
    id: str
    title: str
    description: Optional[str] = None
    start_time: str = ''
    end_time: str = ''
    duration_minutes: Optional[int] = None
    
    # Status and flags
    status: str = 'confirmed'  # confirmed, tentative, cancelled
    transparency: str = 'opaque'  # opaque (busy) or transparent (free)
    visibility: str = 'default'  # default, public, private
    is_recurring: bool = False
    recurring_id: Optional[str] = None
    is_all_day: bool = False
    
    # Attendees and organizers
    creator_email: str = ''
    organizer_email: str = ''
    attendees: List[Dict] = None
    attendee_count: int = 0
    response_status: str = 'needsAction'  # needsAction, declined, tentative, accepted
    
    # Location and links
    location: Optional[str] = None
    conference_data: Optional[Dict] = None
    hangout_link: Optional[str] = None
    html_link: Optional[str] = None
    
    # Additional metadata
    created: Optional[datetime] = None
    updated: Optional[datetime] = None
    sequence: int = 0
    reminders: Dict = None
    attachments: List[Dict] = None
    
    @property
    def start_datetime(self) -> Optional[datetime]:
        """Get start time as datetime object."""
        if not self.start_time:
            return None
        if 'T' in self.start_time:  # DateTime
            dt = datetime.fromisoformat(self.start_time.replace('Z', '+00:00'))
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        # Date only - set to start of day in UTC
        dt = datetime.fromisoformat(self.start_time)
        return dt.replace(tzinfo=timezone.utc)
    
    @property
    def end_datetime(self) -> Optional[datetime]:
        """Get end time as datetime object."""
        if not self.end_time:
            return None
        if 'T' in self.end_time:  # DateTime
            dt = datetime.fromisoformat(self.end_time.replace('Z', '+00:00'))
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        # Date only - set to start of day in UTC
        dt = datetime.fromisoformat(self.end_time)
        return dt.replace(tzinfo=timezone.utc)
    
    @classmethod
    def from_gcal_event(cls, event: Dict) -> 'Event':
        """Create Event instance from Google Calendar event data.
        
        1. Extract basic info
        2. Extract status and flags
        3. Extract attendees and location
        v2
        """
        # Basic info
        start_time = event['start'].get('dateTime', event['start'].get('date', ''))
        end_time = event['end'].get('dateTime', event['end'].get('date', ''))
        
        # Calculate duration
        duration_minutes = None
        if start_time and end_time:
            try:
                start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                duration = end - start
                duration_minutes = int(duration.total_seconds() / 60)
            except (ValueError, TypeError):
                pass
        
        basic = {
            'id': event.get('id', ''),
            'title': event.get('summary', 'No Title'),
            'description': event.get('description'),
            'start_time': start_time,
            'end_time': end_time,
            'duration_minutes': duration_minutes,
            'is_all_day': 'date' in event['start']
        }
        
        # Status and flags
        status = {
            'status': event.get('status', 'confirmed'),
            'transparency': event.get('transparency', 'opaque'),
            'visibility': event.get('visibility', 'default'),
            'is_recurring': bool(event.get('recurringEventId')),
            'recurring_id': event.get('recurringEventId'),
        }
        
        # Attendees and organizers
        attendees = event.get('attendees', [])
        organizer = {
            'creator_email': event.get('creator', {}).get('email', ''),
            'organizer_email': event.get('organizer', {}).get('email', ''),
            'attendees': attendees,
            'attendee_count': len(attendees),
            'response_status': event.get('responseStatus', 'needsAction'),
        }
        
        # Location and links
        location = {
            'location': event.get('location'),
            'conference_data': event.get('conferenceData'),
            'hangout_link': event.get('hangoutLink'),
            'html_link': event.get('htmlLink'),
        }
        
        # Additional metadata
        def parse_datetime(dt_str: Optional[str]) -> Optional[datetime]:
            if not dt_str:
                return None
            try:
                return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                return None
        
        meta = {
            'created': parse_datetime(event.get('created')),
            'updated': parse_datetime(event.get('updated')),
            'sequence': event.get('sequence', 0),
            'reminders': event.get('reminders', {}),
            'attachments': event.get('attachments', []),
        }
        
        return cls(**basic, **status, **organizer, **location, **meta)


@dataclass
class EventFilter:
    """Base class for event filters.
    
    1. Define filter attributes
    2. Implement matching logic
    3. Track metadata
    v1
    """
    name: str
    description: str
    priority: int = 0  # Higher priority filters are checked first
    
    def matches(self, event: Event) -> bool:
        """Base match method - should be overridden."""
        return False


class TitlePatternFilter(EventFilter):
    """Filter events based on title patterns.
    
    1. Check for exact matches
    2. Check for contains matches
    3. Handle case sensitivity
    v2
    """
    def __init__(self, name: str, patterns: List[str], description: str = "", 
                 case_sensitive: bool = False, priority: int = 0):
        super().__init__(name=name, description=description, priority=priority)
        self.patterns = patterns
        self.case_sensitive = case_sensitive
    
    def matches(self, event: Event) -> bool:
        title = event.title
        if not self.case_sensitive:
            title = title.lower()
            patterns = [p.lower() for p in self.patterns]
        else:
            patterns = self.patterns
            
        return any(p in title for p in patterns)


class MetadataFilter(EventFilter):
    """Filter events based on metadata properties.
    
    1. Check transparency
    2. Check attendee count
    3. Check creator/organizer
    v2
    """
    def __init__(self, name: str, description: str = "", priority: int = 0,
                 transparency: Optional[str] = None,
                 min_attendees: Optional[int] = None,
                 max_attendees: Optional[int] = None,
                 creator_email: Optional[str] = None,
                 min_duration: Optional[int] = None,
                 max_duration: Optional[int] = None):
        super().__init__(name=name, description=description, priority=priority)
        self.transparency = transparency
        self.min_attendees = min_attendees
        self.max_attendees = max_attendees
        self.creator_email = creator_email
        self.min_duration = min_duration
        self.max_duration = max_duration
    
    def matches(self, event: Event) -> bool:
        if self.transparency and event.transparency == self.transparency:
            return True
        
        if self.min_attendees is not None and event.attendee_count < self.min_attendees:
            return False
        if self.max_attendees is not None and event.attendee_count > self.max_attendees:
            return False
        
        if self.creator_email and event.creator_email == self.creator_email:
            return True
        
        if (self.min_duration is not None and event.duration_minutes and 
                event.duration_minutes < self.min_duration):
            return False
        if (self.max_duration is not None and event.duration_minutes and 
                event.duration_minutes > self.max_duration):
            return False
        
        return False


class RecurringFilter(EventFilter):
    """Filter recurring events.
    
    1. Check recurrence rules
    2. Check series status
    v2
    """
    def matches(self, event: Event) -> bool:
        return event.is_recurring


class FilterManager:
    """Manage and apply event filters.
    
    1. Store filters
    2. Apply filters to events
    3. Track filter stats
    v1
    """
    def __init__(self):
        self.filters: List[EventFilter] = []
        self.stats: Dict[str, int] = {}  # Track how many events each filter catches
    
    def add_filter(self, filter_: EventFilter):
        """Add a filter and sort by priority."""
        self.filters.append(filter_)
        self.filters.sort(key=lambda x: x.priority, reverse=True)
    
    def get_filter_matches(self, event: Event) -> List[str]:
        """Get names of all filters that match an event."""
        matches = []
        for filter_ in self.filters:
            if filter_.matches(event):
                matches.append(filter_.name)
                self.stats[filter_.name] = self.stats.get(filter_.name, 0) + 1
        return matches


def create_default_filters() -> FilterManager:
    """Create default set of filters.
    
    1. Create basic filters
    2. Add metadata filters
    3. Return manager
    v2
    """
    manager = FilterManager()
    
    # Sleep patterns
    manager.add_filter(TitlePatternFilter(
        name="sleep",
        patterns=["😴", "sleep", "asleep"],
        description="Sleep-related events",
        priority=100
    ))
    
    # Focus/Reserved time
    manager.add_filter(TitlePatternFilter(
        name="focus_time",
        patterns=["focus", "reserved", "block"],
        description="Focus and reserved time blocks",
        priority=90
    ))
    
    # Out of office
    manager.add_filter(TitlePatternFilter(
        name="ooo",
        patterns=["out of office", "ooo", "off"],
        description="Out of office time",
        priority=80
    ))
    
    # Free time
    manager.add_filter(MetadataFilter(
        name="free",
        description="Events marked as free/transparent",
        transparency="transparent",
        priority=70
    ))
    
    # Solo events
    manager.add_filter(MetadataFilter(
        name="solo",
        description="Events with no other attendees",
        max_attendees=1,
        priority=60
    ))
    
    # Daily recurring
    manager.add_filter(RecurringFilter(
        name="recurring",
        description="Recurring calendar events",
        priority=50
    ))
    
    # Morning routine
    manager.add_filter(TitlePatternFilter(
        name="routine",
        patterns=["📧", "morning", "catch up", "laundry", "pack"],
        description="Regular routine tasks",
        priority=40
    ))
    
    # Optional events
    manager.add_filter(TitlePatternFilter(
        name="optional",
        patterns=["optional", "if needed", "?"],
        description="Optional or tentative events",
        priority=30
    ))
    
    # Short meetings
    manager.add_filter(MetadataFilter(
        name="quick_sync",
        description="Short sync meetings",
        max_duration=30,
        priority=20
    ))
    
    # Long meetings
    manager.add_filter(MetadataFilter(
        name="long_meeting",
        description="Long meetings",
        min_duration=90,
        priority=10
    ))
    
    return manager 