import os.path
import pickle
from datetime import datetime, timedelta
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build


SCOPES = ['https://www.googleapis.com/auth/calendar.readonly']
TOKEN_PATH = 'token.pickle'
CREDS_PATH = 'creds.json'


def get_calendar_service():
    """Get authenticated Google Calendar service.
    
    1. Check for existing token
    2. If no valid token, create new one
    3. Build and return calendar service
    v1
    """
    creds = None
    
    # 1. Check for existing token
    if os.path.exists(TOKEN_PATH):
        with open(TOKEN_PATH, 'rb') as token:
            creds = pickle.load(token)
    
    # 2. If no valid token, create new one
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CREDS_PATH, SCOPES)
            creds = flow.run_local_server(port=0)
        
        # Save credentials for future use
        with open(TOKEN_PATH, 'wb') as token:
            pickle.dump(creds, token)
    
    # 3. Build and return calendar service
    return build('calendar', 'v3', credentials=creds)


def get_upcoming_events(service, weeks=3):
    """Get calendar events for the next specified weeks.
    
    1. Calculate time range
    2. Call Calendar API
    3. Return formatted events
    v1
    """
    # 1. Calculate time range
    now = datetime.utcnow()
    end_date = now + timedelta(weeks=weeks)
    
    # 2. Call Calendar API
    events_result = service.events().list(
        calendarId='primary',
        timeMin=now.isoformat() + 'Z',
        timeMax=end_date.isoformat() + 'Z',
        singleEvents=True,
        orderBy='startTime'
    ).execute()
    
    # 3. Return formatted events
    return events_result.get('items', [])


if __name__ == '__main__':
    # Test authentication and fetch events
    service = get_calendar_service()
    events = get_upcoming_events(service)
    display_events(events) 