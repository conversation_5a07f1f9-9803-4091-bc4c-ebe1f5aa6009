import colorama
from src.models.event import Event


# Initialize colorama
colorama.init()


def get_event_color(event: Event) -> str:
    """Get display color based on event properties.
    
    1. Check filter matches
    2. Check status
    3. Check transparency
    v2
    """
    # Filter-based colors
    if hasattr(event, 'filter_matches'):
        if any(f in event.filter_matches for f in ['sleep', 'ooo']):
            return colorama.Fore.RED
        if any(f in event.filter_matches for f in ['focus_time', 'long_meeting']):
            return colorama.Fore.YELLOW
        if any(f in event.filter_matches for f in ['quick_sync', 'routine']):
            return colorama.Fore.GREEN
        if any(f in event.filter_matches for f in ['optional', 'free']):
            return colorama.Fore.CYAN
    
    # Status-based colors
    if event.status == 'cancelled':
        return colorama.Fore.RED
    if event.status == 'tentative':
        return colorama.Fore.YELLOW
    
    # Transparency-based colors
    if event.transparency == 'transparent':
        return colorama.Fore.CYAN
    
    return colorama.Fore.WHITE


def reset_color() -> str:
    """Reset color formatting."""
    return colorama.Style.RESET_ALL 