from typing import List
from datetime import datetime, timezone, timedelta
from rich.console import Console
from models.event import Event
from .formatter import create_event_table
from .stats import calculate_daily_stats, calculate_filter_stats, format_stats_summary


# Initialize rich console
console = Console()


def group_events_by_week(events: List[Event]) -> dict:
    """Group events by week start date.
    
    1. Sort events by start time
    2. Group by week starting Monday
    3. Return grouped events
    v2
    """
    # Sort events
    max_datetime = datetime.max.replace(tzinfo=timezone.utc)
    events.sort(key=lambda e: e.start_datetime or max_datetime)
    
    # Group by week
    weeks = {}
    for event in events:
        if not event.start_datetime:
            continue
        
        # Get week start (Monday)
        event_date = event.start_datetime.date()
        days_since_monday = event_date.weekday()  # Monday = 0, Sunday = 6
        week_start = event_date - timedelta(days=days_since_monday)
        
        if week_start not in weeks:
            weeks[week_start] = []
        weeks[week_start].append(event)
    
    return weeks


def display_events(events: List[Event]):
    """Display formatted calendar events.
    
    1. Group events by week
    2. Create tables for each week
    3. Show statistics tables
    v5
    """
    # Group events by week
    weeks = group_events_by_week(events)
    
    # Display each week's events
    for week_start, week_events in sorted(weeks.items()):
        event_table = create_event_table(week_events, week_start)
        console.print(event_table)
        console.print()
    
    # Show statistics tables
    daily_stats = calculate_daily_stats(events)
    filter_stats = calculate_filter_stats(events)
    for table in format_stats_summary(daily_stats, filter_stats):
        console.print(table)
        console.print()
