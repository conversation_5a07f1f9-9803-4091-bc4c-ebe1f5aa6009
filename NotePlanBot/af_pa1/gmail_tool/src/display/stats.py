from typing import List, Dict
from collections import defaultdict
from rich.table import Table
from rich.box import ROUNDED
from models.event import Event


def calculate_daily_stats(events: List[Event]) -> Dict:
    """Calculate daily event statistics.
    
    1. Group events by day
    2. Calculate busy time
    3. Track meeting counts
    v1
    """
    daily_stats = defaultdict(lambda: {
        'total_events': 0,
        'busy_minutes': 0,
        'meeting_count': 0,
        'attendee_total': 0
    })
    
    for event in events:
        if not event.start_datetime:
            continue
            
        day = event.start_datetime.date()
        stats = daily_stats[day]
        
        # Count events
        stats['total_events'] += 1
        
        # Track busy time
        if event.is_busy and event.duration_minutes:
            stats['busy_minutes'] += event.duration_minutes
        
        # Track meetings
        if event.has_guests:
            stats['meeting_count'] += 1
            stats['attendee_total'] += event.attendee_count
    
    return dict(daily_stats)


def calculate_filter_stats(events: List[Event]) -> Dict[str, int]:
    """Calculate filter match statistics.
    
    1. Count filter matches
    2. Track percentages
    3. Sort by frequency
    v1
    """
    filter_counts = defaultdict(int)
    total_events = len(events)
    
    for event in events:
        if hasattr(event, 'filter_matches'):
            for filter_name in event.filter_matches:
                filter_counts[filter_name] += 1
    
    # Convert to percentages
    stats = {}
    for name, count in filter_counts.items():
        stats[name] = {
            'count': count,
            'percentage': round(count / total_events * 100, 1)
        }
    
    return stats


def create_daily_stats_table(daily_stats: Dict) -> Table:
    """Create table for daily statistics.
    
    1. Setup columns
    2. Add daily rows
    3. Add totals
    v1
    """
    table = Table(
        title="Daily Summary",
        box=ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )
    
    # Add columns
    table.add_column("Date", style="cyan")
    table.add_column("Events", justify="right", style="green")
    table.add_column("Busy Hours", justify="right", style="yellow")
    table.add_column("Meetings", justify="right", style="blue")
    table.add_column("Avg Attendees", justify="right", style="magenta")
    
    # Track totals
    total_events = 0
    total_hours = 0
    total_meetings = 0
    total_attendees = 0
    
    # Add daily rows
    for day, stats in sorted(daily_stats.items()):
        busy_hours = stats['busy_minutes'] / 60
        avg_attendees = (stats['attendee_total'] / stats['meeting_count'] 
                      if stats['meeting_count'] > 0 else 0)
        
        table.add_row(
            day.strftime("%Y-%m-%d"),
            str(stats['total_events']),
            f"{busy_hours:.1f}",
            str(stats['meeting_count']),
            f"{avg_attendees:.1f}"
        )
        
        # Update totals
        total_events += stats['total_events']
        total_hours += busy_hours
        total_meetings += stats['meeting_count']
        total_attendees += stats['attendee_total']
    
    # Add totals row
    avg_total_attendees = (total_attendees / total_meetings 
                        if total_meetings > 0 else 0)
    table.add_row(
        "TOTAL",
        str(total_events),
        f"{total_hours:.1f}",
        str(total_meetings),
        f"{avg_total_attendees:.1f}",
        style="bold"
    )
    
    return table


def create_filter_stats_table(filter_stats: Dict) -> Table:
    """Create table for filter statistics.
    
    1. Setup columns
    2. Add filter rows
    3. Sort by count
    v1
    """
    table = Table(
        title="Filter Statistics",
        box=ROUNDED,
        show_header=True,
        header_style="bold red"
    )
    
    # Add columns
    table.add_column("Filter", style="red")
    table.add_column("Count", justify="right", style="yellow")
    table.add_column("Percentage", justify="right", style="green")
    
    # Add rows sorted by count
    for name, stats in sorted(
        filter_stats.items(),
        key=lambda x: x[1]['count'],
        reverse=True
    ):
        table.add_row(
            name,
            str(stats['count']),
            f"{stats['percentage']}%"
        )
    
    return table


def format_stats_summary(daily_stats: Dict, filter_stats: Dict) -> List[Table]:
    """Format statistics tables.
    
    1. Create daily stats table
    2. Create filter stats table
    3. Return tables
    v2
    """
    return [
        create_daily_stats_table(daily_stats),
        create_filter_stats_table(filter_stats)
    ] 