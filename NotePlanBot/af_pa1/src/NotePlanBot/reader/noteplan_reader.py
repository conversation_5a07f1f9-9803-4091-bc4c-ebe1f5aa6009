"""
NotePlan Reader - Reads and parses NotePlan notes
"""
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from rich import print as rprint

from NotePlanBot.config import CFG


class NotePlanReader:
    """Reads and parses NotePlan notes from the Notes directory"""
    
    def __init__(self, notes_path: Optional[str] = None):
        """Initialize with NotePlan notes directory path"""
        if notes_path is None:
            # Try to get from env/config first
            notes_path = os.getenv('NOTEPLAN_NOTES_PATH')
            if not notes_path:
                # Use config default
                notes_path = CFG.get('NOTES_DIR')
            if not notes_path:
                # Fallback to relative path
                base_path = Path(__file__).parents[5]  # Up to NotePlan app support dir
                notes_path = base_path / "Notes"
        
        self.notes_path = Path(notes_path)
        if not self.notes_path.exists():
            raise ValueError(f"Notes directory not found: {self.notes_path}")
    
    def read_note(self, note_path: str) -> str:
        """Read a single note file"""
        full_path = self.notes_path / note_path
        if not full_path.exists():
            raise FileNotFoundError(f"Note not found: {full_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def list_notes(self, pattern: Optional[str] = None) -> List[Path]:
        """List all notes, optionally filtered by pattern"""
        if pattern:
            return list(self.notes_path.glob(pattern))
        else:
            return list(self.notes_path.rglob("*.txt")) + list(self.notes_path.rglob("*.md"))
    
    def parse_tasks(self, content: str) -> List[Dict[str, any]]:
        """Parse tasks from note content
        
        Returns list of tasks with:
        - text: The task text
        - completed: Boolean indicating if task is done
        - line_number: Line number in the file
        - indent_level: Indentation level (for subtasks)
        """
        tasks = []
        lines = content.split('\n')
        
        # Regex patterns for tasks
        task_pattern = re.compile(r'^(\s*)[-*]\s*\[([ xX])\]\s*(.+)$')
        
        for i, line in enumerate(lines):
            match = task_pattern.match(line)
            if match:
                indent, status, text = match.groups()
                tasks.append({
                    'text': text.strip(),
                    'completed': status.lower() == 'x',
                    'line_number': i + 1,
                    'indent_level': len(indent),
                    'raw_line': line
                })
        
        return tasks
    
    def extract_metadata(self, content: str) -> Dict[str, str]:
        """Extract metadata from note (title, date, tags)"""
        metadata = {}
        lines = content.split('\n')
        
        # Extract title (first non-empty line)
        for line in lines:
            if line.strip():
                metadata['title'] = line.strip().lstrip('#').strip()
                break
        
        # Extract date from various formats
        date_patterns = [
            r'Date:\s*(\d{4}-\d{2}-\d{2})',
            r'(\d{4}-\d{2}-\d{2})',
            r'(\d{1,2}/\d{1,2}/\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, content)
            if match:
                metadata['date'] = match.group(1)
                break
        
        # Extract tags (hashtags)
        tags = re.findall(r'#(\w+)', content)
        if tags:
            metadata['tags'] = list(set(tags))
        
        return metadata
    
    def find_notes_with_tasks(self) -> List[Tuple[Path, List[Dict]]]:
        """Find all notes containing tasks"""
        notes_with_tasks = []
        
        for note_path in self.list_notes():
            try:
                content = self.read_note(note_path.relative_to(self.notes_path))
                tasks = self.parse_tasks(content)
                if tasks:
                    notes_with_tasks.append((note_path, tasks))
            except Exception as e:
                rprint(f"[red]Error reading {note_path}: {e}[/red]")
        
        return notes_with_tasks
    
    def get_daily_note(self, date: Optional[datetime] = None) -> Optional[str]:
        """Get the daily note for a specific date"""
        if date is None:
            date = datetime.now()
        
        # NotePlan daily note format
        date_str = date.strftime("%Y-%m-%d")
        
        # Check Calendar directory first
        calendar_path = self.notes_path.parent / "Calendar" / f"{date_str}.txt"
        if calendar_path.exists():
            with open(calendar_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        # Check Notes directory
        for note_path in self.list_notes(f"*{date_str}*"):
            return self.read_note(note_path.relative_to(self.notes_path))
        
        return None
    
    def extract_tasks(self, content):
        """Extract all tasks (- [ ] or - [x]) from content"""
        lines = content.split('\n')
        tasks = []
        for line in lines:
            if line.strip().startswith('- [ ]') or line.strip().startswith('- [x]'):
                tasks.append(line.strip())
        return tasks
    
    def extract_checkboxes(self, content):
        """Extract all checkboxes (+ [ ] or + [x]) from content"""
        lines = content.split('\n')
        checkboxes = []
        for line in lines:
            if line.strip().startswith('+ [ ]') or line.strip().startswith('+ [x]'):
                checkboxes.append(line.strip())
        return checkboxes
    
    def extract_todos_with_hierarchy(self, content):
        """Extract todos with parent-child relationships based on indentation"""
        lines = content.split('\n')
        todos = []
        current_parent = None
        
        for i, line in enumerate(lines):
            if not line.strip():
                continue
                
            # Calculate indent level (tabs or spaces)
            indent_level = len(line) - len(line.lstrip('\t'))
            if indent_level == 0:
                indent_level = (len(line) - len(line.lstrip(' '))) // 4  # 4 spaces = 1 indent
            
            # Check if it's a task or checkbox
            stripped = line.strip()
            is_task = stripped.startswith('- [ ]') or stripped.startswith('- [x]')
            is_checkbox = stripped.startswith('+ [ ]') or stripped.startswith('+ [x]')
            
            if is_task or is_checkbox:
                todo_item = {
                    'type': 'task' if is_task else 'checkbox',
                    'line': stripped,
                    'completed': '[x]' in stripped,
                    'indent_level': indent_level,
                    'line_number': i + 1,
                    'children': []
                }
                
                if indent_level == 0:
                    # Top-level item
                    current_parent = todo_item
                    todos.append(todo_item)
                elif current_parent and indent_level > 0:
                    # Sub-item - add to current parent
                    current_parent['children'].append(todo_item)
                    
        return todos
    
    def extract_events(self, content):
        """Extract events (lines starting with time format HH:MM) from content"""
        lines = content.split('\n')
        events = []
        time_pattern = re.compile(r'^\d{1,2}:\d{2}')
        for line in lines:
            if time_pattern.match(line.strip()):
                events.append(line.strip())
        return events
    
    def get_today_todos(self):
        """Get all todos (tasks and checkboxes) from today's calendar"""
        today = datetime.now()
        date_str = today.strftime("%Y%m%d")
        
        result = {
            'date': today.strftime("%Y-%m-%d"),
            'tasks': {
                'incomplete': [],
                'complete': []
            },
            'checkboxes': {
                'incomplete': [],
                'complete': []
            }
        }
        
        # Check Calendar directory with YYYYMMDD format
        calendar_path = self.notes_path.parent / "Calendar" / f"{date_str}.txt"
        
        if not calendar_path.exists():
            return result
            
        with open(calendar_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract tasks (- [ ] or - [x])
        all_tasks = self.extract_tasks(content)
        for task in all_tasks:
            if task.startswith('- [ ]'):
                result['tasks']['incomplete'].append(task)
            elif task.startswith('- [x]'):
                result['tasks']['complete'].append(task)
                
        # Extract checkboxes (+ [ ] or + [x])
        all_checkboxes = self.extract_checkboxes(content)
        for checkbox in all_checkboxes:
            if checkbox.startswith('+ [ ]'):
                result['checkboxes']['incomplete'].append(checkbox)
            elif checkbox.startswith('+ [x]'):
                result['checkboxes']['complete'].append(checkbox)
                
        return result
    
    def get_all_outstanding_todos(self, days_back=30):
        """Get all incomplete todos from the last N days"""
        from datetime import timedelta
        
        all_outstanding = {
            'tasks': [],
            'checkboxes': []
        }
        
        # Check calendar files for the last N days
        for i in range(days_back):
            date = datetime.now() - timedelta(days=i)
            date_str = date.strftime("%Y%m%d")
            calendar_path = self.notes_path.parent / "Calendar" / f"{date_str}.txt"
            
            if calendar_path.exists():
                with open(calendar_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Get incomplete tasks
                tasks = self.extract_tasks(content)
                for task in tasks:
                    if task.startswith('- [ ]'):
                        all_outstanding['tasks'].append({
                            'date': date.strftime("%Y-%m-%d"),
                            'task': task
                        })
                
                # Get incomplete checkboxes
                checkboxes = self.extract_checkboxes(content)
                for checkbox in checkboxes:
                    if checkbox.startswith('+ [ ]'):
                        all_outstanding['checkboxes'].append({
                            'date': date.strftime("%Y-%m-%d"),
                            'checkbox': checkbox
                        })
        
        # Sort by date (most recent first)
        all_outstanding['tasks'].sort(key=lambda x: x['date'], reverse=True)
        all_outstanding['checkboxes'].sort(key=lambda x: x['date'], reverse=True)
        
        return all_outstanding


if __name__ == "__main__":
    # Test the reader
    try:
        reader = NotePlanReader()
        rprint(f"[green]NotePlan notes directory: {reader.notes_path}[/green]")
        
        # Test today's todos
        rprint("\n[cyan]Today's TODOs:[/cyan]")
        today_todos = reader.get_today_todos()
        
        rprint(f"\n[yellow]Tasks (using - [ ]):[/yellow]")
        if today_todos['tasks']['incomplete']:
            for task in today_todos['tasks']['incomplete']:
                rprint(f"  ○ {task}")
        else:
            rprint("  No incomplete tasks")
            
        if today_todos['tasks']['complete']:
            rprint(f"\n[green]Completed tasks:[/green]")
            for task in today_todos['tasks']['complete']:
                rprint(f"  ✓ {task}")
        
        rprint(f"\n[yellow]Checkboxes (using + [ ]):[/yellow]")
        if today_todos['checkboxes']['incomplete']:
            for checkbox in today_todos['checkboxes']['incomplete']:
                rprint(f"  ☐ {checkbox}")
        else:
            rprint("  No incomplete checkboxes")
            
        if today_todos['checkboxes']['complete']:
            rprint(f"\n[green]Completed checkboxes:[/green]")
            for checkbox in today_todos['checkboxes']['complete']:
                rprint(f"  ☑ {checkbox}")
        
        # Test outstanding todos
        rprint("\n[cyan]Outstanding TODOs (last 7 days):[/cyan]")
        outstanding = reader.get_all_outstanding_todos(days_back=7)
        
        if outstanding['tasks']:
            rprint(f"\n[yellow]Outstanding Tasks:[/yellow]")
            for item in outstanding['tasks'][:5]:  # Show first 5
                rprint(f"  {item['date']}: {item['task']}")
            if len(outstanding['tasks']) > 5:
                rprint(f"  ... and {len(outstanding['tasks']) - 5} more")
        
        if outstanding['checkboxes']:
            rprint(f"\n[yellow]Outstanding Checkboxes:[/yellow]")
            for item in outstanding['checkboxes'][:5]:  # Show first 5
                rprint(f"  {item['date']}: {item['checkbox']}")
            if len(outstanding['checkboxes']) > 5:
                rprint(f"  ... and {len(outstanding['checkboxes']) - 5} more")
    
    except Exception as e:
        rprint(f"[red]Error: {e}[/red]")