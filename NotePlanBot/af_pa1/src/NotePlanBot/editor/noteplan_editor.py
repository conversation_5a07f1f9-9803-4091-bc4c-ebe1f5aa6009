"""
NotePlan Editor - Writes and updates NotePlan notes
"""
import re
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from rich import print as rprint

from NotePlanBot.config import CFG


class NotePlanEditor:
    """Edits and updates NotePlan notes"""
    
    def __init__(self, notes_path: Optional[str] = None):
        """Initialize with NotePlan notes directory path"""
        if notes_path is None:
            # Try to get from env/config first
            import os
            notes_path = os.getenv('NOTEPLAN_NOTES_PATH')
            if not notes_path:
                # Use config default
                notes_path = CFG.get('NOTES_DIR')
            if not notes_path:
                # Fallback to relative path
                base_path = Path(__file__).parents[5]  # Up to NotePlan app support dir
                notes_path = base_path / "Notes"
        
        self.notes_path = Path(notes_path)
        if not self.notes_path.exists():
            raise ValueError(f"Notes directory not found: {self.notes_path}")
    
    def update_task_status(self, note_path: str, task_line_number: int, completed: bool) -> bool:
        """Update a task's completion status"""
        full_path = self.notes_path / note_path
        if not full_path.exists():
            raise FileNotFoundError(f"Note not found: {full_path}")
        
        # Read the file
        with open(full_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Update the task line
        if task_line_number <= 0 or task_line_number > len(lines):
            raise ValueError(f"Invalid line number: {task_line_number}")
        
        line_idx = task_line_number - 1
        line = lines[line_idx]
        
        # Replace [ ] with [x] or vice versa
        if completed:
            updated_line = re.sub(r'\[([ ])\]', '[x]', line)
        else:
            updated_line = re.sub(r'\[([xX])\]', '[ ]', line)
        
        if updated_line == line:
            return False  # No change made
        
        lines[line_idx] = updated_line
        
        # Write back
        with open(full_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        return True
    
    def add_task(self, note_path: str, task_text: str, after_line: Optional[int] = None) -> int:
        """Add a new task to a note
        
        Returns the line number of the added task
        """
        full_path = self.notes_path / note_path
        if not full_path.exists():
            raise FileNotFoundError(f"Note not found: {full_path}")
        
        # Read the file
        with open(full_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Format the task
        task_line = f"- [ ] {task_text}\n"
        
        if after_line is None:
            # Find the last task or add at end
            last_task_idx = None
            for i, line in enumerate(lines):
                if re.match(r'^[-*]\s*\[[ xX]\]', line.strip()):
                    last_task_idx = i
            
            if last_task_idx is not None:
                insert_idx = last_task_idx + 1
            else:
                # Add after a blank line at the end
                if lines and not lines[-1].strip():
                    insert_idx = len(lines) - 1
                else:
                    lines.append('\n')
                    insert_idx = len(lines)
        else:
            insert_idx = min(after_line, len(lines))
        
        # Insert the task
        lines.insert(insert_idx, task_line)
        
        # Write back
        with open(full_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        return insert_idx + 1  # Return 1-based line number
    
    def create_note(self, note_path: str, title: str, content: Optional[str] = None) -> Path:
        """Create a new note with given title and optional content"""
        full_path = self.notes_path / note_path
        
        # Ensure parent directory exists
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create default content if not provided
        if content is None:
            content = f"""# {title}
Date: {datetime.now().strftime('%Y-%m-%d')}

## Tasks
- [ ] 

## Notes

"""
        
        # Write the note
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return full_path
    
    def append_to_note(self, note_path: str, text: str, section: Optional[str] = None) -> bool:
        """Append text to a note, optionally under a specific section"""
        full_path = self.notes_path / note_path
        if not full_path.exists():
            raise FileNotFoundError(f"Note not found: {full_path}")
        
        # Read the file
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if section:
            # Find the section
            section_pattern = rf'^#+\s*{re.escape(section)}\s*$'
            lines = content.split('\n')
            section_idx = None
            
            for i, line in enumerate(lines):
                if re.match(section_pattern, line, re.IGNORECASE):
                    section_idx = i
                    break
            
            if section_idx is not None:
                # Find where to insert (before next section or end)
                insert_idx = len(lines)
                section_level = len(re.match(r'^(#+)', lines[section_idx]).group(1))
                
                for i in range(section_idx + 1, len(lines)):
                    if re.match(rf'^#{{{1},{section_level}}}\s+', lines[i]):
                        insert_idx = i
                        break
                
                # Insert the text
                lines.insert(insert_idx, text)
                content = '\n'.join(lines)
            else:
                # Section not found, create it
                content += f"\n\n## {section}\n{text}"
        else:
            # Just append at the end
            if not content.endswith('\n'):
                content += '\n'
            content += f"\n{text}"
        
        # Write back
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
    
    def update_daily_note(self, date: Optional[datetime] = None, tasks: Optional[List[str]] = None) -> Path:
        """Update or create daily note with tasks"""
        if date is None:
            date = datetime.now()
        
        date_str = date.strftime("%Y-%m-%d")
        
        # Check Calendar directory first
        calendar_dir = self.notes_path.parent / "Calendar"
        calendar_dir.mkdir(exist_ok=True)
        
        daily_note_path = calendar_dir / f"{date_str}.txt"
        
        if daily_note_path.exists():
            # Update existing note
            if tasks:
                for task in tasks:
                    self.add_task(
                        str(daily_note_path.relative_to(self.notes_path.parent)),
                        task
                    )
        else:
            # Create new daily note
            content = f"""# {date_str}

## Tasks
"""
            if tasks:
                for task in tasks:
                    content += f"- [ ] {task}\n"
            else:
                content += "- [ ] \n"
            
            content += "\n## Notes\n\n"
            
            with open(daily_note_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        return daily_note_path


if __name__ == "__main__":
    # Test the editor
    try:
        editor = NotePlanEditor()
        rprint("[green]NotePlan editor initialized[/green]")
        
        # Create a test note
        test_note_path = "NotePlanBot/test_note.txt"
        rprint(f"\n[cyan]Creating test note: {test_note_path}[/cyan]")
        
        note_path = editor.create_note(
            test_note_path,
            "NotePlanBot Test Note",
            """# NotePlanBot Test Note
Date: 2025-01-07

## Tasks
- [ ] Test task creation
- [ ] Test task updates
- [x] Completed task example

## Notes
This is a test note created by NotePlanBot.
"""
        )
        
        rprint(f"[green]Created note at: {note_path}[/green]")
        
        # Add a task
        line_num = editor.add_task(test_note_path, "New task added by NotePlanBot")
        rprint(f"[green]Added task at line {line_num}[/green]")
        
        # Update task status
        editor.update_task_status(test_note_path, 3, completed=True)
        rprint("[green]Updated task status on line 3[/green]")
        
    except Exception as e:
        rprint(f"[red]Error: {e}[/red]")