"""
Global configuration management for NotePlanBot
"""
import os
from pathlib import Path
from typing import Any, Dict

from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Config:
    """Singleton configuration class for NotePlanBot"""
    _instance = None
    _config: Dict[str, Any] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """Initialize configuration from environment and defaults"""
        # Base paths
        self._config['BASE_DIR'] = Path(__file__).parents[2]  # NotePlanBot root
        self._config['NOTEPLAN_DIR'] = self._config['BASE_DIR'].parent  # NotePlan app support dir
        self._config['NOTES_DIR'] = self._config['NOTEPLAN_DIR'] / "Notes"
        self._config['CALENDAR_DIR'] = self._config['NOTEPLAN_DIR'] / "Calendar"
        
        # Bridge configuration
        self._config['BRIDGE_FILE'] = os.getenv(
            'BRIDGE_FILE',
            str(self._config['BASE_DIR'] / "src" / "NotePlanBot" / "files" / "bridge" / "bridge.md")
        )
        self._config['TODO_CHECK_INTERVAL'] = int(os.getenv('TODO_CHECK_INTERVAL', '10'))  # 10 seconds for development
        
        # Claude Code configuration
        claude_path = os.path.expanduser('~/.claude/local/claude')
        self._config['CLAUDE_COMMAND'] = os.getenv('CLAUDE_COMMAND', claude_path)
        self._config['CLAUDE_TIMEOUT'] = int(os.getenv('CLAUDE_TIMEOUT', '300'))  # 5 minutes
        
        # NotePlan integration
        self._config['NOTEPLAN_TASK_NOTE'] = os.getenv('NOTEPLAN_TASK_NOTE', 'NotePlanBot/tasks.txt')
        self._config['AUTO_CREATE_DAILY_NOTE'] = os.getenv('AUTO_CREATE_DAILY_NOTE', 'true').lower() == 'true'
        
        # Display configuration
        self._config['RICH_CONSOLE_WIDTH'] = int(os.getenv('RICH_CONSOLE_WIDTH', '120'))
        self._config['SHOW_TIMESTAMPS'] = os.getenv('SHOW_TIMESTAMPS', 'true').lower() == 'true'
        
        # API Keys (if needed for future integrations)
        self._config['ANTHROPIC_API_KEY'] = os.getenv('ANTHROPIC_API_KEY', '')
        
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """Set configuration value"""
        self._config[key] = value
    
    def update(self, config_dict: Dict[str, Any]):
        """Update multiple configuration values"""
        self._config.update(config_dict)
    
    def all(self) -> Dict[str, Any]:
        """Get all configuration values"""
        return self._config.copy()
    
    def reload(self):
        """Reload configuration from environment"""
        load_dotenv(override=True)
        self._initialize()


# Global configuration instance
CFG = Config()


# For convenience, expose commonly used values
BASE_DIR = CFG.get('BASE_DIR')
NOTEPLAN_DIR = CFG.get('NOTEPLAN_DIR')
NOTES_DIR = CFG.get('NOTES_DIR')
CALENDAR_DIR = CFG.get('CALENDAR_DIR')
BRIDGE_FILE = CFG.get('BRIDGE_FILE')
TODO_CHECK_INTERVAL = CFG.get('TODO_CHECK_INTERVAL')


if __name__ == "__main__":
    from rich import print as rprint
    from rich.table import Table
    
    # Display configuration
    table = Table(title="NotePlanBot Configuration")
    table.add_column("Key", style="cyan")
    table.add_column("Value", style="green")
    
    for key, value in CFG.all().items():
        # Mask sensitive values
        if 'KEY' in key and value:
            display_value = value[:10] + "..." if len(value) > 10 else value
        else:
            display_value = str(value)
        table.add_row(key, display_value)
    
    rprint(table)