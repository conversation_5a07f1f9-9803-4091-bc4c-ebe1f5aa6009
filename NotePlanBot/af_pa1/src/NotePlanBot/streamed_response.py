"""
Trigger Direction Streamed Response Function
Allows user to trigger AI responses with streaming output
"""
import queue
import subprocess
import threading
from typing import Callable, Optional

from rich.console import Console
from rich.live import Live
from rich.markdown import Markdown
from rich.panel import Panel

console = Console()


class StreamedResponse:
    """Handles streaming responses from Claude Code"""
    
    def __init__(self):
        self.output_queue = queue.Queue()
        self.is_streaming = False
        self.process = None
        
    def trigger_response(self, prompt: str, callback: Optional[Callable] = None):
        """
        Trigger a streamed response from Claude Code
        
        Args:
            prompt: The prompt to send to <PERSON>
            callback: Optional callback function to process chunks
        """
        if self.is_streaming:
            console.print("[yellow]Already streaming a response[/yellow]")
            return
            
        self.is_streaming = True
        
        # Run Claude in a thread to stream output
        thread = threading.Thread(
            target=self._run_claude_stream,
            args=(prompt, callback),
            daemon=True
        )
        thread.start()
        
    def _run_claude_stream(self, prompt: str, callback: Optional[Callable]):
        """Run Claude and stream the output"""
        try:
            # Use unbuffered output for real-time streaming
            cmd = ["claude", "-p", prompt]
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # Line buffered
                universal_newlines=True
            )
            
            # Stream stdout
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    self.output_queue.put(('stdout', line.rstrip()))
                    if callback:
                        callback(line.rstrip())
                        
            # Wait for process to complete
            self.process.wait()
            
            # Get any stderr
            stderr = self.process.stderr.read()
            if stderr:
                self.output_queue.put(('stderr', stderr))
                
        except Exception as e:
            self.output_queue.put(('error', str(e)))
        finally:
            self.is_streaming = False
            self.output_queue.put(('done', None))
            
    def stop_streaming(self):
        """Stop the current streaming process"""
        if self.process and self.process.poll() is None:
            self.process.terminate()
            self.is_streaming = False
            

class DirectionTrigger:
    """
    Trigger directed AI responses based on user input
    Monitors for trigger phrases and initiates streaming responses
    """
    
    def __init__(self):
        self.streamer = StreamedResponse()
        self.trigger_phrases = [
            "LLM - what do you think?",
            "Claude, ",
            "AI: ",
            "//",  # Quick trigger
        ]
        
    def check_trigger(self, text: str) -> Optional[str]:
        """
        Check if text contains a trigger phrase
        Returns the prompt after the trigger, or None
        """
        for trigger in self.trigger_phrases:
            if trigger in text:
                # Extract prompt after trigger
                parts = text.split(trigger, 1)
                if len(parts) > 1:
                    return parts[1].strip()
        return None
        
    def process_input(self, text: str) -> bool:
        """
        Process input text and trigger response if needed
        Returns True if response was triggered
        """
        prompt = self.check_trigger(text)
        if prompt:
            console.print(f"[cyan]Triggering AI response for: {prompt}[/cyan]")
            
            # Create a live display for streaming
            with Live(console=console, refresh_per_second=4) as live:
                response_text = ""
                
                def update_display(chunk):
                    nonlocal response_text
                    response_text += chunk + "\n"
                    md = Markdown(response_text)
                    panel = Panel(
                        md,
                        title="[bold blue]Claude Response[/bold blue]",
                        border_style="blue"
                    )
                    live.update(panel)
                
                # Trigger the response
                self.streamer.trigger_response(prompt, update_display)
                
                # Wait for completion
                while self.streamer.is_streaming:
                    try:
                        msg_type, content = self.streamer.output_queue.get(timeout=0.1)
                        if msg_type == 'done':
                            break
                    except queue.Empty:
                        continue
                        
            return True
        return False


def create_trigger_hook(bridge_file_path: str):
    """
    Create a hook function that monitors bridge file for triggers
    
    Args:
        bridge_file_path: Path to the bridge file to monitor
    
    Returns:
        Hook function that can be called with file content
    """
    trigger = DirectionTrigger()
    
    def hook(content: str):
        """Process bridge file content for triggers"""
        # Look for new lines that might contain triggers
        lines = content.split('\n')
        
        # Check last few lines for triggers
        for line in lines[-5:]:
            if line.strip() and trigger.process_input(line):
                # Found and processed a trigger
                break
                
    return hook


# Example usage and testing
if __name__ == "__main__":
    console.print("[bold green]Testing Streamed Response System[/bold green]\n")
    
    # Test direct trigger
    trigger = DirectionTrigger()
    
    # Test trigger detection
    test_inputs = [
        "what's the next step? hmmm... LLM - what do you think? Should we implement caching?",
        "Claude, explain how Python async works",
        "// quick question about decorators",
        "This won't trigger anything",
    ]
    
    for test in test_inputs:
        console.print(f"\n[yellow]Testing:[/yellow] {test}")
        triggered = trigger.process_input(test)
        if not triggered:
            console.print("[dim]No trigger found[/dim]")