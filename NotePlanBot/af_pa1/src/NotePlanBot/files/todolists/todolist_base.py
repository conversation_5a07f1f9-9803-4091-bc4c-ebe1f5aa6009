"""
Base todolist file for the second pane - LLM-Managed Task List
This file will be displayed in the second vertical split in VSCode
"""
from datetime import datetime
from typing import Any, Dict, List


class TodoList:
    """
    TODO LIST - Managed by <PERSON>
    Last Updated: {timestamp}
    
    ACTIVE TASKS:
    -------------
    [ ] Task 1 - Description
    [ ] Task 2 - Description
    
    COMPLETED:
    ----------
    [x] Completed task - Description
    
    NOTES:
    ------
    - This list is automatically managed by Claude Code
    - Changes are triggered by updates to bridge files
    """
    
    def __init__(self):
        self.tasks: List[Dict[str, Any]] = []
        self.completed: List[Dict[str, Any]] = []
        self.last_updated = datetime.now()
    
    def add_task(self, description: str, priority: str = "medium"):
        """Add a new task to the list"""
        task = {
            "id": len(self.tasks) + 1,
            "description": description,
            "priority": priority,
            "created": datetime.now().isoformat(),
            "status": "pending"
        }
        self.tasks.append(task)
    
    def complete_task(self, task_id: int):
        """Mark a task as completed"""
        for i, task in enumerate(self.tasks):
            if task["id"] == task_id:
                task["status"] = "completed"
                task["completed"] = datetime.now().isoformat()
                self.completed.append(self.tasks.pop(i))
                break
    
    def to_markdown(self) -> str:
        """Convert the todo list to markdown format"""
        md = f"""# TODO LIST - Managed by Claude Code
Last Updated: {self.last_updated.strftime('%Y-%m-%d %H:%M:%S')}

## ACTIVE TASKS:
"""
        for task in self.tasks:
            priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(task["priority"], "⚪")
            md += f"- [ ] {priority_emoji} {task['description']} (ID: {task['id']})\n"
        
        md += "\n## COMPLETED:\n"
        for task in self.completed[-10:]:  # Show last 10 completed
            md += f"- [x] ~~{task['description']}~~ ✓ {task.get('completed', 'N/A')[:10]}\n"
        
        md += "\n---\n*Auto-managed by NotePlanBot*"
        return md
    
    def save_to_file(self, filepath: str):
        """Save the todo list to a markdown file"""
        with open(filepath, 'w') as f:
            f.write(self.to_markdown())


# Example initial todo list
if __name__ == "__main__":
    todo = TodoList()
    todo.add_task("Create todolist.py monitor", priority="high")
    todo.add_task("Implement bridge file monitoring", priority="high")
    todo.add_task("Add Claude Code integration", priority="medium")
    todo.add_task("Test the system end-to-end", priority="medium")
    
    # Mark first two as completed
    todo.complete_task(1)
    todo.complete_task(2)
    
    print(todo.to_markdown())