"""
<Title> OK so what goes here? </Title>
<Date> 2023-01-06 </Date>

# OK so what goes here?
The idea is that we just type what's we're up to. Drop context, link to filenames if something is long, fold content if it's too long. etc.

Then the second pane is the ongoing LLM-Managed Task List
And the Third is some kind of "log" of the research, content, suggestions etc that the LLMs are making for us as we go.
NOTE: the panes are just files I open in Trae (Vscode) with vertical split.

TASKS
1. Create todolist.py which runs every TODO_CHECK_INTERVAL = 30 # seconds. It should inherit from Bridge.config["TODO_CHECK_INTERVAL"]
    1.1 self.bridge.
    1.2 check for > 1 LOC change from previous bridge check (self.bridge_history[-1])
    1.1 it should programmatically run Claude Code using subprocess.run() (check CC documentation online).



"""
from typing import ClassVar, List

import pydantic
from rich import print as rprint

from NotePlanBot.utils.bridge_utils import _get_bridge


class Bridge(pydantic.BaseModel):
    title: str
    date: str
    bridge: str = ""
    bridge_history: List[str] = pydantic.Field(default_factory=list)  # NOTE probably add timestamps?
    ongoing_tasks: List[str] = pydantic.Field(default_factory=list)
    log: List[str] = pydantic.Field(default_factory=list)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.bridge = _get_bridge(__file__)
    def edit_tasks(self, tasks):
        """have CC do this for you"""
        # TODO

    config: ClassVar[dict] = {
        "TODO_CHECK_INTERVAL": 30,  #seconds
        "date": "2023-01-06",
        "content": "",
        "ongoing_tasks": [],
        "log": [],
    }


# Test code to show output
if __name__ == "__main__":
    rprint("[green]Running bridge1.py test[/green]")
    rprint("\n[yellow]Bridge content from docstring:[/yellow]")
    content = _get_bridge(__file__)
    rprint(content)
    
    # Test Bridge class initialization
    rprint("\n[cyan]Testing Bridge class:[/cyan]")
    bridge = Bridge(title="Test Title", date="2023-01-06")
    rprint(f"Bridge object created: {bridge}")
    rprint(f"Bridge content attribute: {bridge.bridge}")


