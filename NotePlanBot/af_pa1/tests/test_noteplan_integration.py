#!/usr/bin/env python3
"""
Test suite for NotePlan integration
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.NotePlanBot.reader.noteplan_reader import NotePlanReader
from src.NotePlanBot.editor.noteplan_editor import NotePlanEditor
from src.NotePlanBot.config import CFG
from src.NotePlanBot.files.bridge.bridge_model import Bridge
from src.NotePlanBot.todolist_monitor import TodolistMonitor


class TestNotePlanReader(unittest.TestCase):
    """Test NotePlan reader functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.reader = NotePlanReader()
        
    @patch('builtins.open', create=True)
    def test_read_note(self, mock_open):
        """Test reading a note"""
        mock_open.return_value.__enter__.return_value.read.return_value = "Test content"
        
        content = self.reader.read_note("test.txt")
        
        self.assertEqual(content, "Test content")
        mock_open.assert_called_once_with("test.txt", 'r')
        print("✓ Read note works")
        
    def test_extract_tasks(self):
        """Test extracting tasks from content"""
        content = """Some text
- [ ] Incomplete task
- [x] Complete task
- Regular bullet point
- [ ] Another task"""
        
        tasks = self.reader.extract_tasks(content)
        
        self.assertEqual(len(tasks), 3)
        self.assertIn("- [ ] Incomplete task", tasks)
        self.assertIn("- [x] Complete task", tasks)
        print("✓ Extract tasks works")
        
    def test_extract_events(self):
        """Test extracting events from content"""
        content = """09:00 Morning standup
Some notes
14:00 Client meeting
Not an event
18:30 Team dinner"""
        
        events = self.reader.extract_events(content)
        
        self.assertEqual(len(events), 3)
        self.assertIn("09:00 Morning standup", events)
        self.assertIn("14:00 Client meeting", events)
        print("✓ Extract events works")


class TestNotePlanEditor(unittest.TestCase):
    """Test NotePlan editor functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.editor = NotePlanEditor()
        
    @patch('os.makedirs')
    @patch('builtins.open', create=True)
    def test_create_note(self, mock_open, mock_makedirs):
        """Test creating a new note"""
        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        self.editor.create_note("/path/to/note.txt", "New content")
        
        mock_makedirs.assert_called_once()
        mock_file.write.assert_called_once_with("New content")
        print("✓ Create note works")
        
    @patch('builtins.open', create=True)
    def test_update_note(self, mock_open):
        """Test updating existing note"""
        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        self.editor.update_note("note.txt", "Updated content")
        
        mock_file.write.assert_called_once_with("Updated content")
        print("✓ Update note works")
        
    @patch('src.NotePlanBot.reader.noteplan_reader.NotePlanReader.read_note')
    @patch('src.NotePlanBot.editor.noteplan_editor.NotePlanEditor.update_note')
    def test_add_task(self, mock_update, mock_read):
        """Test adding task to note"""
        mock_read.return_value = "Existing content"
        
        self.editor.add_task("note.txt", "New task")
        
        mock_update.assert_called_once()
        updated_content = mock_update.call_args[0][1]
        self.assertIn("- [ ] New task", updated_content)
        print("✓ Add task works")
        
    @patch('src.NotePlanBot.reader.noteplan_reader.NotePlanReader.read_note')
    @patch('src.NotePlanBot.editor.noteplan_editor.NotePlanEditor.update_note')
    def test_complete_task(self, mock_update, mock_read):
        """Test completing a task"""
        mock_read.return_value = "- [ ] Incomplete task\n- [ ] Another task"
        
        self.editor.complete_task("note.txt", "Incomplete task")
        
        mock_update.assert_called_once()
        updated_content = mock_update.call_args[0][1]
        self.assertIn("- [x] Incomplete task", updated_content)
        print("✓ Complete task works")


class TestBridgeModel(unittest.TestCase):
    """Test Bridge model functionality"""
    
    def test_bridge_initialization(self):
        """Test Bridge model creation"""
        bridge = Bridge(content="Test content")
        
        self.assertEqual(bridge.content, "Test content")
        self.assertEqual(bridge.line_count, 1)
        print("✓ Bridge model initialization works")
        
    def test_line_count(self):
        """Test line counting"""
        bridge = Bridge(content="Line 1\nLine 2\nLine 3")
        
        self.assertEqual(bridge.line_count, 3)
        print("✓ Bridge line counting works")
        
    def test_empty_content(self):
        """Test empty bridge content"""
        bridge = Bridge(content="")
        
        self.assertEqual(bridge.line_count, 0)
        print("✓ Bridge handles empty content")


class TestConfig(unittest.TestCase):
    """Test configuration management"""
    
    def test_cfg_singleton(self):
        """Test CFG is singleton"""
        cfg1 = CFG
        cfg2 = CFG
        
        self.assertIs(cfg1, cfg2)
        print("✓ CFG singleton works")
        
    def test_default_values(self):
        """Test default configuration values"""
        self.assertEqual(CFG.TODO_CHECK_INTERVAL, 30)
        self.assertEqual(CFG.CLAUDE_COMMAND, "/Users/<USER>/.claude/local/claude")
        self.assertTrue(CFG.AUTO_CREATE_DAILY_NOTE)
        print("✓ CFG default values correct")


class TestTodolistMonitor(unittest.TestCase):
    """Test todolist monitoring"""
    
    @patch('src.NotePlanBot.todolist_monitor.Path')
    def setUp(self, mock_path):
        """Set up test fixtures"""
        mock_path.return_value.exists.return_value = True
        self.monitor = TodolistMonitor()
        
    def test_monitor_initialization(self):
        """Test monitor initializes"""
        self.assertIsNotNone(self.monitor.bridge_file)
        self.assertEqual(self.monitor.check_interval, 30)
        print("✓ TodolistMonitor initialization works")
        
    @patch('src.NotePlanBot.todolist_monitor.TodolistMonitor._read_bridge_file')
    def test_check_for_changes_no_change(self, mock_read):
        """Test detecting no changes"""
        mock_read.return_value = Bridge(content="Same content")
        self.monitor.last_bridge = Bridge(content="Same content")
        
        changed, bridge = self.monitor._check_for_changes()
        
        self.assertFalse(changed)
        print("✓ No change detection works")
        
    @patch('src.NotePlanBot.todolist_monitor.TodolistMonitor._read_bridge_file')
    def test_check_for_changes_with_change(self, mock_read):
        """Test detecting changes > 1 LOC"""
        mock_read.return_value = Bridge(content="Line 1\nLine 2\nLine 3")
        self.monitor.last_bridge = Bridge(content="Line 1")
        
        changed, bridge = self.monitor._check_for_changes()
        
        self.assertTrue(changed)
        print("✓ Change detection works")


class TestIntegration(unittest.TestCase):
    """Test full integration scenarios"""
    
    @patch('src.NotePlanBot.reader.noteplan_reader.NotePlanReader.read_note')
    @patch('src.NotePlanBot.editor.noteplan_editor.NotePlanEditor.update_note')
    def test_calendar_workflow(self, mock_update, mock_read):
        """Test full calendar workflow"""
        # Initial calendar content
        mock_read.return_value = "09:00 Team standup\n- [ ] Prepare demo"
        
        reader = NotePlanReader()
        editor = NotePlanEditor()
        
        # Read calendar
        content = reader.read_note("20250714.txt")
        events = reader.extract_events(content)
        tasks = reader.extract_tasks(content)
        
        self.assertEqual(len(events), 1)
        self.assertEqual(len(tasks), 1)
        
        # Add new event
        editor.add_line_to_note("20250714.txt", "14:00 Client meeting")
        
        # Complete task
        editor.complete_task("20250714.txt", "Prepare demo")
        
        print("✓ Calendar workflow integration works")


if __name__ == "__main__":
    print("Running NotePlan Integration Tests...")
    print("-" * 40)
    unittest.main(verbosity=2)