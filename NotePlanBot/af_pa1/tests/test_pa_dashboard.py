#!/usr/bin/env python3
"""
Test suite for PA Dashboard functionality
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pa import PersonalAssistant


class TestPersonalAssistant(unittest.TestCase):
    """Test PA Dashboard functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.pa = PersonalAssistant()
        
    @patch('pa.GmailService')
    def test_gmail_initialization(self, mock_gmail):
        """Test Gmail service is initialized"""
        pa = PersonalAssistant()
        self.assertIsNotNone(pa.gmail)
        print("✓ Gmail service initialized")
        
    def test_noteplan_initialization(self):
        """Test NotePlan reader/editor are initialized"""
        self.assertIsNotNone(self.pa.noteplan_reader)
        self.assertIsNotNone(self.pa.noteplan_editor)
        print("✓ NotePlan reader/editor initialized")
        
    @patch('pa.GmailService.list_unread_messages')
    @patch('pa.GmailService.get_thread')
    def test_get_email_summary(self, mock_get_thread, mock_list_unread):
        """Test email summary generation"""
        # Mock unread messages
        mock_list_unread.return_value = [
            {'threadId': '123', 'id': '123'}
        ]
        
        # Mock thread details
        mock_get_thread.return_value = [{
            'subject': 'Test Email Subject',
            'from': '<EMAIL>'
        }]
        
        # Should not raise exception
        self.pa.get_email_summary(max_results=5)
        mock_list_unread.assert_called_once()
        print("✓ Email summary works")
        
    @patch('pa.Path.exists')
    @patch('pa.NotePlanReader.read_note')
    def test_get_today_schedule(self, mock_read_note, mock_exists):
        """Test today's schedule retrieval"""
        mock_exists.return_value = True
        mock_read_note.return_value = """10:00 Team meeting
- [ ] Review PR
- [x] Send update email
Regular text"""
        
        # Should not raise exception
        self.pa.get_today_schedule()
        mock_read_note.assert_called_once()
        print("✓ Today's schedule works")
        
    @patch('pa.Path.exists')
    def test_get_today_schedule_no_file(self, mock_exists):
        """Test schedule when no calendar file exists"""
        mock_exists.return_value = False
        
        # Should handle gracefully
        self.pa.get_today_schedule()
        print("✓ Handles missing calendar file")
        
    @patch('pa.Path.exists')
    @patch('pa.NotePlanReader.read_note')
    def test_get_upcoming_events(self, mock_read_note, mock_exists):
        """Test upcoming events retrieval"""
        mock_exists.return_value = True
        mock_read_note.return_value = "14:00 Client call\n15:00 Code review"
        
        self.pa.get_upcoming_events(days=3)
        # Should be called 3 times (for 3 days)
        self.assertEqual(mock_exists.call_count, 3)
        print("✓ Upcoming events works")
        
    @patch('pa.input', side_effect=['0'])  # Exit immediately
    def test_quick_actions_menu(self, mock_input):
        """Test quick actions menu displays"""
        # Should display menu and exit
        self.pa.quick_actions_menu()
        mock_input.assert_called_once()
        print("✓ Quick actions menu works")
        
    @patch('pa.input', side_effect=['today', '10:00', 'Meeting with team'])
    @patch('pa.Path.exists')
    @patch('pa.NotePlanReader.read_note')
    @patch('pa.NotePlanEditor.update_note')
    def test_add_calendar_event_existing(self, mock_update, mock_read, mock_exists, mock_input):
        """Test adding event to existing calendar"""
        mock_exists.return_value = True
        mock_read.return_value = "09:00 Daily standup\n11:00 Lunch"
        
        self.pa.add_calendar_event()
        
        # Should insert in time order
        mock_update.assert_called_once()
        updated_content = mock_update.call_args[0][1]
        self.assertIn("10:00 Meeting with team", updated_content)
        print("✓ Add calendar event to existing file works")
        
    @patch('pa.input', side_effect=['20250720', '14:00', 'Review session'])
    @patch('pa.Path.exists')
    @patch('pa.NotePlanEditor.create_note')
    def test_add_calendar_event_new(self, mock_create, mock_exists, mock_input):
        """Test adding event to new calendar"""
        mock_exists.return_value = False
        
        self.pa.add_calendar_event()
        
        mock_create.assert_called_once()
        self.assertEqual(mock_create.call_args[0][1], "14:00 Review session")
        print("✓ Add calendar event to new file works")
        
    @patch('pa.Path.rglob')
    @patch('pa.Path.read_text')
    def test_search_notes(self, mock_read_text, mock_rglob):
        """Test note searching functionality"""
        # Mock file paths
        mock_path1 = MagicMock()
        mock_path1.name = "Meeting Notes.txt"
        mock_path1.read_text.return_value = "Discussion about project timeline"
        
        mock_path2 = MagicMock()
        mock_path2.name = "Todo List.txt"
        mock_path2.read_text.return_value = "Review timeline for Q3"
        
        mock_rglob.return_value = [mock_path1, mock_path2]
        
        # Search for "timeline"
        self.pa.search_notes("timeline")
        
        mock_rglob.assert_called_once_with("*.txt")
        print("✓ Note search works")


class TestPAIntegration(unittest.TestCase):
    """Test PA integration with actual services"""
    
    @patch('pa.GmailService')
    @patch('pa.NotePlanReader')
    @patch('pa.NotePlanEditor')
    def test_full_initialization(self, mock_editor, mock_reader, mock_gmail):
        """Test full PA initialization"""
        pa = PersonalAssistant()
        
        # All services should be initialized
        self.assertIsNotNone(pa.gmail)
        self.assertIsNotNone(pa.noteplan_reader)
        self.assertIsNotNone(pa.noteplan_editor)
        print("✓ Full PA initialization works")


if __name__ == "__main__":
    print("Running PA Dashboard Tests...")
    print("-" * 40)
    unittest.main(verbosity=2)