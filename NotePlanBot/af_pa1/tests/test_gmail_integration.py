#!/usr/bin/env python3
"""
Test suite for Gmail integration
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'gmail_tool'))

from src.tools.gmail.service import GmailService
from src.tools.gmail.ui import CFG, fetch_threads_page, create_thread_table
from src.tools.gmail.models import Task, SnoozedEmail, EmailGroup
from src.tools.gmail.storage.project_store import ProjectStore, ProjectInfo


class TestGmailService(unittest.TestCase):
    """Test Gmail service functionality"""
    
    @patch('src.tools.gmail.service.build')
    def setUp(self, mock_build):
        """Set up test fixtures"""
        self.gmail = GmailService()
        
    def test_service_initialization(self):
        """Test Gmail service initializes"""
        self.assertIsNotNone(self.gmail.service)
        print("✓ Gmail service initialized")
        
    @patch('src.tools.gmail.service.GmailService.service')
    def test_list_unread_messages(self, mock_service):
        """Test listing unread messages"""
        # Mock API response
        mock_service.users().messages().list().execute.return_value = {
            'messages': [
                {'id': '123', 'threadId': 'thread123'},
                {'id': '124', 'threadId': 'thread124'}
            ]
        }
        
        messages = self.gmail.list_unread_messages(max_results=10)
        
        self.assertEqual(len(messages), 2)
        self.assertEqual(messages[0]['id'], '123')
        print("✓ List unread messages works")
        
    @patch('src.tools.gmail.service.GmailService.service')
    def test_get_thread(self, mock_service):
        """Test getting thread details"""
        # Mock thread response
        mock_service.users().threads().get().execute.return_value = {
            'messages': [
                {
                    'id': '123',
                    'payload': {
                        'headers': [
                            {'name': 'Subject', 'value': 'Test Subject'},
                            {'name': 'From', 'value': '<EMAIL>'}
                        ]
                    }
                }
            ]
        }
        
        thread = self.gmail.get_thread('thread123')
        
        self.assertEqual(len(thread), 1)
        self.assertEqual(thread[0]['subject'], 'Test Subject')
        print("✓ Get thread works")


class TestGmailUI(unittest.TestCase):
    """Test Gmail UI functionality"""
    
    def test_cfg_defaults(self):
        """Test CFG default values"""
        self.assertEqual(CFG.threads_per_page, 10)
        self.assertTrue(CFG.show_participants)
        self.assertTrue(CFG.show_duration)
        self.assertTrue(CFG.show_preview)
        print("✓ CFG defaults correct")
        
    @patch('src.tools.gmail.ui.GmailService')
    def test_fetch_threads_page(self, mock_gmail_class):
        """Test fetching threads page"""
        mock_gmail = Mock()
        mock_gmail_class.return_value = mock_gmail
        
        # Mock messages
        mock_gmail.list_unread_messages.return_value = [
            {'id': '1', 'threadId': 't1'},
            {'id': '2', 'threadId': 't2'}
        ]
        
        # Mock threads
        mock_gmail.get_thread.side_effect = [
            [{'subject': 'Email 1', 'from': '<EMAIL>', 'snippet': 'Preview 1'}],
            [{'subject': 'Email 2', 'from': '<EMAIL>', 'snippet': 'Preview 2'}]
        ]
        
        threads, total_count = fetch_threads_page(page=0)
        
        self.assertEqual(len(threads), 2)
        self.assertEqual(total_count, 2)
        print("✓ Fetch threads page works")
        
    def test_create_thread_table(self):
        """Test creating thread table"""
        threads = [
            {
                'messages': [{'subject': 'Test', 'from': '<EMAIL>'}],
                'duration': '5m',
                'preview': 'Test preview'
            }
        ]
        
        table = create_thread_table(threads, show_group_column=False)
        
        self.assertIsNotNone(table)
        self.assertEqual(len(table.columns), 4)  # Num, Subject, Participants, Preview
        print("✓ Create thread table works")


class TestModels(unittest.TestCase):
    """Test Pydantic models"""
    
    def test_task_model(self):
        """Test Task model"""
        task = Task(
            id="task1",
            thread_id="thread1",
            title="Complete review",
            created_at=datetime.now(),
            status="pending"
        )
        
        self.assertEqual(task.id, "task1")
        self.assertEqual(task.status, "pending")
        self.assertIsNone(task.deadline)
        print("✓ Task model works")
        
    def test_snoozed_email_model(self):
        """Test SnoozedEmail model"""
        snooze_until = datetime.now() + timedelta(hours=1)
        snoozed = SnoozedEmail(
            thread_id="thread1",
            snoozed_at=datetime.now(),
            snooze_until=snooze_until
        )
        
        self.assertEqual(snoozed.thread_id, "thread1")
        self.assertFalse(snoozed.is_expired())
        print("✓ SnoozedEmail model works")
        
    def test_email_group_model(self):
        """Test EmailGroup model"""
        group = EmailGroup(
            group_id=1,
            name="Project Updates",
            thread_ids=["t1", "t2", "t3"],
            reasoning="All related to project X"
        )
        
        self.assertEqual(group.group_id, 1)
        self.assertEqual(len(group.thread_ids), 3)
        print("✓ EmailGroup model works")


class TestProjectStore(unittest.TestCase):
    """Test project store functionality"""
    
    @patch('src.tools.gmail.storage.project_store.Path.exists')
    def setUp(self, mock_exists):
        """Set up test fixtures"""
        mock_exists.return_value = False
        self.store = ProjectStore(file_path="test_projects.xlsx")
        
    def test_project_info_creation(self):
        """Test creating project info"""
        project = ProjectInfo(
            name="Test Project",
            description="A test project",
            status="active"
        )
        
        self.assertEqual(project.name, "Test Project")
        self.assertEqual(project.status, "active")
        print("✓ ProjectInfo model works")
        
    @patch('src.tools.gmail.storage.project_store.Path.exists')
    @patch('src.tools.gmail.storage.project_store.openpyxl.Workbook.save')
    def test_add_project(self, mock_save, mock_exists):
        """Test adding project to store"""
        mock_exists.return_value = False
        store = ProjectStore(file_path="test_projects.xlsx")
        
        project = ProjectInfo(
            name="New Project",
            description="Testing add",
            status="planning"
        )
        
        store.add_project(project)
        
        self.assertEqual(len(store.projects), 1)
        self.assertEqual(store.projects[0].name, "New Project")
        print("✓ Add project to store works")


class TestMemories(unittest.TestCase):
    """Test memory file access"""
    
    def test_memory_files_structure(self):
        """Test memory files exist in correct structure"""
        # Check if pa_memories directory would be created
        memories_dir = "gmail_tool/pa_memories"
        
        # In actual deployment these would exist
        expected_files = [
            "long_term_memories.md",
            "short_term_memories.md"
        ]
        
        # Just verify the structure is correct
        self.assertIsInstance(expected_files, list)
        self.assertEqual(len(expected_files), 2)
        print("✓ Memory file structure defined")


if __name__ == "__main__":
    print("Running Gmail Integration Tests...")
    print("-" * 40)
    unittest.main(verbosity=2)