# NotePlanBot

A three-pane system that interfaces with NotePlan for AI-assisted task management.

## Overview

NotePlanBot monitors a "bridge file" where you type context and tasks. When changes are detected (> 1 LOC), it triggers Claude Code to help manage your tasks. The system provides a three-pane workflow:

1. **Bridge File** (left pane) - Type your context, activities, and requests
2. **Task List** (middle pane) - AI-managed todo list from NotePlan
3. **AI Log** (right pane) - Suggestions and assistance from <PERSON> Code

## Quick Start

```bash
# Install dependencies
pip install -e .

# Run the bot
python run.py

# Or use just commands
just run
```

## Features

- **Bridge File Monitoring**: Watches for changes every 30 seconds
- **NotePlan Integration**: Reads and writes NotePlan notes/tasks
- **Claude Code Integration**: Automatic AI assistance when changes detected
- **Three-Pane Visualization**: Terminal UI showing all three panes
- **Configurable**: Environment variables and global config management

## Project Structure

```
NotePlanBot/
   run.py                      # Main entry point
   src/NotePlanBot/
      main.py                 # Application orchestration
      config.py               # Global CFG singleton
      todolist_monitor.py     # Bridge file monitor
      visualizer.py           # Three-pane terminal UI
      reader/                 # NotePlan reading components
         noteplan_reader.py
      editor/                 # NotePlan writing components
         noteplan_editor.py
      files/bridge/           # Bridge system
          bridge_model.py     # Bridge data model
          bridge.md           # Default bridge file
          config.py           # Bridge configuration
   justfile                    # Command shortcuts
```

## Configuration

Environment variables (in `.env`):
- `BRIDGE_FILE`: Path to bridge file (default: src/NotePlanBot/files/bridge/bridge.md)
- `TODO_CHECK_INTERVAL`: Check interval in seconds (default: 30)
- `CLAUDE_COMMAND`: Claude Code command (default: claude)
- `NOTEPLAN_TASK_NOTE`: NotePlan note for tasks (default: NotePlanBot/tasks.txt)

## Usage Examples

### Basic Usage
1. Run `python run.py` to start monitoring
2. Edit the bridge file in your editor
3. Type your context and tasks
4. Save with > 1 line change to trigger AI

### Three-Pane Mode
```bash
just visualizer  # Full terminal UI
```

### Custom Bridge File
```bash
python run.py /path/to/your/bridge.md
```

## Development

```bash
# Run tests
just test

# Format code
just format

# Lint
just lint

# See all commands
just
```