#!/usr/bin/env python3
"""
Hiring Research Script - Uses Claude Code to research candidates

This script prompts Claude <PERSON> to find comprehensive information about a candidate including:
- Basic research and background
- University and ranking (with UK equivalent)
- Stand-out achievements
- Impressive logos/companies
- Best bullet points for roles (e.g., built X that made $10m or 100m users)

Usage:
    python research_candidate.py "<PERSON>" --linkedin "https://linkedin.com/in/johndoe"
    python research_candidate.py "<PERSON>" --company "TechCorp" --role "CTO"
"""

import argparse
import subprocess
import json
import os
from datetime import datetime
from pathlib import Path


def create_research_prompt(name, linkedin=None, company=None, role=None, additional_info=None):
    """Create a comprehensive research prompt for <PERSON>"""
    
    prompt = f"""Research the following candidate comprehensively: {name}

REQUIRED INFORMATION TO FIND:

1. BASIC INFORMATION:
   - Full name and current location
   - Current role and company
   - LinkedIn profile: {linkedin if linkedin else 'Find their LinkedIn profile'}
   - Email if publicly available
   - Personal website/blog if any

2. EDUCATION:
   - University/universities attended
   - Degrees obtained (Bachelor's, Master's, PhD)
   - Year of graduation
   - Major/field of study
   - University ranking (global ranking and UK equivalent - e.g., "equivalent to UK Russell Group" or "similar to Imperial College")
   - Notable academic achievements (honors, scholarships, research)

3. PROFESSIONAL EXPERIENCE:
   - Current company: {company if company else 'Identify current company'}
   - Current role: {role if role else 'Identify current role'}
   - Previous companies and roles (focus on impressive logos)
   - Years of experience in each role
   - Career progression speed

4. STAND-OUT ACHIEVEMENTS:
   - Products built/launched
   - Revenue generated (e.g., "built feature that generated $10M ARR")
   - User metrics (e.g., "scaled platform to 100M users")
   - Technical innovations or patents
   - Published papers or significant contributions
   - Speaking engagements at major conferences
   - Awards or recognition

5. TECHNICAL/DOMAIN EXPERTISE:
   - Primary technical skills
   - Programming languages/frameworks
   - Domain expertise areas
   - Open source contributions
   - Technical writing/blogging

6. BEST BULLET POINTS FOR ROLES:
   Create 3-5 compelling bullet points that would make them stand out for senior tech roles, focusing on:
   - Quantifiable impact (revenue, users, performance improvements)
   - Leadership experience (team size, scope)
   - Technical achievements
   - Business outcomes

{f'Additional context: {additional_info}' if additional_info else ''}

FORMAT: Structure the findings in a clear markdown format suitable for our CRM system. Be specific with numbers, dates, and achievements. If information isn't available, note what couldn't be found.
"""
    
    return prompt


def run_claude_research(prompt, output_file):
    """Execute Claude Code with the research prompt"""
    
    # Create a temporary file with the prompt
    temp_prompt_file = Path("/tmp/candidate_research_prompt.txt")
    with open(temp_prompt_file, 'w') as f:
        f.write(prompt)
    
    # Run Claude Code
    try:
        # Using the claude command directly
        cmd = f"claude < {temp_prompt_file}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Save the output
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Parse candidate name for filename
            with open(output_file, 'w') as f:
                f.write(f"# Candidate Research Report\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(result.stdout)
            
            print(f"✅ Research completed! Saved to: {output_file}")
            print("\n--- Preview ---")
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
            
        else:
            print(f"❌ Error running Claude Code: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
    finally:
        # Clean up temp file
        if temp_prompt_file.exists():
            temp_prompt_file.unlink()


def main():
    parser = argparse.ArgumentParser(description="Research candidates using Claude Code")
    parser.add_argument("name", help="Candidate's name")
    parser.add_argument("--linkedin", help="LinkedIn profile URL")
    parser.add_argument("--company", help="Current company")
    parser.add_argument("--role", help="Current role")
    parser.add_argument("--info", help="Additional information")
    parser.add_argument("--output", help="Output file path (default: auto-generated)")
    
    args = parser.parse_args()
    
    # Generate output filename if not provided
    if not args.output:
        name_slug = args.name.lower().replace(" ", "_")
        output_dir = Path(__file__).parent / "Leads"
        output_dir.mkdir(exist_ok=True)
        args.output = output_dir / f"{name_slug}_research.md"
    
    # Create research prompt
    prompt = create_research_prompt(
        name=args.name,
        linkedin=args.linkedin,
        company=args.company,
        role=args.role,
        additional_info=args.info
    )
    
    print(f"🔍 Researching: {args.name}")
    print(f"📝 Output will be saved to: {args.output}")
    print("🤖 Running Claude Code research...\n")
    
    # Run the research
    run_claude_research(prompt, args.output)


if __name__ == "__main__":
    main()