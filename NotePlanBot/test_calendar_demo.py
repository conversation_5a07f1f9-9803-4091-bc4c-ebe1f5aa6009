#!/usr/bin/env python3
"""
Demonstrate calendar functions
"""
import sys
sys.path.insert(0, 'src')

from datetime import datetime
from NotePlanBot.reader.noteplan_reader_calendar import NotePlanCalendar

# Create calendar instance
cal = NotePlanCalendar()

# Today's date
today = datetime.now()
today_str = today.strftime("%Y%m%d")

print(f"📅 Adding events for today ({today.strftime('%A, %B %d, %Y')})")
print("-" * 60)

# Add some test events for later today
events = [
    ("19:30", "Code review with team"),
    ("20:00", "Dinner break"),
    ("21:00", "Evening planning session"),
    ("22:00", "Review tomorrow's agenda")
]

for time, description in events:
    result = cal.add_calendar_event(today_str, time, description)
    print(f"✅ {result}")

# Now let's read back today's calendar
print(f"\n📋 Today's Calendar Entries:")
print("-" * 60)

entries = cal.get_calendar_entries(start_date=today, end_date=today)
if today_str in entries:
    data = entries[today_str]
    print(f"\nEvents for {data['day_name']}:")
    for event in data['events']:
        print(f"  {event}")
    
    if data['tasks']:
        print(f"\nTasks:")
        for task in data['tasks']:
            print(f"  {task}")
            
    if data['checkboxes']:
        print(f"\nCheckboxes:")
        for checkbox in data['checkboxes']:
            print(f"  {checkbox}")

# Create an iCal event for sharing
print(f"\n📤 Creating iCal format for the code review:")
print("-" * 60)
ical = cal.create_ical_event(today, "19:30", "Code review with team", duration_hours=0.5)
print(ical)

# Note about guests
print(f"\n⚠️  Note about guests:")
print("-" * 60)
print("NotePlan's file format doesn't natively support guest lists.")
print("To add guests, you can:")
print("1. Add them in the event description: '19:30 Meeting with @john @sarah'")
print("2. Use the MCP Google Calendar integration for full guest support")
print("3. Export to iCal and import to a calendar app that supports attendees")