OK - Emailing CEO a debrief post-meeting. Transcript below. Deck below. Asks are below that. We must make ALL the asks.

TRANSCRIPT
-   1
    
-   2
    
-   3
    
-   4
    
-   5
    

Find and replace with Cmd + F

-   I'm starting to wonder if you're showing up, <PERSON>.
    
-   Been 30 seconds late. Does he? Doesing?
    
-   That is backwards, <PERSON>.
    
-   <PERSON> and know <PERSON>.
    
-   Curious. Okay. It's because I have two cameras with the same name. Yeah, well, let's put that away. How am I looking? Am I Deb now?
    

-   Why is it sometimes one way and sometimes the other super confusing?Right. So that's now the correct way around, is it?
    
-   That's the correct way around. But that's the part of intelligence.
    
-   See what you mean? I don't... Do you really mind which one of these do I like the most? Go on, <PERSON><PERSON>, I finish of this list. <PERSON>, by the way, said it was a great line. What was it?
    

-   01:54
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    <PERSON> I'm currently in London now having.
    
-   Will. Hi, <PERSON>. Nice to meet you again. Are you in New York? Yeah, you all dressed up in a shirt and not, like, in your dungeon with all those red lights. I thought you must be in the Apollo office.
    
-   02:11
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    Alex
    
-   02:12
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ALV-UjWfMv94OJMUkQQc5d60yMT3RcVBrxoTeD8cp7hrSyJbCASF2xU=s100.jpeg)
    
    Will
    
    We were very... We're very nearly in New York this week. We're trying to book a workshop, but with Apollo. But we didn't get a schedule this week, so maybe.
    

-   02:27
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    Alex
    
    Yeah, but you'll be very pleased to know that I said to Will, "Let's just go in anyway." Because I think we haven't had a meeting with Matt in six months. It's worth it. And he goes, Alex, we cannot spend $4,000 so that we get to have an in-person meeting with Matt, can we?
    
-   You cannot, and I am in San Francisco next week, so do not go next week to meet with Matt because Matt is.
    
-   02:50
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    Alex
    
    So \[laughter\] is he not coming today?
    
-   ...\[Laughter\]... So please, he's coming now?
    
-   03:02
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    Alex
    

-   03:03
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    Alex
    
-   I was just saying if you go to New York to meet with Matt Fitzpatrick, it's a bad idea. Just because he's not... \[laughter\] is not going to be three.
    
-   03:13
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    Alex
    
    Well, we are in London. I've gone into a good rhythm with Ing. I've got my airline. I know which terminal it goes from. I know how to get from my house to the plane in an hour and a half. I've got my timings down on how to sleep on that plane so I don't lose any time. I'm feeling pretty smooth, but it's not as productive as here.
    
-   No, that's good. So I guess you'll see all the revenue grow. How smooth will it grow as smoothly as your schedules have gotten in?
    
-   03:47
    
    ![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)
    
    Alex
    
    It. my schedule gotten smooth or has it gotten loose?
    
-   Now as my schedule has gotten smooth, you think the revenue is going to grow smoothly up like this or.
    

-   The target is that there's nothing smooth about the increase in the revenue.We've been asked if we're able to be four times the size of the engagement. So trying to put together a plan that extracts that gets the billables as high as possible without actually leading to a total legion of people working on the project.
    
-   Yeah. The one thing that I have realized with, at least my experience with Apollo over the last year as I've seen them work, is that they continue to want to do these 200 to 50Kengagements. I know Marshall at this point feels like he's going to break through that. I don't know what your read on that is. Does it feel like we're in there ready to pay for it? It feels like he wants more work. Haven't felt the "I want to pay you more for that work" move in the same rhythm as I need more done.
    
-   Yeah. So, as far as I can tell, yeah. They've told us they have a 200 million budget for innovation. There are fewer and fewer other companies involved in this. We know Pallant was previously involved,and they're not so they're very much on the wavelength that they do need our help to get all they need someone offering a product like ours to get where they need to go, and we are on a dwindling list of people who might be looked upon to provide that.I think the question is, how do we loop in the platform so that we can change the business model into something that builds substantially more than just the 40% gross margin? Gross margin on those people.
    

-   And got a lot of ideas on how we can do that, but at the moment, we do at least have very loud, very clear signals that they want to expand the scope of what we're doing and pay for more people.Marshall is currently running those pricing proposals past.
    
-   Yeah, and I've got... What was I going to say?
    
-   Yeah. Yeah. I checked about it. Go. A1.
    
-   I've gotten a number. I have a plan in place in terms of switching gears for myself sometime very soon. I feel like there is enough momentum with the people that are helping in terms of just the R&D direction. The constant thing in Invisible is getting anyone too focused on anything for more than three weeks.Strangely, housing ourselves within Apollo has worked extremely well for this. It's some combination of the Marshall Effect where people don't try and come and steal people and having an important client with lots and lots of problems.
    
-   Nobody wants to talk to Marshall, Alex except for the two of you. I think we have very few people who willingly will go talk to Marshall. I can count on my hand a number of people who willingly go have a conversation with Marshall.
    
-   Yeah. So, I've heard this from several people. Casper is like, "I love that man. He saved us so many times." Whether exactly what kind of leader he is and when question for post-war I sent really seen it like it was a few times he's like got really snappy with me and then I've been like, "Offended," would just be like, "I'm not accepting this."Then later he's come back and just apologized really nicely, and I've asked.
    
-   He's very good at that. He's good at that.
    

-   Good at completely snapping in the moment and losing it, and then thirty minutes later, coming back with, "I'm sorry. I lost my temper."My point stands, so to speak, and let's just keep moving on. It's his ammo, but it works and it works in most cases. As long as it works for you, I like it because it doesn't work on a lot of people. A lot of people get really riled up with him.So as long as it works for you, it's great.
    
-   As long as it's earnest, it works for me. If it's a strategy, then.
    
-   I don't think it's a strategy. It's just that the minute he gets really angry, he may or may not have control over how he reacts to a situation. He realizes very quickly after that he's reacted badly,but that reaction turns more people off than not. They don't really care for how he comes back from it.
    
-   Yeah, I see that he's not the founder, co-founder, whatever you want to call it.
    
-   Telling him he's not a twenty or twenty-something where he can go and yell at people, but someday he'll learn.
    
-   Of a thirty-person organization with a hundred freelancers anymore. No.
    
-   Know we didn't set up this call.
    
-   And people do not feel like they can.
    
-   Yeah, go ahead. You know, the idea behind this call. And math, I think is going to show in, like, a bit. But the idea behind this call was.I know in the past we've like, chatted a couple of times about the two you actually hiring maybe a group of three to five folks primarily to focus on, like, sort of a great timing. Matt.
    
-   How are we doing? What thanks? I heard you guys have been doing magical things at Palo pallo.
    

-   In front of and behind the scene.
    
-   You haven't spoken like they were missing having chatted with you, they said.
    
-   Really? Well, I listen to you. You're always... Well, I don't do one-on-ones, but if you want to set up time to talk about Apollo, I'm always happy to do that. That's the only way I can moderate between everyone in the company wanting to do one-on-ones with me and the company. I'm happy to do that.I've been talking to Alexia Bunch. I think we actually might get Harvard Management on the back of some of this stuff, which is pretty cool. But a lot of what you guys have done on prompting and stuff like that is really interesting as a broad use case in Asset Engine.So, yeah, I'm sorry.
    

-   We have not chatted enough. I'll put it that But.
    
-   No, I mean, it's been on us. I started off sending out day emails, and then I was like, "Hang on. My job is to do experiments, and what I'm doing is writing myself a story where things are going well."Experiments are not about things going well. They're about getting it wrong. I'm getting it wrong, and still, here we are. How long have we got? We've got like 20 minutes left. We didn't want to spend the time doing your presentation, but we did just want to have some structure and just race through a surprisingly large amount of information.
    

-   Just race through some slides and then we won't cover most of what's in them.So we haven't quite finished this memo anyway. Then we can send it afterwards and then leave most of the time.
    

-   I want you guys to hire some people.
    

-   That's the deliverable coming out of this: tell me who you want to hire.
    

-   Yeah, I think who you want to hire.
    

-   And like, I would just add that we've. I think you've been doing a lot of writing and experimenting and thinking about it in that way. But how do we actually get you to write something that's not client-specific but a little bit more generic that we could potentially push outside? That's the other ask that I would have as you start thinking about some of the folks that you bring in. There's a lot of good work that's happening, a lot of good work that I would want Invisible to be known for, but we're just not pushing out enough.
    

-   Maybe we are at a product that just sits next to you guys and captures your brains and does a product. I mean, I don't know, you tell me, but that might mean going.
    
-   Well, hopefully we've got a few examples and things like that.
    
-   Yeah, I mean, yeah, let us we have been only doing narrow stuff to get to general stuff because, yes, there is no point doing anything else.
    

-   Well, it's not ento, but you get me.That's stop one. I have not shown my screen in a little while, so something is almost certainly going to go wrong or something strange is going to happen.
    

-   What we got, let's make this go over here. Okay, can you see my screen and nothing else? Good, then do not disturbwe are good. Okay, so high-level overview with AGM, they currently... When I originally turned up, I said, I think this should just be good on the ground research, stuff we can learn. I went to hypotheses.If some of those hypotheses are just wrong, if there's something about the way enterprises work that I'm missing, all of this is for naught. So that's great. However, that's gone really well. I think we've really impressed them. They originally wanted some junior prompt engineers, butwe just brought in really good talent, and we've been scaling up a bunch of junior people, and they want to hire and scale. As far as it does seem like that is the case, they are very positive, and they have a lot of money.
    

-   We'll come back to it. We've got two perspectives, silverets, which is a bit exaggerated, but that's what they're aiming to be. They're aiming to be like...If this works, this could be something that might enable us to enhance our existing teams to be able to dominate a market, not just do well. Then we have some asks which we can come to. I think we can... This one. This slide is obviously incomplete.
    
-   Yeah, I think the key thing that's interesting about Apollo as a case study is that we know they've got a 200 million dollar budget for innovation. We're talking about how we can scale up our engagement to four or five million a year now.But obviously, we need to find a way to capture a substantial chunk of that, which is 200 million. That is replicated across all of financial services, and any individual problem that we come up against, we're able to solve.But in order to realize all of the potential here in a couple of years, we need to get dramatically better at going into an organization and discovering what actually are the workflows that exist there.We've spent months now with Apollo slowly moving through different teams.
    

-   And be honest, it's still the tip of the iceberg, and they know that. So, the key aspect here is how we can find a way to get an FDA on the ground, able to very rapidly discover a problem, provide a solution, move on to the next one, driving themselves deeper and deeper into the organization to do so.
    
-   And really mining and building a queue like we have. We've found that even this is a very rare company, or I don't know how rare it is, but they have a sea level prerogative to automate the company, to automate as much as possible, which I think is less about saving money and more about this is the next big thing in private equity. We have to master it. I'm not...But yeah, the cards are at their chest. So we have this approach, which is they will just hire people that we suggest at this point. That's just roughly where we're at. So we can hire people, we can suggest them, they're going to say yes to most of them. It might even not even interview most of them, and then we can charge them for them.Then we want to move towards a transformation non-billable model. That requires the tech that we're working on, that CONNECTNEXES. Those FDS for productivity.
    
-   What I would say is I would encourage you guys to think less about the contracting structure with Apollo because I think we have a lot of ways to make that work. I'd encourage you to think less about the FTE model in the sense that one of the things I think we're most interested in is actually using this VDI to run up remote FTEs, but you actually make our FTE motion infinitely scalable.I think we will get there and focus most on a lot of the cool stuff you've done on prompt seating and actually getting this stuff to work because that.
    

-   I'll actually show you guys a demo that I'm not sure you've seen.If we crack that, we can serve 50 people first. I've got some new mountain, which we kind of messed up in the past and was very interested in this former McKinsey. One black guy just took over there. Is there a... Harvard Management is very interested. StepStone, which is one of the largest allocators in front of it.But we have basically four to five P funds that are all looking at variance. The same thing that we could serve if we got that product thing correctly.
    
-   Interesting. I wonder if it has overlap. Okay, let's fly through this. I really want to have enough time to talk, to discuss why now and pain. I think this doesn't require sitting on it.We have good buy-in with the Sea Level executives.
    

-   Yeah, rapid revenue plan, hiring machine proven pipeline like we know. Hi. And really good. The goal here is finding really good people. Will enable. Very good at finding really good people. Even if that just means filtering from the existing hiring machine.That is fine, but we're going to go to our network because we just have.
    
-   Well, what I would say is more great engineers I will always hire full time. So, if you have people in your network, let's hire them. But I want a lot of these to come from the marketplace so that we don't have to... I think we will have a lot more like we have right now. 14 workshops or projects kicking off an enterprise. I don't want to have to hire three FDs every time we do a workshop.So, this is why the marketplace becomes so powerful, and I don't... Have you guys talked to Gordon Cly about what we're doing there?
    
-   Yeah, a fair bit. We did have a plan in place. I've been into this for a year, and I had a guy who was going to do it as well to do a prompt engineering series of... Can you automate the piece of work that you're doing using the tooling that we have? Challenges and just say if you can do it, we'll give you $3,000 and just use this literally as a talent dog whistle because our agency is weirdly filled with surprisingly smart people who are technical. It's really strange.
    
-   ITE elite engineers in our workplace, our computer vision team example like.
    

-   So what I would say is that what we're doing is rather than a motion of "hire 100", we're going to bring in as contractors to start and use VDI if they're good, then bring them on full time.But there's a way to do this. That is way less labor-intensive than doing this all full-time. That's what Marshall and I have been talking about for follow scaling on the DA.
    
-   Super cool. I really want to hear more about that. We won't spend too long on this, but just one example. I don't know if you've looked at PowerPoint generation companies; there are ten of them working on it.
    
-   I have spent an inordinate amount of time looking at this because I have built more PowerPoints than I care to admit, and I do not enjoy doing So.
    
-   Great. We should come back to this will as like we worked it together initially. Will is actually a better engineer than me and so just took over because it was harder than we thought. Significantly.And they're all not solving the Enterprise problem. The Enterprise Problem is not Graphic design. The Enterprise Problem already has the Graphic design done for you. It is filling out the correct template. Which we came up with an intermediary language for. How does an LM write in stuff that results in being on brand PowerPoint?Like I tested the main ten yesterday. As far as I can tell, this works better than all of them. I don't know why nobody else is doing this. Maybe they are. We haven't seen them, but this is just one thing that we've done we've found, and I think it's surprisingly, probably valuable.
    
-   Can we use this internally? Today, are you able to where?
    
-   Yeah, not today, Will. When do you want to let people use it?
    
-   I think a company-wide launch probably makes more sense when I can build it on top of the agency platform.
    

-   At the moment, like what? What was in the video? There is a sort of custom scaffold for it, but it's really just agents with a specific data model and a set of tools for them to manipulate slides.
    
-   You have a lot of very happy people in this company. If you can do it.
    
-   Yeah, and the cop Stratin team uses it, please. We pull a lot of slides together for many different reasons.
    
-   Yeah, it's like, in comparison to the other tools that are out there, it's more of a bottom-up approach. You've already got a process. There's a very clear deck structure that people want.How can you tell AI to just do this but with this data? There's quite a lot of demands for that across Apollo. The other aspects that have been quite important are reading Excel projects. LMS are very bad at that.If there's a well-structured table, they can do it okay by manipulating it with Python, but when it's a messy file that needs to be carefully checked and validated, et cetera, they're really quite bad.So that's probably the next thing we'll build. Quite a lot of stuff that we're building really will end up being for the start as something that we can post on the Agenic platform, but capabilities that stretch much more widely across anyone who's using that platform with clients.
    

-   I think, by the way, one comment on that. This is where my view in the AI era is that point solutions get destroyed, and the winners are the scale players because you can just offer more stuff. So that becomes something where we could go to Johnson & Johnson and be like, "By the way, we'll give this for free as a service, just as an agent, you can use your whole team if you use our knowledge management platform." Right there, we probably get half the asset management customers who want to do it.
    
-   I think you'll have specialists that come in on every single problem, and then the foundation models just randomly get good at that one thing.
    
-   Yeah, exactly. If you're a company that does 100% of what you do in PowerPoint generation, you're going to get destroyed.But if you offer it as something you can do along with your other services, then you're going to get a lot of interest.
    

-   So one thing I can tell you is that of all the deliveries we've done, except for the Wills PowerPoint one, which was just surprisingly hard, good. 70% of the work has been done automatically.We even had a day where a junior COR engineer completed three deliverables, and I hadn't finished my first one. It was two days. At the end of the second day, I looked at that solution and it was better. Now, it wasn't fully automated. It got 70-80% of the waythere, and then they finished it off. But that's a lot of work that they didn't have to do, and it did it. That was two months ago, but this has been something we've been working on for a long time. This has been the PromptSwarm model breaker. There are all these different components. Synthetic test case generation has all been coming in this direction. When Aaron joined and started doing Agent Factory, it was like, "Great, we'll just focus on the feedback part of that system because somebody else is taking on the whole complex bear of building an actual agentic platform." Fantastic. We are just whimsically regarded as Cortex or requirements factory.
    

-   Exactly.Yeah. So the prefrontal cortex, in terms of the brain, it is extremely slow. His entire job is to tell stories and do entailment. Just one thing followed from another, and it can handle this really abstract thinking and causal thinking.But it's extremely slow. It takes a long time. If you were to count the amount of energy it uses for a task compared to the subconscious, it's incalculable. The example I'd like to give is evaluation. You start off trying to be creative, and you're thinking out loud, "How am I being creative? What are my ideas?"Then you're thinking out loud, "Are these good ideas or not? Why?" This is extremely tiring and frustrating. Then you go to sleep and you do this a few more times, and then before you know it, you're not actually doing the evaluation part. You're just having this intuition for whether or not that's a good idea. This is a good idea.That's a promising line of thought. It's just sort of happening. Then before you know it, you're flashing your head against the wall. Then you wake up in the shower three days later, and the idea just pops into your head because you have found the route of the solution, essentially trained. You can just hand that information to your subconscious, and then it is able to learn how to do it faster. Our argument.
    
-   Alex, I hate to interrupt. We only have about three minutes left, and I think it might be useful if we discuss exact hiring plans that we want to talk about.
    
-   Can I ask one question and show you one thing for you that.
    

-   Have actually curious, from the work you've done here, if I took the following. Let's say I have a series of documents and I query them, and I've built a pretty good underlying Verograph database or whatever it is I'm doing. I'm doing query and then I do prompt seating before I do the queries. What sort of lift do you get on the accuracy of the prompt good.
    
-   So this is asking a question of.
    
-   The data of the kind of unstructured data.
    
-   So I think, it really depends how you're going to be measuring quality. I think RAG, typically at the moment, is quite good at answering a singular question, for example, finding a reference in the data, surfacing that, what it falls down on quite badly is a review of a large amount of documentation stating pros and cons.Different documents, different viewpoints, disagree. So, by ceding prompts in that situation, you can essentially force different hypotheses to be checked and validated and scored.Unfortunately, there are great metrics you can pull out of your back pocket to say how much better it's going to be, but instead of just getting the AI to try and browse a huge amount of data.
    
-   Yeah, actually, I am convinced that this is one of the big... I think this is one of the things I think is most interesting about Apollo is that intuitively, it should be immaterial lift because it creates a reference point that is probably broadly accurate rather than a question that could be completely wrongif you do it right. If you have enough precedent prompts, you should at least get a query that will be broadly right most of the time.
    
-   Yeah, there's a whole thing called Zamic Fu shop, which is where you start. You start before you do training, fine-tuning. You just come up with examples that you vet, and then you simply add a router before any important query that says, "Of my examples that I have in my set, which three are most likely to cause me to have the right answer?"It should generally include two normal ones and then one if it is an edge case example of how that edge case was handled. This can drastically increase the last 5% of accuracy in terms of how you handle weird situations because you can access those. I want to really quickly... We are literally just at 7:30. The automated prompt engineer. The cortex is a riff off of the PromptSwarm system.We iterate on the prompt based on what we think a good prompt looks like. Then we generate test cases synthetically. Those test cases are then run. And then we have these generated graders, each of which is basically just asking a simple question based on the requirements document.If you have a requirement that says it should be like X, it should not be like Y. It should have LaTeX. It should not do Y. It definitely should have. Yes, this is the other thing. Each one of those is passed to an LLM that just has its sole responsibility of reliably answering the question and scoring. Did it do this thing? Did it not do this thing?If not, why not? If it has, why not? It passes that on to a feedback loop. And then the final thing of all of that is just being able to take everything back to the test case document, requirements document, and the instruction document for a complete loop.And then very quickly on this last one because I really don't get it out. This is not as far along, but using a combination of the stuff we did in NVIDIA and some other tools that we have picked up, we've been running experiments. Can we extract tasks from information like dense information about people doing work, for instance, taking a screenshot every one second or DOM capture or you hire a consultant and you get them to work simultaneously?You could hire a team of five, one of them is the manager, and four of them are not, and they just effectively simulate a piece of work. It very much looks like you can quite reliably do this with an LLM by doing mass Q&A by continuously doing confidence-type approaches.It's basically just video, and from that we can extract not just roughly how they did their task, but we can have lots of nested details if needed about the various ways we achieved it. We think there is just one really valuable potential thing on the table, which is that automatic automation. Probably we're not going to be able to get it further than, let's say, 70%.If it does, well, maybe it's 70% of the way there. A lot of the time it's not going to. You're just going to need a human to come in and finish it off. However, if you're buying a house and you refurbish it for $200,000 and you sell it for $400, that's worth $200,000 in value of information.If we consider a private equity fund or a CEO, do this with us, do this. Automation Guild tell your best people that if they get paid, they're going to get well paid, and we're going to watch them. They're going to describe what they do.But they are going to be paid.
    
-   I can do one better than that for you.
    

-   I'm going to just show you guys because I think part of why I'm asking you to hire more people is a lot.
    

-   This stuff I already have almost live, and we're going to try and figure out how to do it, and it would be good to have you guys involved in that.So, two particular ones I'll point you to is Jordan C. Lee. So, Silver Lake reached out last week. Silver Lake has a problem, which is... Again, this is really interesting how so many of these things converge repeatedly.Everybody said, "I have this identity verification business, and I tried to track within. Here's a problem it gets like 30% accuracy." We said, "Well, that's not that surprising because none of the information about identity verification exists in the Eleven."They're like, "Well, could you observe our people in the annotation platform and train a model of our people's behavior to replicate the decision-making process?" We literally are going to... You talk. Jordan Clely I think Sober likes to kick that off in like two weeks in a port go.
    
-   Perfect. That was. That was one of our asks. \[Laughter\].
    
-   Okay, so there you go. Then the second one, I'm going to show you. So we're going to kick off this PC and Harvard Management. The way the deal is going to work, I think, is they don't pay us very much now. They pay us some nominal amount. It becomes a pretty material feeing next year as they are... It is going to be a containerized version of our platform dropped in their premise.You guys probably haven't seen this yet, but this is the knowledge management system, particularly for wealth management, right? So, let's say the following. You get lots of different documents from all their managers. You can look at the summarization of the main documents they're getting in. You can look at returns analytics. Their big issue is they get all these fragmented, random documents from all managers. They get in 50 different formats that are 50 pages each, and they have no way to digest that. They told us it takes them... Because of the volume of their deals, several hundred people would be required to read all these to process it.So, they don't have a way to get the returns in order. So, stuff like this, the returns exposure in a structured format, is what they want to do. So, what we've talked to them about is things like a natural language interface, which managers have had good returns last quarter. One second, and I'll tell you what was interesting to them, which relates to the ability to have that natural language. Now, here's what hooked them that we went through. They tried to do this. LMS couldn't get out of accuracy. There was a mix of extraction formats being different, and everything else.We said to them, the issue is the actual labeling and annotation of your documents. The actual underlying source organization and the seeding of a couple requests you probably get repeatedly are the two issues. They said,"Well, that makes a lot of sense to us. If you can prove that for us, that's probably worth like a couple hundred grand to a million dollars a year just on that use case. By the way, we'd like to know..."Harbor management is the most important allocator in the United States right now. They're the most important player in their most important endowment in the world, probably. So, the head of AI is... We're going to kick this off in like two weeks.So, what I would say is you guys need to abstract yourselves out of Apollo. You can spend half your time in Apollo, but a bunch of your time on stuff like this.
    
-   I can do that. That's very similar to what we did at sites.
    

-   I technically have a payment pending on financial document extraction.
    
-   No, this is why I was like, "You guys are the ones..." So Alex had already pulled into Harvard Management, and he's super keen to do it. I want to Apollo to keep being a major focus because I think what you said earlier, Alex, everything should be solving small problems that become transferable. The fact that you're asking about tracking a workflow of human observation iswhat Silver Lake asked for. All of this is about building thousands of those reusable small flows and then multiplying them. But we need six to seven more people like you so that when you go away for two days, Apollo's not like, "You know where Alex and Will are..."So, what I would say is get our shell list of who you want, and I want to hire in the next two to four weeks. We're going to announce a very large fundraise in the next two to three weeks, which will probably make recruiting easier.So let's do that. Then let's pull you guys into some of these others.
    
-   Like more than a hundred million dollars.
    

-   It'll be around a hundred.
    
-   How \[Laughter\] Do you have some sort of godlike powers? \[Laughter\] Yeah.
    
-   How did we raise a hundred million off the creakiest revenue base in history? It's a great question, one I'll tell over beer at some point. But we did get there, so it's good.
    

-   Imagine how this valuation will be if, by the way, Apollo is so crucial to that.That's why a lot of... I told Marshall, "Do literally anything of all the wants" because V was like the most important customer reference we had in a lot of ways. That's why everything... I haven't fussed all about people doing whatever they want on a pal because they've been really good to us.But it's time for you guys to broaden your horizons because there's going to be a lot of cool stuff coming. I would say, right out of this, maybe shoot Jordan. Talk to Jordan about that Silver Lake thing.We should just do that. We should get that going because it would be helpful. Then on the Harvard thing, we should make... I'll add you guys to the team so we make sure you're involved in that and then give me some people you want toYeah.
    

-   We can hire our own people, and if Apollo can hire people, then we're good. We just it's we do.
    
-   Apollo can hire people. I think we've already started hiring people in Apollo. But if you need somebody more... yes.
    
-   Sorry, let me say those two things. The resourcing plan for Apollo is a separate thing that we're going to handle, and I've talked to Marshall about that. It's going to be a mix of the marketplace, but this... This isn't the "you guys" team not getting there is overlap between "guys".I want our research team that is doing this stuff to get bigger than you.
    
-   Yes, yeah, did we did Andrew Hicks it sounded like he converted, but what happened to him?
    
-   Yeah, I'm going to shoot him. Actually, as a good reminder, I think he's been... I'll shoot him an email. Now, he... I think he had some personal stuff going on.
    
-   Okay, because we had a very good report. We spent most of the half of the day at level,but then he... The last two emails he hasn't responded to. If it's personal stuff, that at least makes sense.
    
-   Yeah, he said, it's personal stuff, but I like what I'd say is if you guys have friends, if like if you want to open up job specs, like, let's get five to six people on your team. AS yeah.
    
-   Yeah, I want to hire one person. This person can be shared with Apollo, I'm shared with anyone else. Sunism. But I have absolutely brilliant, fine, exceptional people person.
    

-   Does that make sense? He's kind of like.
    
-   Okay, just put the name in a resume and give them an offer this week.
    

-   Matt. Exciting. I'm going to loop you in on those two emails on HMC and Super Lake.
    
-   That's pretty look forward to it.
    

-   Thanks guys. Excited sta talk soon see you.
    
-   Right it. Take care. Bye.
    

![New](MATT%20FF%20BIG%20ASS%20MEETING/svg%253e.svg)

![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocIwcWUYBkPqnjAzQH2v_iN9ni_7FbtovqIT3Nlr11iGaA1pO7s=s100.jpeg)

Alex

49%

![user](MATT%20FF%20BIG%20ASS%20MEETING/ACg8ocKUtQTbQmB02l5VRzNfTxqUpaZIftPXkYLk79OMn-sK9puu7Q=s100.png)

Matt

27%

![user](MATT%20FF%20BIG%20ASS%20MEETING/ALV-UjWfMv94OJMUkQQc5d60yMT3RcVBrxoTeD8cp7hrSyJbCASF2xU=s100.jpeg)

Will

17%

<EMAIL>

-------------------

ordan Cealy and testing DKE.
- [ ] Matt - how can we get feedback on observation?
- [ ] ∆ 10:00 - 11:00 **DRAFT EMAIL TO MATT WITH ASKS & SLIDES**
	- [ ] has anyone replied? Check Slack. Matt, Varsha, Will, 
	- [ ] Check email → two intros. Reply to those first.
	- [ ] Think 
	- [ ] Actual Draft: Meeting Debrief → Action Items (then also include details you missed.)
		- [ ] Follow-on email: Comp → Will. I've been putting this off. Yep. For, since February, because I very much do not enjoy negotiation and I'm very conflict avoidant in general. Comma, it's staggering really, comma. However, it's reaching a point where it is, where me not bringing this up is not just doing me a disservice, but it is also a problem for the company. I believe that I have added an exceptionally high amount of value strategically at this company. And also, Floyd, and Royal. Well, I've just loads of things in that. There's a dozen things that I could list. But I think even just for instance, I don't think without me, when you started entertaining, joining the company, there still would have been virtually zero AI infrastructure. And you may as a result of seeing nothing real not even have joined and saved us. Comma. Comma. Comma. Comma. Comma. Comma. Comma. Comma. Comma. Comma. Comma. Comma. Comma. Comma. I was offered 200k in January and I turned it down for my 135 base here. Because I started a story here seven years ago and now I want to finish it. But from all my charitable works and the two year break I took after I burned out. As well as very strongly needing a war chest. Ahead of whatever global geopolitical chaos may be about to come in the next five years. Comma. Dot dot dot. All this is to say. Full stop. Will and I are co-leads of the AIR&D team. Comma. I would like to be paid the same as well. Full stop. I would like to be fits into a whole lot.
- [ ] Work out what the tasks are and Time block them.
- [ ] AlphaSights Team (that aren't Josh)
- [ ] ASKS
	- [ ] Mgmt streamlining. I've been 3-4 weeks away from seriously streamlining my comms since Dec. This might sound ridiculous... but I think I could reduce time-on-slack & email by 30%. 

- [ ] Steve → 
- [ ] Martin article

# ASKS



# Vision Doc 1
Map, Territory, Diagram.  Diagram is the second Half of make no little plans. Korzybski → make maps, not opinions.

""

+ There were two version. Is this the version we liked and where is the other one?
+ Get the diagrams from ChatGPT for the Slides, link here.
+ Angela → who's the best person in your team to get thinking about how we could position this new tech I'm now really confident we're going to get working?
> What has changed since this version above is that I've started leaning much more heavily on the argument that [getting 70% of the way there (in a success scenario) is plenty to have confidence "this task can be automated". While getting to production grade (say 95% accuracy*) might be a tonne of work, if our system can reliably get to 70% by itself, that's enough to run it on thousands of tasks and give
> A) report on what can be automated now vs next year vs whatever → analysis something something.
> B) impact. "70% working" may well look surprisingly close to done without digging in. The scale of how many tasks this was done for... impact.
> C) Sales. Unique access to a big list of tasks and sub tasks, rich data for further QnA behind each.
> ... if we go with Apollo's idea of the spin-out... could you raise funds for a PE fund? Once we can prove it? Fund financials are excellent, no one else is doing "push for maximum automation using the degree of force that only an acquiring PE fund + Mckinsey is really capable of doing". But we have to kind of decide, because if I put the schematics on their VM, they'll have it.
> 70% → reframe as "the secret sauce: the 70% threshold".  x% = x% human parity in quality.
> The value of **THE ONER** . Vibe coding and even engineers iterating ad lib for speed to get to a solution is, I estimate, something like 100x less valuable a skill than Spec Driven Development. See below. This philosophy also applies to Task Automation. We could write a series of white papers over 3-6 months on this if we wanted to drive adoption within the org. Why? A spec that, from which, agents (+graders etc) can 
> Have cats explain it again. Program your own 'as if cats' using the prompt in the code.
> Re Process Mining: "that new sr guy, ex UI Path → yeah it never works, we just had it as a foot-in." → this is a credibility problem. The Proxy (Map|Territory?) should get us in though. Surprising detail and scale _before_ we get in.
+ Steve = expanding Apollo + helping me find the A* engineers in our network. There are many but time + will. Automation Guilds strategy was his idea. None of my strong recc. hires haven't been excellent. Will then need him to find the rare engineers if this grows quickly and we have to scale it.
+ If goes well. Be baller. OK. Serious question Matt. Financially, why shouldn't I go somewhere else? I'm pretty nervous asking this but it's just progressively bugging me more and more. My AGI timelines are short. I know what chaos is coming in the run up and I have my chosen family that I want to ensure are protected if/when that chaos comes. I spent my UHNW money recovering for 1-2 years after a personal tragedy in 2023. I was offered $200k pa in January and I turned it down when you offered the team and head of AI R&D....  Honest question: would you have joined Invisible if we had had circa zero AI tech? Because, and please don't share that I said this as I don't know who's narrative it might not fit with, but there's at least a dozen people who would I think happily admit that that's what you would have found if I hadn't taken on the ridiculously ballsy mission of pushing AI into the org. There was literally nada when I got here despite being told, and many people at all levels believing we had things we didn't. It was crazy challenging, and I had to do it without taking ownership of it.... Now I'm doing all this.... My bonus totals like $15k on $130k base. It just got boosted to $150... I have a *lot* of rough edges. But I think I do genuinely have a very rare knack for finding winning long-odds bets that I can just about pull off, then just about pulling them off.... I don't genuinely feel like real value /impact is what will lead to better comp. I can't not do the most important thing I can think of, it's a compulsion.
	+ Paid same as Will. Set some milestones, reduce it back if I miss them. (If up later, no, it's overdue.)
	+ I need a small exec team. Associate: One Oxbridge grad w 2 years somewhere strong + extremely high dopamine. I really struggle without this, losing 30-50% efficiency.
	+ Exec Prerogative
		+ **Will.** We could have built this in April when Will joined. He strongly felt that we needed to first bring in $$ or we weren't safe. OK so I took us into Apollo. Perfect recon. Will needs to know this is what he should do to be safe. For him, safety is what makes him want to take risks, he's not ADHD, and he's brilliant. Ambition is secondary.
		+ Domac & Marshall etc → I want to put in a rule. No LLM / agentic get's delivered that we didn't automatically create.
		+ Aaron → he'll happily take whatever is useful to him and adapt / improve, I know. Want to know I'm safe to just hand things over and you know we're behind there somewhere. If I take things straight to him, I'm not going to get credit the same way. I think you now have enough evidence to just trust me going forward. I'll keep finding more silver bullets, and this plan has a bunch more to it planned around FM advances as is. 
		+ **R&D Spec-Driven-LLM-Dev only**. David M and Domac are both supporters [check] of the idea to make a wild rule: No deliverable work where each package (module) wasn't a 'oner' - i.e., built in one go by agents following a spec. This skill is SO much more valuable 
	+ Steve → see above.
	+ 3 A* engineers.
	+ David Domac → need to pay him $120k. Insane he's on $70, was promised and we'll lose him eventually.
	+ Agentry Automation Guild Challenge to find more diamonds.
	+ [Wagemaker]([[Vision Doc 1#WAGEMAKER]]) → was made redundant whilst I was trying to poach him. Wasn't delivering enough but [notes on him delivering]... I'm very confident he'll deliver for me, concerned there's some political BS going on that I don't want to wade into / drag his name through the dirt for. We're pretty isolated and I'd have him focused just on our team.
	+ Team isolation. The constant distractions / being asked to do all kinds of random pieces doesn't work. Kills speed by >70%. Urgency just wins over 'hard important thing with minimal positive feedback until nailed it'. Not zero external work, but anchoring to say no. Steve & Wagemaker to protect.
	+ Revi → $1500 a month. Help closing partnership. They can adapt their agentic research machine for the proxy of **Territory**.
	+ **Britton** I've** spent $3k personally on software experiments I didn't have time to get approved. Special permission for R&D.... Also, this is going to spend a lot of tokens. Like, a huge number. Will need some Foundry/similar GPUs.
	+ Andrew Hicks + John Zhu + Ernest. Heavily Split Attention.
	+ David Melville → best engineer for this in the org. Paired with Will (focused), Domac and [JZ&Ernst hopefully] we can crush this.
	+ Should we pitch Alexei/Aaron/Kit to collaborate, and how without sharp elbows. **Do not want** that. Long story but violence and lies ruined a whole chunk of my life. Want to build. Team + tech + strategy.
	+ Junaid + Fine Tuning. Current plan doesn't hold technical water. But we can apply it here.
	+ Data Quality have been pouring through examples since Prompt Swarm in Feb → want them testing this with KPIs to hold accountable. **Need  many more real enterprise examples from SE/Sales. Ask them to get them. Not 'the hardest thing that org can't do' but 'everything they spend a lot of non-comms human hours on.**
	+ Caspar, Marek, Tom down to sell. Jenny will get it.
	+ New
		+ 10 hires a month + Steve
		+ 

### Karam 3
+ Get 'Foundations' from Achint and Karam as that was great. 
Instruction →    ←  FeedbackMost people's agentic loops are pure chaos
![image](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f4a1.png)
Record and datamine a human going from A→F on something valuable
Figure out how to get from A → F, automatically*, expensively
Re-run that a load of times and generate more and more feedback, automatically*, to reliably get A→F
Then reverse engineer [A→B, B→C, C→ D, D→E, E→F], automatically*
and use all those feedback signal and test cases to optimise the crap out of it and make it affordable automatically.
So long as A→F was anything approaching what we could describe as 'a human job' → boom. >100 thousand * that salary per annum in software value potential.What I **don't** get is why so few people are managing to achieve this. Bar complexity / whatever reason I keep finding myself re-doing the basics again beyond not having enough consolidated inspiration to hire my A team yet.I'm guessing we could actually pad the above out together, and you guys take whatever part of the dev makes sense.
We'll tee up with the agent factory team and get their latest code to make sure we'll end up compatible
but the POC just needs to demonstrate a system self-improving via building it's own self-improvement mechanisms.Simple, elegant, demonstrative.RL is a challenging to visualise algorithm, but the concept *isn't.*
threaded( loop(actor-instruction (prompts1), critic-feedback(prompts2), go astray, stop, reflect → rewind with slightly edited prompts or restart)
  ↳ multi-thread-reflect(scores, qual-review) → new strategy branches → new threads per branch
  ↳ reflect & prune branches to limit threads*as automatically as possible (edited)


# The Requirement Factory: Automating Task Discovery and Scaffold Implementation at (Serious) Scale
The goal was an enterprise pivot. Leveraging Alexei & Aaron, not duplicating. Concept _was_ Agent Factory, but actually Aaron and Will & my ideas didn't overlap that much. He's building the product.
## What We're Building: The ~Requirement~ Factory 
### Map. Territory. Contours. Scaffold. 
### Kernels → Oners → Gradient → Ascent ^8xbd1v
Map - not a compass. Compass points to north. 1000's of tasks, <two dozen months to adapt.
Terrotitry

> Draft to David (incomplete)
> What do you think of this angle?
> 
> We've discovered America. Invest in ships, ship factories or maps? Maps? Critically valuable, very scalable, but fungible and low willingness to pay
> 
> (1) Map: Knowing is worth much more than Doing per unit effort
> Efficiency from automation is of course very valuable. 1000's of
> But that's a lot of work. Even if 20% of white collar tasks could be automated up to 70% human parity quality (allow gross over-simplification for explanatory purposes)...
> Assuming 'production-usable' is 95% and thousands of task/subtasks + domain knowledge learning... that's a colossal body of work even just for one org.
> 
> 
> (2) Territory: 'The Map' is a massive competitive advantage.
> 
> (3) Contours:
> Scaffold
> 
> Fund
> 
> sketch a diagram of our proposed system (use notes sent to David) and then build in Pi scorers naively. Share these and state they are naive o3 guesses, the point is as an intuition pump. How should we do this? And how? I want us to avoid our previous pitfalls in meetings and keep things grounded in demos. Less 'what could be possible' and more 'how could we pull value from this right now without huge human feedback requirement?'









Domain Knowledge Extractor isn't just another automation tool - it's a methodology that answers the question every C-suite executive should be asking: "Which tasks that I'm paying people to do can be automated with today's technology, and how?"
Instead of waiting for clients to come to us with their automation ideas (which may or may not be feasible), we flip the script. We go in and systematically discover which tasks *should* be automated, break them down into subtasks, and provide a roadmap for implementation.

## The Problem We Solve
The automation industry has a dirty secret: the feedback cycle kills most projects before they deliver value.
```
Traditional Automation Timeline:
Day 1: Initial task description from client
Day 3: Client responds to clarification questions  
Day 7: First prototype delivered
Day 11: Client provides feedback (4 days delay)
Day 18: Second iteration
Day 25: More feedback (another week lost)
...
Multiply by 100 tasks = Project death
```
Scale this across 100 tasks in an enterprise, and you're looking at:
- 6+ months of discovery
- Incomplete data that misses critical edge cases
- Exhausted stakeholders who've lost interest
- Zero actual automation delivered
The core issue: people don't know how they do their jobs until they're doing them. Ask someone to describe their workflow, and you'll get maybe 60% accuracy. The remaining 40% - the edge cases, exceptions, and implicit knowledge - that's where automation fails.

## Our Solution: 
Generate, Don't ExtractWe've learned from the foundation model playbook. OpenAI, Meta, and others don't collect millions of examples - they collect thousands and synthetically extend them. We do the same for task automation.
Domain Knowledge Extractor works in two phases:
**Phase 1: Discovery**
- Deploy lightweight observation tools to capture how work actually happens
- Interview key personnel while they're performing tasks
- Generate comprehensive task maps showing:
  	- Core workflows
	- Decision points
	- Edge cases and exceptions
	- Data dependencies
	- Human judgment requirements
**Phase 2: Validation Through Documents**Instead of asking busy employees to spend 100 hours reviewing test cases (for work that might eliminate their jobs), we:
- Create simple, one-page requirement documents
- Use "if this, then that" declarative logic
- Present scenarios in familiar document format
- Get rapid feedback through document review, not testing
This approach compresses months of discovery into weeks, with higher quality output.

## The Bigger Vision: Requirements Factory
Domain Knowledge Extractor is the entry point to something larger. Over the past year, we've built complementary systems that now form a complete automation pipeline:
```
graph TD    A[Domain Knowledge Extractor] -->|Task Requirements| B[Prompt Swarm]    B -->|Generated Implementations| C[Eval Constellations]    C -->|Performance Metrics| D[Self-Optimizing Prompt Engineering]    D -->|Optimized Models| E[Production Automation]    E -->|Feedback| A
```
**Components Already Built:**
- **Automated Prompt Engineer**: Generates optimal prompts for discovered tasks
- **Prompt Swarm**: Creates diverse implementation approaches
- **Eval Constellations**: Comprehensive testing without human bottlenecks
- **Self-Optimizing Prompt Engineering**: Continuous improvement based on real-world performance (upgrading it even further to reward learning is what Andrew Hicks and David Karam will do for us)

## Why This Works Now
The basic feedback loop in automation is simple:
```
Human describes task → System attempts automation → Human provides feedback → Repeat
```
This loop fails because:
- Humans can't fully articulate their knowledge
- Feedback requires too much time/effort
- Edge cases emerge only in production
- Scale compounds all these problems
Our approach succeeds because:
- Generate comprehensive task understanding upfront
- Validate through efficient document review
- Use AI to simulate edge cases before production
- Create self-improving systems through automated feedback loops

## The Business CaseFor clients:
- **Discovery as a product**: Valuable reconnaissance even without automation
- **Clear ROI projections**: Know what can be automated before investing
- **Reduced risk**: Test automation feasibility before committing resources
For Invisible:
- **Premium positioning**: We find the opportunities, not just execute requests
- **Competitive moat**: Rich task data that improves our automation capabilities
- **Scalable growth**: Each client enriches our task understanding across industries

## The Feedback Factory: 
What Makes This All WorkThe thing missing is the detail around what I've been cooking on for 18 months: the feedback factory. Prompt swarm automatically makes reliable graders based on requirements. Generates NL test case strategy docs (1-2 pages of intelligently grouped "if this → that" assertions) for them automatically, then optimizes the grading prompt so that it fits. I gave it a silly name on purpose but this is very powerful. Originally it required human feedback on the outputs, but we also automated that pretty well…
This is so valuable because a grader factory unlocks a new paradigm of automation. Requirements become intelligent graders, not just that score but that give feedback.
1. Observe + decomposition → Extract hundreds of tasks per job.
2. Enrich via automated confidence-driven hierarchical QnA → infer requirements (dozens per task) and synthetic eval suites. (>100 test cases per requirement)
3. Prompt swarm → Generate graders from requirements. Auto-prompt optimize them to fit their test suites.
4. Automatically iterate prompts (agentic teams) to achieve their requirements. The graders (eval constellations) provide the reward signal, and qualitative feedback.
5. Human feedback is only required to update or add to the test case strategy docs.
Real jobs have hundreds of tasks involved. Enterprises have hundreds of jobs and need north stars to keep everyone sailing in the same direction. But teams need KPIs. It's often cloudy, it's often sunny, and the guys below deck can't even see the sky.
At the base of the pyramid, sure, the individuals need instructions.
The status quo approach to automation seems to imagine that we can train an entire navy's combined skillset, from scratch, by asking the handful of available scribes (who by the way haven't even worked as a deck hand) to manually write not just the job descriptions but the entire training manual. For every role, and every task. No experienced managers, no graduate program.
Insert fan out demo of task explosion for a navy:
- List every role in a navy
- For each role, list every skill and task required
- For each task, list requirements (correct = and incorrect = and "done well" =)
- Imagine you are writing idiot proof instructions for the worlds smartest 5 year old to be able to carry out the task better than a professional on the first try. Do this for every task and requirement.
AI drastically reduces the complexity however. They do not get tired, their cognition is ludicrously cheap, they don't secretly just want power, food and sex, they can communicate at the speed of light, and most importantly they don't require years of training / puberty as they can be copy and pasted.
If we can automate the feedback factory that steers one task to harbour, without cheating, we can do it for thousands.
Goal = destination
Requirements = north stars
Graders = KPIs/feedback
"X% of all work has already become an addressable market"

## Next Steps
Deploy Domain Knowledge Extractor with 2-3 pilot clients to:
- Validate the discovery methodology
- Build initial task corpus
- Demonstrate value of comprehensive task mapping
- Create case studies for broader rollout
The future of work isn't about replacing humans - it's about understanding what humans do so well that we can augment them effectively. Domain Knowledge Extractor is how we build that understanding at scale.


## DKE - features backlog
- [ ]   Ask David: using confidence ensembles, and impact assessment (on narrative, on p(success)), we can surface certain questions live to guild user. Notification, or just pop over text that fades away. They should speak the answer. Microphone reliability required. This allows us to gather exceptionally rich insight -IN THE MOMENT- which is very, very important for getting the reasons behind things. This can also train them to COT the useful stuff whilst demonstrating /working..
- [ ] Redacting will be a thing, not sure how much it matters though.


---
# A quick attempt at a visualisation for driving home the scale point.
# Task Explosion Visualization
## The Perception vs Reality Gap
### How We Think Jobs Work (4 Slices)
```
┌─────────────────────────────────┐
│         Sandra's Job            │
├─────────────────────────────────┤
│  Analysis (45%)  │              │
│                  │  Meetings    │
│                  │    (30%)     │
├──────────────────┼──────────────┤
│   Reports (20%)  │ Admin (5%)   │
└─────────────────────────────────┘
```

### How Jobs Actually Work (200+ Tasks)
```
┌─────────────────────────────────┐
│         Sandra's Job            │
├─────────────────────────────────┤
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
└─────────────────────────────────┘
Each line = a different micro-task
```

## The Engine Diagram
```
graph TD    subgraph "Input: Perceived Simplicity"        A[Job Description<br/>4-5 Major Categories]    end        subgraph "The Discovery Engine"        B[Observe & Decompose]        C[Task Extraction<br/>100s of micro-tasks]        D[Sequence Mapping<br/>Order matters!]        E[Quality Thresholds<br/>Good enough to proceed?]    end        subgraph "Output: Actual Complexity"        F[Task Map<br/>200+ interconnected pieces]        G[Requirements per Task<br/>Dozens each]        H[Test Cases<br/>100+ per requirement]    end        A --> B    B --> C    C --> D    D --> E    E --> F    F --> G    G --> H        style A fill:#f9f,stroke:#333,stroke-width:2px    style F fill:#9f9,stroke:#333,stroke-width:2px    style G fill:#9f9,stroke:#333,stroke-width:2px    style H fill:#9f9,stroke:#333,stroke-width:2px
```

## The Spectrum ProblemMarshall describes tasks as a spectrum where:
- Tasks aren't discrete categories but blended activities
- "Two reds and a blue, then three pinks" - you're constantly shifting between task types
- Sequential dependencies: Task A at 70% quality might block Task B entirely
- The order matters as much as the execution
```
Morning workflow reality:
[Email-Red] → [Analysis-Blue] → [Email-Red] → [Meeting-Pink] → [Analysis-Blue] → [Report-Pink] → [Email-Red]

Not:
[All Emails] → [All Analysis] → [All Meetings] → [All Reports]
```

## Tasks Support Projects Support Goals
```
graph BT    subgraph "Micro Level"        T1[Task 1: Check inventory]        T2[Task 2: Update spreadsheet]        T3[Task 3: Send notification]        T4[Task 4: Review threshold]    end        subgraph "Project Level"        P1[Project: Inventory Management]    end        subgraph "Goal Level"        G1[Goal: Optimize Supply Chain]    end        T1 --> P1    T2 --> P1    T3 --> P1    T4 --> P1    P1 --> G1        style T1 fill:#ffd,stroke:#333,stroke-width:1px    style T2 fill:#ffd,stroke:#333,stroke-width:1px    style T3 fill:#ffd,stroke:#333,stroke-width:1px    style T4 fill:#ffd,stroke:#333,stroke-width:1px
```

## The Bridgewater ParallelJust as Bridgewater shows asset classes that appear simple but contain massive complexity when decomposed, jobs have the same property:
**Surface Level**: "I manage investments"
**One Level Down**: "I analyze markets, meet with clients, prepare reports"
**Reality**: 200+ specific tasks like "Check Bloomberg for EUR/USD at 9:15am", "Update position sizing spreadsheet cell F47", "Email Jim about Tuesday's deviation", etc.
The key insight: **You can't automate what you can't see, and you can't see the real work until you decompose it to its atomic level.**


We can get pretty far with "good enough" automation.


Seventy percent automation can have a big impact.


This opens up sales opportunities.


We find tasks ripe for automation.


We break them into smaller pieces.


Then, we plan how to automate them.


The old way of finding automation tasks is slow.


It takes forever to get feedback.


Projects often die before they finish.


People don't always know how they do their jobs.


Important details get missed.


Our solution learns like big AI models.


We generate insights, not just collect them.


First, we watch how people work.


We interview them while they work.


We create maps of their tasks.


These maps show everything.


Then, we validate with documents.


These are simple, one-page documents.


They use "if this, then that" logic.


People review familiar documents quickly.


This makes discovery much faster.


We also get better quality results.


This is part of a larger system.


It's a complete automation pipeline.


We have automated prompt generation.


We create different automation approaches.


We test thoroughly without human effort.


Our system constantly improves itself.


The old feedback loop is broken.


People can't fully explain their work.
Image Generation: enabled.

We can get pretty far with "good enough" automation.


Seventy percent automation can have a big impact.


This opens up sales opportunities.


We find tasks ripe for automation.


We break them into smaller pieces.


Then, we plan how to automate them.


The old way of finding automation tasks is slow.


It takes forever to get feedback.


Projects often die before they finish.


People don't always know how they do their jobs.


Important details get missed.


Our solution learns like big AI models.


We generate insights, not just collect them.


First, we watch how people work.


We interview them while they work.


We create maps of their tasks.


These maps show everything.


Then, we validate with documents.


These are simple, one-page documents.


They use "if this, then that" logic.


People review familiar documents quickly.


This makes discovery much faster.


We also get better quality results.


This is part of a larger system.


It's a complete automation pipeline.


We have automated prompt generation.


We create different automation approaches.


We test thoroughly without human effort.


Our system constantly improves itself.


The old feedback loop is broken.


People can't fully explain their work.



## WAGEMAKER …
And am I on track, right? Up until being laid off, there were conversations about me moving into the VP role, doing this, doing that. That's not changed ever. There was a brief conversation around where does Matt sit, where does Victor sit in the DeepMind Google role, how do we pass things are. The only thing I can think of that has probably pissed someone off (being blunt right) is the GeoPay thing because that was they were pissing about about that excuse the language for about six to seven months and people were continuously asking including myself. 


All hands before the announcement of the new pace, late May, early late May. All hands, I know I push a bit too hard, and I know Saoirse got quite annoyed with me. Yeah, yeah, I remember that. 

What was it you had? Yeah, I just turned on my little voice to text, so you had like 3 words. You had 1500 agents, not 1500, um, 2 million revenue. Yeah, we did a couple of million revenue. We went and we scaled a gen take for DeepMind. Did more scoping for DeepMind. Went in person in San Francisco with the PoCs that I kept warm. No one else on DeepMind expanded that as well and then grew those relationships. And then beyond that, we were growing the scope for Boar Crow and Phoenix which were the three projects that we have. Simon's got three more and he asked me if we could take them on, and I said that we could. At no stage did I push back, and essentially, particularly Phoenix which is the agentic stuff, um, we brought back to life or I brought back to life. No one else excuse me, and that could be a couple of mil a month if it was to scale to the full extent. I brought in David to look at automation, I brought in the resources, Trevor, Uli, Theresa, Mayor, and all of them have reported good management. All of them have been in my DMs, and all of them have my back around being managed appropriately, being grown, being coached. We're talking a couple of mil in revenue, PoCs that are happy, expansion on what we're doing, introduction to new parts of DeepMind management that's happy, trainer expansion, working with training. Yeah, I was a bit hard on hiring, I will be honest because the scopes that were being set for European hiring roles were not appropriate, but beyond that. 

So let me think that because I've lost access to everything. I would say $2M, but you know who would have the accurate number at this stage? Trevor Bryant. He would have the most accurate number. But I mean, the most accurate is less is less is less. 

## Madness …
OK → slightly baller play that needs a litle finessing. We stoke Apollo’s idea of the spin-out. We approach Matt. When we inevitably don’t get that much backing, Marshall and Francis would probably really quite like the idea of spinning out a research unit focused on Private Equity automation as JV with Apollo. We could even pitch it BECOMING an experimental fund. We could raise for it. Marek and I also know LPs. It’s a very cool fund concept and could pull some heavy hitting tech along with the idea…. we avoid deal closing just because we don’t want to have a PE partner working with us. We approach loads of PE partners and have them do the deals, and we co-invest. Keep Matt F as involved as possible ofc. Find the right top-tier boutique to be the execution partner.

I don’t think I need to spell out how the comp works if you set up a PE fund.

Wanna start a PE fund? ECARX -style. We do the tech, raise LP capital to co-invest but let other funds compete for us to bring the deals (we have a pre-deep-scan method for discovery, partner with Revi.ai for discovery execution). Get’s us out of not being “PE Partner material”.

### Email to Jason
**From:** Alex Foster <<EMAIL>>
**To:** Jason Mather <<EMAIL>>
**Date:** Monday, June 30 2025 at 12:17 PM EDT
**Subject:** Re Automatic Prompt Engineering

Worth noting that this is just the simplest version of the tech from several weeks back so it could work in Glean.

The full feedback loop agent is WIP.
 
One thing for us to touch base on soon: I've been looking for candidate 'big wins' and the below argument has been the biggest I've been sitting on for the last couple months. 

There's a really interesting question combining automated prompt engineering with another Invisible R&D project: the domain knowledge extractor that extracts workflows by watching people work (essentially agentic process mining but from direct observation vs logs):   

1. Hypothesis: A core driver of PE buy decisions next year will be 'how much we could drive efficiency via LLM orchestration automation'. 2026 will be the year enterprise agentic automation takes off in earnest. Integration will remain challenging and Private Equity will have a unique position to be able to force through large restructurings.
2. Fully automatically designing agentic systems to do entire people's jobs is likely still very hard to get working well enough for production >95% accuracy.
3. Let's say we can get it to automate many workflows to 70% reliably.
4. 70% accuracy might not be production-ready, but it does tell us with quite some confidence, which tasks/workflows _can_ be automated, as we have a first pass. If we can get to '70%' then we can have surprisingly high confidence that it's doable.
5. Doing this automatically and pairing it with systematic task discovery opens up what feels likeit could be a very powerful advantage. A viable answer to the question "how much of this company's operations could be automated?".

How might we go about gathering some feedback around this argument?
Any initial thoughts?
Best,

Alex

---
**From:** Jason Mather <<EMAIL>>
**To:** Alex Foster <<EMAIL>>
**Date:** Monday, June 30 2025 at 10:51 PM EDT
**Subject:** Re: [External] Re Automatic Prompt Engineering

Will read / respond 
 tomorrow. This is a horrible week for me as we have a large go-live next week (version 0.x of the mailbox routing thing), and we've still got a ton to do before then. And we're launching Cursor 
 tomorrow. And I'm dealing with an immigration for my husband 
 tomorrow. And the week ends 
 on Wednesday...
---
**From:** Jason Mather <<EMAIL>>
**To:** Alex Foster <<EMAIL>>
**Date:** Tuesday, July 1 2025 at 10:20 AM EDT
**Subject:** Re: [External] Re Automatic Prompt Engineering

To be clear, this is not around watching folks in our industry, but in other industries as we look to figure out how much efficiency we can add in different companies?

if it’s outside of our company, it’s not clear how we’d get access to observe people / processes?


### **DRAFT**
Exactly.

'Automation Guilds' is one concept I'm playing with.
1. Approach CEO/CFO: 'wouldn't you like to know how much of your core white collar operations are automatabletoday vs next year vs year after? [Very subtle & tactful insinuation of consequences of not knowing and guessing / doing 1-10 automation projects at a time when there's likely thousands of tasks to automate within 1-3 years / 12-36 months].
2. Automation Guild = the product. They literally offer a decent cash incentive for anyone taking part in the Guild, and make it selective. Best people get in + need for explicit verbal understanding of their work. Needs careful PR, but we only need one well designed example of how to execute it. 
3. Incentives:
	4. In Apollo's case I was assuming used as part of DD.
	5. Non-aggregated data doesn't leave their premises.
	6. Massive strategic advantage.
	7. Clear starting point for doing the automation, although the actual prompt systems wouldn't be shared except
8. Alternative: Focus groups of consultants. I started off here, it's how my AI market research startup I sold worked.
We can also proxy this 
 entirely I think via iterative research and our 
 QnA﻿-challenge  , fan-out theorising and systematically acquired feedback on the workflow spec files via e.g., ENS. It won't be anywhere near as good but could be a strong open for discovery.

Redact
+ Partner with Revi for the automation research piece.