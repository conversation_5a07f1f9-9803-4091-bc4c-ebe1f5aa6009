• Map
• Update
• Territory
• Dominate
AI R&D
1

2
Exec Overview & TItle slide

Asks (a few of them)
*• Hiring A* FDEs rapidly for Apollo immediately and billing is a no-brainer. Immediate ROI. • R&D Hiring. Our story is ready, we can get top performers. 
• Asking for 10, 5 will be cheap (<PERSON>, Slavic team)   • Need to be genuinely focused on the deep tech.   • Involved with Apollo but not paid for by Apollo, or 50% output loss.
AGM: Want to pay for hire and scale
$5M Sept
$8M EoY
Hiring A* talent, fast.
AI → POC → ROI

3
Two Prospective ‘Silver Bullets’
One already used internally, nearing v1: PFC
One just about validated as possible: DKE
First could 10x LLM FDE productivity.
Second could 10x discovery
–Together, if they both work, could unlock an *enormous* scalable revenue stream. [Highly Tentative]

EXECUTIVE SUMMARY

Apollo
Two potential wins Initial token spend. GLEAN;  We need to scale up capacity per person. We need to 10x the productivity of these 10 people.
4
fix



-> This memo isn’t quite finished. We don’t want to do a presentation or pitch but there’s quite some here. 
We’re going to race through it in 7 minutes if that’s alright rather than explain every item, and then we can focus on what makes the most sense to you.

Unlock Apollo’s $200m AI
Transformation budget today
<PERSON>
Chief Executive Officer 
<PERSON>Black AI by McKinsey 
LOW RISK $9M PER YEAR: HIRE TEN Sr ‘10x-er’ FDE PER QUARTER
5
10x FDE PRODUCTIVITY
# AI R&D Deck Content - All Slides

## Slide 0: CEO action today unlocks a $9M-this-year ramp with Apollo

**Subtitle:** *Need: $2M hiring budget + CIO intro this week*

### Key Content for Slide:

- **Apollo has a $200 million AI transformation budget** (Line 1214)
- **"We're already eyeing ~$9M by year's end"** (Line 2980)
- Apollo is "a rare, C‐level–driven chance to transform their operations, and they trust us" (Line 2980)

### Supporting Visual:

- Timeline bar showing $9M GP by Dec '25
- Apollo logo with $200M budget callout

### One-Pager Content:

**Context & Pain:**

- "Apollo (with a $200M innovation budget) is a rare, C‐level–driven chance to transform their operations, and they trust us." (Line 2980)
- "if we go with Apollo's idea of the spin-out... could you raise funds for a PE fund?" (Line 29)
- Apollo approach = "get in, get integrated, hire loads of engineers, do lots of discovery and make lots of prompts, chains, agents for people to use, deploy deploy deploy" (Lines 2803-2804)

**Evidence & Pilot Metrics:**

- Email Thread with Jason Mather (Apollo Global Management) (Line 1297)
- "Perfect recon" - We've done the discovery already (Line 41)

**Financial/Org Detail:**

- $9M target for year one
- $200M total budget opportunity

**Next Actions:**

- Approve $2M recruitment budget
- Schedule CIO intro call
- Launch hiring engine

—-






[17/07/2025, 05:50:19] Llama: A. The “why now” pain statement
•⁠  ⁠One crisp sentence on Apollo’s current bottlenecks and cost of inaction.

B. Quantified proof
•⁠  ⁠Metrics from pilots: hours saved, defects reduced, dollar impact.
•⁠  ⁠Early demo results framed as evidence, not concept.

C. Financial model snapshot
•⁠  ⁠12-month revenue, cost, margin, cash needs for both streams.

D. Resource & org plan
•⁠  ⁠Headcount curve, critical hires, who leads each phase.

E. Competitive/alt-solution landscape
•⁠  ⁠Why Apollo can’t just hire Accenture / build internally.

F. Risk & mitigation slide
•⁠  ⁠Delivery risk, talent acquisition risk, LLM/legal risk; how we control them.

G. Explicit next steps / decision needed today
•⁠  ⁠E.g., “Approve +$2 M recruiting budget; intro call with Apollo CIO next week.”

Slot these into 6-7 clean slides around the two demos and you’ll have a tight 30-minute narrative.
[17/07/2025, 05:50:44] Llama: Sharp value statement: “$X saved / Y % faster / Z % margin lift” from pilot results.
Concrete timeline & milestones (90-day, 12-month).
Risk & mitigation (delivery risk, talent supply, single-client concentration).
Competitive landscape: why no one else can match our spec-driven approach.
Demo framing: one sentence each—problem shown, ‘aha’ moment, outcome.
Financial model snapshot: revenue, cost to scale, expected EBITDA.
Call-to-action slide summarising exactly what decision you need tomorrow.
[17/07/2025, 05:55:40] Steve Loft Boy Paula: So what we can offer is a hiring engine because when you build no hiring engine what you put in is raw untapped or misshaped talent and what comes out is useful skills aligned capability maps people that are able to do good work and create exponential value the modern engineer is not just a you know you are not just looking for a 10X engineer. You're looking for a 10X engineer who can create agents that create 10X engineers, so it's an exponential growth cut. Therefore, when you look at a organisation that is able to provide those system services and people, they are able to build an innovation function that is able to quickly and nibbly adapt and change to market dynamics and are able to build on existing systems and processes in a way that is able to create real and tangible value.


Immeasurable PE opportunities on horizon
How Important Generalised Automation is to the PE Industry
This is arguably the most important development in Private Equity history.

For some time, human interfaces with AI tools. Then whole jobs that also write emails and appear on slack etc, but not yet
- Major automation requires major governance change. Huge layoffs, broken incentives everywhere.
- PE uniquely positioned to force things through

Startups are going to to start eating enterprises alive in the next few years if they don’t figure this out.
BUY IN

We have great buy-in and trust.

C-level Executive
Head of Innovation
Head of AI Engineering
Aleksei/DM relationships

Daily comms, continued enthusiasm.

‘The Chasm’ is very real, and lived experience to them. We’ve moved the needle where almost all other providers have failed to. We actually understood their problems and have been solving them.

They’re bought into the ‘big plan’. The first part of it, at least.

“Joint Venture” has been seriously mentioned.
$600bn fund has AI pain - Now.
Apollo’s automation pain is acute and costly, now.

• Task backlog is growing 5× faster than fixes; 6-month delay costs $18 MHeadcount (sprawl) -constrained growth



• [Todo]• 
Why now + pain
Apollo’s task backlog grows 5× faster than automation
6
---

## Slide 1: Why-now pain - Apollo's automation pain is acute and costly now

**Subtitle:** *Task backlog is growing 5× faster than fixes; 6-month delay costs $18M*

### Key Content for Slide:

- **"Enterprises today already have not hundreds but thousands of tasks that they could automate using today's LLM technology-level. That number is growing way, way faster than they're making even a dent in the task list."** (Context from user notes)
- **"Enterprises are in a kind of danger they've never faced before."** (User notes)
- Visual: Growth chart A (task backlog) vs chart B (automation capacity)

### Supporting Visual:

- Exponential growth curve of automatable tasks vs linear automation implementation
- "$18M cost of 6-month delay" callout

### One-Pager Content:

**Context & Pain:**

- Traditional Automation Timeline shows project death by feedback cycle (Lines 116-128)
- "Multiply by 100 tasks = Project death"
- "A core driver of PE buy decisions next year will be 'how much we could drive efficiency via LLM orchestration automation'. 2026 will be the year enterprise agentic automation takes off in earnest." (Line 475-479)

**Evidence:**

- Process Mining failures: "that new sr guy, ex UI Path → yeah it never works, we just had it as a foot-in." (Line 33)

---

* This assumes we _can_ sell $200k salaried developers in with 40% margin, which is ambitious.

** Hires from Alex and Will’s network have performed exceptionally well at Invisible. 

And we have a secret weapon.
Rapid Revenue Plan:
   10 FDEs now, 10 in Q4→ $1 M GP/month @ 40 % margin*
These hires just make money 
05
xxxxx
04
xxx
03
Cortex / Requirements Factory starts to kick-in in earnest.

→ 5-10x’d FDE’s
02
Proven pipeline / hiring engine** delivers talent in ≤ 30 days and pays back in 12 weeks
01
Spin up Will-Alex A* hiring machine
7
10x too high?


## Slide 2: Our hiring engine scales 10 engineers/month at 40% margin

**Subtitle:** *Proven pipeline fills roles in ≤ 30 days; each engineer pays back in 12 weeks*

### Key Content for Slide:

- **"10 hires a month + Steve"** (Line 58)
- **"10 people per month, $200k each, charge at $333k, = $133k *10 per year per month"** (Safe Plan from user notes)
- **"$1m new GP per month"** (Safe Plan)

### Supporting Visual:

- Hiring funnel graphic showing 10/month throughput
- Margin math table: Cost $200k → Bill $333k → 40% margin

### One-Pager Content:

**Steve Loft Boy Paula's insight:**
"So what we can offer is a hiring engine because when you build no hiring engine what you put in is raw untapped or misshaped talent and what comes out is useful skills aligned capability maps people that are able to do good work and create exponential value the modern engineer is not just a you know you are not just looking for a 10X engineer. You're looking for a 10X engineer who can create agents that create 10X engineers, so it's an exponential growth cut."

**Hiring Plan Details:**

- Steve = expanding Apollo + helping find A* engineers (Line 35)
- "None of my strong recc. hires haven't been excellent" (Line 35)
- Need isolation: "The constant distractions / being asked to do all kinds of random pieces doesn't work. Kills speed by >70%." (Line 49)

---



8
LLM Deck Builder
DEMO
Enterprise decks (powerpoint) have different needs.
No one is solving it correctly.
We’re close from having solved >50% of finserve powerpoint.Our tool is a “bottom up” approach to slide manipulation: It fits into existing processes and workflows with existing example decks, getting user acceptance by creating the deck exactly as they would currently do it.

This is achieved with a number of different agentic flows, operating on slide images, a suite of custom slide manipulation tools, and a slide template schema designed for LLM comprehension.

While this is currently implemented on a custom framework, future versions of this would live entirely within the agentic platform.
ATOMIC
AXON
SYNAPSE
EXPERTS
NEURON
Show Screenshot of Will Demo

Other Deliveries & WIP
PROMPTS|AGENTS|RAPID VALUE
Neuron
Unify fragmented data from any source or format into a clean, 
structured layer for analysis and automation.
Atomic
Turn manual workflows into automated processes 
and connect with 300+ integrations.
Axon
Build and deploy AI agents tailored to your processes with coordinated handoffs and full control over training and execution.
Experts
Find top-tier specialists to fine-tune your models, 
with custom sourcing available on demand.
Synapse
Train your model to your specifications and evaluate outputs for quality, safety, and accuracy.
4 screenshots even if of text, that they’re in interfaces, is good. Include Prompt Library screenshot / Excel UI? Both have great one-line descriptions
[Maybe just include four screenshots of prompt outputs here, then 1-line describe them in the color sections below.
Probably make white not color.]
9
Excel spreadsheet ingestion (L1)
AI powered Data pipelines
Automated DD processes
AI QA
AI Asset monitoring
AI Opportunity discovery
What does it cost to do? 
What does it cost not to do? 
What is it gonna make us?

10
Aleksei & David M
MORE DELIVERIES / WIP.
Neuron
Unify fragmented data from any source or format into a clean, 
structured layer for analysis and automation.
Atomic
Turn manual workflows into automated processes 
and connect with 300+ integrations.
Axon
Build and deploy AI agents tailored to your processes with coordinated handoffs and full control over training and execution.
Experts
Find top-tier specialists to fine-tune your models, 
with custom sourcing available on demand.
Synapse
Train your model to your specifications and evaluate outputs for quality, safety, and accuracy.
[Maybe include just to skim over as impressive and takes no time to skim past a slide for later consumption]

What does it cost to do? 
What does it cost not to do? 
What is it gonna make us?

* Probably.
Surprisingly high confidence.
Two Breakthroughs
Proof of concepts & hypothesis validation for ‘market-victory-level’ upside*
We looked
We experimented
We found*

11
Hit the hype, a weeee bit less cheesy please.
**Alex Foster**:
That's awesome man!
There's just a lot more competition and sensitivity, sales is hard.
I'm pitching our two 'silver bullets' to Matt on Thursday, we'll see if it sticks or we're just going scale the Apollo approach
(Apollo approach = get in, get integrated, hire loads of engineers, do lots of discovery and make lots of prompts, chains, agents for people to use, deploy deploy deploy)
It works, but I don't love it as a model.
**Jeremy Roscoe**:
what are the silver bullets you're pitching?
**Alex Foster**:
Silver bullets = Requirements Factory / Cortex → spend tonnes of tokens working out how to solve as much of an LLM problem automatically as possible via synthetic test cases and requirements judges. Very similar to what the foundation models were doing with web trajectories. Doesn't matter how much it costs to solve it as once it's solved, it's automated.
and Domain Knowledge Extractor / Automation Guilds. It's more controversial. We've been using watchers to take loads of screenshots and abstract DOM details of someone working. Multiple layers of abstraction via LLMs and technique similar to David and my "en-mass QnA" concept he deployed for Nvidia.
Hyper-Rich-Observation-Data → tasks+subtasks → try to automate all of them automatically.
Then the hopeful slam dunk of it is value of information.
•  Toy Model: If you refurb a house for £200k and sell it for £400k more, value of refurb is £200k.
•  A tool that tells you which house would sell for £800k more for a £200k refurb... that information is worth £400k.
upshot → automatically automating orchestrations up to production is probably just way too hard.
But if 'how far the automatic automation got by itself' is a strong signal on whether it could be production automated...
We'd be selling very valuable information.
And it's kind of the ideal sales platform; knowing 1000 tasks that could be automated with enough effort across an org.



### PRD Overview → Claude
PRD Overview → 3 critical questions. [Claude](https://claude.ai/artifacts/inspiration/3d81ba29-d1ad-4e9b-b58e-3e0f46ba8afd)
1.  I've been working on a number of approaches for a feedback system for our Agentic platform. What I want and I've been working on is the idea that you have a single spec file that a developer is working on, and in the background, you have a series of loops that is looking at that file and any other context that's required, saying "Right, is there something new that needs to be done or something that I should change?" It looks at what's been made already or what's been done at the task logs, and whilst you're working on this spec file which has the initial problem and the requirements and the various scenarios that you think we should test this with, it's sort of working away and it has a whole load of system prompts for different general components that it should be making for things in general be working on AI features, not front-end stuff. The hard bit really is just having it fire off these new Claude code instances and keeping an eye on everything, so trying to work out what the most important next components are. I was working on the like a chat interface for that to be the central thing rather than just a requirements document edited by like vibe coding. That's where we're at. 
2. Target users are Enterprise CEOs and CFOs. The problem this solves is: we want to be able to 50-70% automate some very large number of tasks. They don't have to be done up to human level, this is just a way of generating signal as to what can and can't be automated. We already have a system for watching and tracking a really large amount of extracting tasks from watching people work automatically. And then this is the part that just works really really hard, spends a lot of tokens to attempt to automate that task. The important thing is that we see how far it gets and that we use the requirements-based grading system to measure that. 
3. Whether the agentic system is able to automate a very large number of tasks given a lot of support initially, and whether the system is able to course correct critically because we're happy to spend the tokens if it needs to be able to spot it veering away from its requirements and specifications itself by continuously coming back and reviewing its specification. 






### Results in this PRD:
# One-pager: Agentic Task Automation Platform

## 1. TL;DR
An autonomous feedback system that continuously monitors and improves task automation for enterprise workflows. The platform uses AI agents to process specification files, automatically generate code components, and self-correct through requirements-based feedback loops. Designed for Enterprise CEOs and CFOs who need to identify and automate 50-70% of large-scale business tasks without requiring human-level performance.

## 2. Goals
### Business Goals
* Achieve 50-70% automation rate across enterprise task portfolios
* Generate actionable intelligence on task automation feasibility
* Reduce manual oversight requirements through self-correcting AI systems
* Maximize ROI on AI token spend through targeted automation attempts

### User Goals
* Identify which business processes can be effectively automated
* Reduce time spent on routine task management and execution
* Gain visibility into automation progress and success rates
* Scale operational efficiency without proportional headcount increases

### Non-Goals
* Achieve human-level performance on all automated tasks
* Replace human decision-making for strategic initiatives
* Provide front-end development automation capabilities
* Support individual contributor or small team use cases

## 3. User stories
**Enterprise CEO:** "I need to understand which of our thousands of daily tasks can be automated so I can make informed decisions about operational scaling and resource allocation."

**Enterprise CFO:** "I want to quantify the ROI of our AI automation investments and identify the highest-impact areas for further automation development."

**Operations Leadership:** "I need a system that can attempt task automation at scale while providing clear metrics on success rates and failure modes."

## 4. Functional requirements
### Core Platform (P0)
* Specification file processing and continuous monitoring
* Multi-agent feedback loops for requirement validation
* Automated Claude code instance generation and management
* Requirements-based grading system for automation assessment

### Intelligence Layer (P1)
* Task extraction from user behavior monitoring
* Priority ranking for component development
* Self-correction mechanisms for specification drift
* Chat interface for central requirement management

### Enterprise Features (P2)
* Token usage optimization and cost tracking
* Automation success rate dashboards
* Task portfolio analysis and reporting
* Integration with existing enterprise workflow systems

## 5. User experience
### Primary User Journey
* Executive defines automation goals and uploads task specifications
* System continuously monitors spec files and generates automation candidates
* AI agents attempt task automation with real-time feedback loops
* Requirements-based grading provides automation success metrics
* Dashboard shows automation rates and identifies optimization opportunities

### Edge Cases and UI Notes
* Handle specification ambiguity through clarification requests
* Provide fallback mechanisms when automation attempts fail
* Support iterative refinement of automation requirements
* Enable manual intervention points for critical task corrections

## 6. Narrative
Sarah, a CFO at a Fortune 500 company, starts her Monday morning by reviewing the Agentic Platform dashboard. Over the weekend, the system processed 847 routine tasks across finance, operations, and customer service. The platform shows that 582 tasks (69%) were successfully automated, with detailed breakdowns by department and task complexity.

The system flagged 23 tasks that began deviating from their original specifications and automatically course-corrected using the requirements-based feedback loops. Sarah notices that invoice processing automation improved from 45% to 73% success rate after the system refined its approach based on continuous spec monitoring.

She opens the chat interface to modify requirements for expense report automation, knowing that the AI agents will immediately begin testing new approaches in the background. By lunch, she receives a notification that the updated specifications have been validated and are ready for deployment across the organization.

## 7. Success metrics
* **Automation Success Rate**: 50-70% of attempted tasks successfully automated
* **Self-Correction Accuracy**: 90%+ of specification drift detected and corrected
* **Task Coverage**: Number of unique enterprise tasks processed monthly
* **Token Efficiency**: Cost per successfully automated task
* **Time to Automation**: Average time from spec creation to working automation
* **Requirements Compliance**: Percentage of automations meeting original specifications

## 8. Milestones & sequencing
### Phase 1: Core Infrastructure (Months 1-3)
* Build specification file monitoring system
* Implement basic feedback loop architecture
* Create requirements-based grading framework
* Deploy MVP with single enterprise pilot

### Phase 2: Intelligence Layer (Months 4-6)
* Add multi-agent coordination capabilities
* Implement self-correction mechanisms
* Build chat interface for requirement management
* Expand to 3-5 enterprise customers

### Phase 3: Enterprise Scale (Months 7-12)
* Optimize token usage and cost management
* Build comprehensive analytics dashboard
* Add advanced task prioritization algorithms
* Scale to 50+ enterprise deployments 


Repeat: 
This system continuously improves the opening Spec Docs either itself or by humans. Humans are bad at annotation, and specifying automation or what they want. They are great at pointing out what is wrong with a human-readable document they already recognize
12
Cortex
/REQUIREMENTS FACTORY: DEMO
5-10x FDE Productivity
By itself, we think this system, at least 50% borrowed from the plans of OAI & Anthropic, can 5-10x LLM FDE productivity.

With Automation Guilds/DKE (next up), extraordinarily high upside ceiling if hypotheses validated.  [[ AF to-do]]
Iterate: The Spec: Instruction⤄Requirement
xxx
Telemetry: Grader Re-alignment 
The beating heart of the Requirements Factory. Agents, even the best, fall-off on long-running tasks. Requirements → Graders: individual, reliable LLM-QA prompts that do one thing, well: re-alignment.
Include what makes most sense here. UI ‘task cards’ screenshot if possible.
Instrument: Synthetic Scenarios
[We got very good] at Synthetic Test Case generation in Sep-Feb ‘24-’25. This, critically, enables end-to-end testing before humans give feedback. Iteration speed nitro. G=k1/t
BREAKTHROUGH #1
“Unlikely the 20-30m process from earlier is done,” [terminal still flying on Mac B, Will’s?]

**PFC / Requirements Factory. ** *Automation Automation*. [**Move to opening of main slide>>**. The human 'PFC' (Pre-frontal-cortex) is extremely slow and expensive. Its job is to tell stories (trajectories) to figure out how to do things, so that the various subconscious fast-brain can learn new tricks at incredible speed. ]PFC / Cortex / Requirements Factory turns terribly defined, fuzzy tasks into rigorously testable specs at scale. 10x FDE productivity. Synthetic testing and high-scale feedback signal starts to open the door to very high scale strategies. Compatible with any agentic platform as focused on *meta-automation*. At-scale, automated feedback signal.


## Slide 3: Breakthrough #1—Requirements Factory makes engineers 10× faster

**Subtitle:** *Live demo: prompt build time fell from 2 days to 2 hours*

### Key Content for Slide:

- **"Early versions of the Requirements Factory did about 80% of the work for several prompt deliverables. In two days, David delivered three prompts to Apollo using it whilst I was still working on the first"** (Line 3002)
- **"We boost each engineer's output 10×"** (Line 2980)

### Supporting Visual:

- 30-sec demo GIF showing automated spec → prompt generation
- Before/After: 2 days → 2 hours

### One-Pager Content:

**The Full System:**

```
graph TD  
A[Domain Knowledge Extractor] -->|Task Requirements| B[Prompt Swarm]  
B[Generated Implementations] -->| C[Eval Constellations]  
C[Performance Metrics] -->| D[Self-Optimizing Prompt Engineering]  
D[Optimized Models] -->| E[Production Automation]  
E[Feedback] -->| A
```

(Lines 159-166)

**The Secret Sauce - Feedback Factory:**

- "Prompt swarm automatically makes reliable graders based on requirements. Generates NL test case strategy docs (1-2 pages of intelligently grouped 'if this → that' assertions)" (Lines 203-205)
- "Requirements become intelligent graders, not just that score but that give feedback" (Lines 206-207)
- "A spec that, from which, agents (+graders etc) can [build]" - Spec Driven Development is "100x less valuable a skill than" ad hoc coding (Line 31)

**Process:**

1. Observe + decomposition → Extract hundreds of tasks per job
2. Enrich via automated confidence-driven hierarchical QnA → infer requirements
3. Prompt swarm → Generate graders from requirements
4. Automatically iterate prompts to achieve requirements
5. Human feedback only to update test case strategy docs
   (Lines 209-214)

---



COST OF ACTION
What does it cost us to do?

1. We’ve finished v1.
2. Earlier versions already sped up jr prompt engineer to finish 3 tricky deliverables faster than AF finishing one.



COST OF INACTION
What does it cost not to do? 
VALUE OF RETURN
What is it gonna make us?

Aaa
B
Enhanced Tool Integration Automation. Every API / Web App becomes ‘accessible functionality / data’ within < 1week.


The ‘Requirements Factory’
3-5 lines can go here
CORTEX
13
Avoiding Toe-Treading
For simplicity, these docs do not build-in what Kit/Aaron/Alexei's teams and products are doing within the system.
We roughly would guess something like:
1. AgentFactory → Aaron's team are building the platform the automation operates off of, the below only feeds into / enhances it. Once their agentic platform is ready, the machine is not drafting prompts, but **Fleets**. Fleets are just YAML, aka text. Prompts are text, Fleets are text. David Melville's original idea for how to synergise cleanly.
2. DKE / Automation Guilds → we prototype, Alexei / DataPlatform own it. This is Data + Sales.
2. RequirementsFactory → we prototype then own/co-own the continuous R&D for enhancing the feedback loops. Specifically talking about **the feedback system** here.

However, this is just a rough guess.
We're showing this to you (Matt) first (* recent check-in with Alexei and Aaron gave a 🟢 on the DKE and feedback systems in Feb) so that you are the one who orchestrates what happens next. What you want to happen before we take this to Kit/Alexei/Aaron etc.
* What do we need to validate before R&D commit to pursuing this strategy full force? Technically? Business Value -wise? 
* Or should we just run at this thing immediately?
* Or are we missing some crucial consideration, off-the-mark and need to backup, and rethink the whole thing?

### Cortex
We liked this placeholder name because:
* The other components are all intelligence/mind -themed.
* This system is –not– pre-training or post-training or fine tuning. It's not maths. It's out-loud, chain of thought. Tonnes of it.
* A rough model of how human's creatively problem solve:
	* (1) Semi-random ideas + slow pre-frontal-cortext evaluations on which ideas are good/bad and (crucially) **why**. Human teams talk to each other and very slowly, painfully and frustratingly, make progress.
	* (2) Then it get's easier, we sleep, we compress, we develop language short-hands for the problems, and our evaluations (graders) for how good our 'ideas of solutions' are get much faster, and sub-conscious, but often still with slow-reasoning as well, and often still with justifications.
	* (3) Then... it consolidates. We wake up one morning, we're in the shower, and our mind is doing the whole [generate ideas and evaluate them] process subconsciously. The solution, the idea of it as a concept and often even just a few sentences or words that abstract it, appear in our head as if by magic.
* The system we are proposing is analogous to (1). Entirely out-loud thinking+feedback. Just like the above model of the pre-frontal-cortex part of the above model.
* Hence: **Cortex**.
**Is it financially viable to run 'Cortex' at scale in operational production? i.e., to actually get each and every task –done–**
Likely not. It's probably very expensive for most tasks.
**So doesn't that mean this has no sellable value?**
No, because if we can get Cortex to work, it almost doesn't matter how expensive it is to run.
If we can get it working, even just to the '70%' mark for, say 1/3, of the core tasks an organisation's workforce has to execute, that's already extremely valuable.
1. The hypothetical CEO/CFO now has this, frankly, **crazy valuable** piece of intel.
2. The actual production execution can be done by a system doing >100x less 'thought'. Agentic Platform + human FDEs, external specialist constractors / specialist agent providers, our own pre/post-training solution for compressing the solutions generated by Cortex, and other runtime solutions such as dynamic few shot for automated continual self-improvement.
**But this sounds really complex and hard to sell...**
The foot in the door can just be the intel.
We don't need to convince them we can pull-off automating some large portion of it in the first meeting.
We just need to convince them how valuable this intel will be, and that they should **convince us** that they should be one of the first 10 orgs we set up Automation Guilds for.

### Micro Overview
Capture → Abstract → Generate → Test → Evaluate → Deploy → Improve
                          ↑                                    ↓
                          ←────────── Feedback Loop ──────────←
                         


Extract Domain Knowledge
Extremely rich qualitative dataset that can be re-queried at will for ideation when Cortex ‘gets stuck’.
Pilot flagged 73 % of personal workflows. No tie-in with Cortex, not reliably working, PC-only.
14
Automation Guilds
DOMAIN KNOWLEDGE EXTRACTION
THOUSANDS OF TASKS, UNLOCKED AUTOMATICALLY
Where process mining failed, this will succeed, observed from the source, the same way 
Simulate & Proxy
TBD
Sell Value of Information
CEO’s want. Persuasive as real. Blueprint. Scaffolding. Requires tools.
Incentivise
“Do x well and you’ll be out of a job” -> bad incentive. ≠ A players.
“Apply to be part of elite, exclusive, well incentivised + 0.2% royalities stock Automation Guild. Your skills could become the AI. 
Observe
Screenshots + heavy VLM QnA (Nvidia POC) + optional ethnography (HT Aaron).
Include what makes most sense here. 
BREAKTHROUGH #2
**PFC / Requirements Factory. ** *Automation Automation*. [**Move to opening of main slide>>**. The human 'PFC' (Pre-frontal-cortex) is extremely slow and expensive. Its job is to tell stories (trajectories) to figure out how to do things, so that the various subconscious fast-brain can learn new tricks at incredible speed. ]PFC / Cortex / Requirements Factory turns terribly defined, fuzzy tasks into rigorously testable specs at scale. 10x FDE productivity. Synthetic testing and high-scale feedback signal starts to open the door to very high scale strategies. Compatible with any agentic platform as focused on *meta-automation*. At-scale, automated feedback signal.


## Slide 4: Breakthrough #2—Domain Knowledge Extractor maps 1000s of automatable tasks

**Subtitle:** *Pilot flagged 73% of Apollo workflows ≥ 70% automatable in 2 weeks*

### Key Content for Slide:

- **"Domain Knowledge Extractor answers the question every C-suite executive should be asking: 'Which tasks that I'm paying people to do can be automated with today's technology, and how?'"** (Lines 111-112)
- **The 70% Threshold: "getting 70% of the way there is plenty to have confidence 'this task can be automated'"** (Lines 24-29)

### Supporting Visual:

- Task inventory visualization: 2,341 total tasks → 1,893 automation ready
- 73% automation potential gauge

### One-Pager Content:

**The Innovation - Generate, Don't Extract:**

- "We've learned from the foundation model playbook. OpenAI, Meta, and others don't collect millions of examples - they collect thousands and synthetically extend them." (Lines 138-140)
- "The core issue: people don't know how they do their jobs until they're doing them. Ask someone to describe their workflow, and you'll get maybe 60% accuracy." (Line 136)

**The 70% Secret Sauce:**
"70% accuracy might not be production-ready, but it does tell us with quite some confidence, which tasks/workflows _can_ be automated" (Lines 473-475)

This gives us:

- **A) Report on what can be automated now vs next year**
- **B) Impact: '70% working' may well look surprisingly close to done**
- **C) Sales: Unique access to a big list of tasks and sub tasks**
  (Lines 24-29)

**Results Example:**
"'I just review claims' → AI DISCOVERS → 'You make 8,000 micro-decisions/day'" (Lines 849-850)

---



COST OF INACTION
What does it cost not to do? 

This is very much possible right now.

Very little market movement seems to have properly realised this [NEED ANSWER TO ‘who else’ on this todo].

They will realise. Then, land-grab. We don’t need to tell anyone how we’ve solved process mining.
VALUE OF RETURN
What is it gonna make us?

We can tell you all your tasks that could be automated, and whether now vs ‘26 vs ‘27. Incredibly valuable information. Foot-in, C-level ‘can’t say no’. Private Equity buying signal (Alpha). Sales platform, no competitor has a liberal list of what can / can’t be automated at an enterprise.

Targeting: Sell highest ROI automations rather than ‘whatever they came up with’. Scalability, recurring skill-use.

Value of Information
3-5 lines can go here
AUTOMATION GUILDS / DOMAIN KNOWLEDGE EXTRACTION
15
COST OF ACTION
What does it cost us to do?

To finish validation: 
• 5-10 solid AI devs and 3-6 months.
• POC with DKI and Invisible Agents doing non-AIT work. Full capture + audio.

Start selling, exclusive access.



$VOI can be > $Execution, & 100x more scalable.
[example of houses.]



What does it cost to do? 
What does it cost not to do? 
What is it gonna make us?


Asks:WHAT WE NEED TO EXPEDITE AND ACCELERATE.
16
HIRING ENGINE for AI FDEs FOR INSTANTROI
R&D Hires
Within reason unlimited token budget
FinServ
E-comm/
Retail
Joint Venture
Seriously Consider PE Co-investment Fund

10_FDEs pq
• Immediate ROI-only.
• A* technical talent acquisition: SL
• Alex, Will, Steve, go on networking warpath.
• We have the story we needed now.

2*5_Devs
• 5 each dedicated to Cortex & Guilds
• 5 fr Will Hayes
• David Domac $120k
• 5 fr DD Balcan team.
MW if possible
• AF: 1 Associate
• AF: comp = WH

- Letting Vas go.

9x
Reimbursements speed for Headway 
& the revenue team


66%
Cost reduction for Klarnaʼs competitive intel work

9x
Revenue per SKU using data enrichment


43%
cost reduction using bespoke automation


1030%
ROI in dispute 
claims savings

xxx
xxx


LARGEST 
BIG BOX RETAILER
1. List of Asks prepared
2. Steve → resources
3. V smart industrious high dopamine associate to work directly with me. Never not had it, productive efficiency is down 70%, which is still v high but it's a total waste. Compensating w unsustainable sleep & number of hours (80-90 pw), age just started kicking and I can't get away with the sleep as I was.
2. Include Whimsical Diagram
3. Taking a serious bet on Domac.
	4. Joins R&D team.
	5. Salary → $120k. 
	6. David Hire Balcans → Five * $50k each.
7. Invisible Agents Automation Guild
	8. VDI → virtual machine.
	9. Speak aloud to say what they're going to do. → transcribed.
	10. Also write it down.


Enhance then show this deck to Dan C for AlphaSights team re-approach. Would be slam dunk.




+ There were two version. Is this the version we liked and where is the other one?
+ Get the diagrams from ChatGPT for the Slides, link here.
+ Angela → who's the best person in your team to get thinking about how we could position this new tech I'm now really confident we're going to get working?
> What has changed since this version above is that I've started leaning much more heavily on the argument that [getting 70% of the way there (in a success scenario) is plenty to have confidence "this task can be automated". While getting to production grade (say 95% accuracy*) might be a tonne of work, if our system can reliably get to 70% by itself, that's enough to run it on thousands of tasks and give
> A) report on what can be automated now vs next year vs whatever → analysis something something.
> B) impact. "70% working" may well look surprisingly close to done without digging in. The scale of how many tasks this was done for... impact.
> C) Sales. Unique access to a big list of tasks and sub tasks, rich data for further QnA behind each.
> ... if we go with Apollo's idea of the spin-out... could you raise funds for a PE fund? Once we can prove it? Fund financials are excellent, no one else is doing "push for maximum automation using the degree of force that only an acquiring PE fund + Mckinsey is really capable of doing". But we have to kind of decide, because if I put the schematics on their VM, they'll have it.
> 70% → reframe as "the secret sauce: the 70% threshold".  x% = x% human parity in quality.
> The value of **THE ONER** . Vibe coding and even engineers iterating ad lib for speed to get to a solution is, I estimate, something like 100x less valuable a skill than Spec Driven Development. See below. This philosophy also applies to Task Automation. We could write a series of white papers over 3-6 months on this if we wanted to drive adoption within the org. Why? A spec that, from which, agents (+graders etc) can 
> Have cats explain it again. Program your own 'as if cats' using the prompt in the code.
> Re Process Mining: "that new sr guy, ex UI Path → yeah it never works, we just had it as a foot-in." → this is a credibility problem. The Proxy (Map|Territory?) should get us in though. Surprising detail and scale _before_ we get in.
+ Steve = expanding Apollo + helping me find the A* engineers in our network. There are many but time + will. Automation Guilds strategy was his idea. None of my strong recc. hires haven't been excellent. Will then need him to find the rare engineers if this grows quickly and we have to scale it.
+ If goes well. Be baller. OK. Serious question Matt. Financially, why shouldn't I go somewhere else? I'm pretty nervous asking this but it's just progressively bugging me more and more. My AGI timelines are short. I know what chaos is coming in the run up and I have my chosen family that I want to ensure are protected if/when that chaos comes. I spent my UHNW money recovering for 1-2 years after a personal tragedy in 2023. I was offered $200k pa in January and I turned it down when you offered the team and head of AI R&D....  Honest question: would you have joined Invisible if we had had circa zero AI tech? Because, and please don't share that I said this as I don't know who's narrative it might not fit with, but there's at least a dozen people who would I think happily admit that that's what you would have found if I hadn't taken on the ridiculously ballsy mission of pushing AI into the org. There was literally nada when I got here despite being told, and many people at all levels believing we had things we didn't. It was crazy challenging, and I had to do it without taking ownership of it.... Now I'm doing all this.... My bonus totals like $15k on $130k base. It just got boosted to $150... I have a *lot* of rough edges. But I think I do genuinely have a very rare knack for finding winning long-odds bets that I can just about pull off, then just about pulling them off.... I don't genuinely feel like real value /impact is what will lead to better comp. I can't not do the most important thing I can think of, it's a compulsion.
	+ Paid same as Will. Set some milestones, reduce it back if I miss them. (If up later, no, it's overdue.)
	+ I need a small exec team. Associate: One Oxbridge grad w 2 years somewhere strong + extremely high dopamine. I really struggle without this, losing 30-50% efficiency.
	+ Exec Prerogative
		+ **Will.** We could have built this in April when Will joined. He strongly felt that we needed to first bring in $$ or we weren't safe. OK so I took us into Apollo. Perfect recon. Will needs to know this is what he should do to be safe. For him, safety is what makes him want to take risks, he's not ADHD, and he's brilliant. Ambition is secondary.
		+ Domac & Marshall etc → I want to put in a rule. No LLM / agentic get's delivered that we didn't automatically create.
		+ Aaron → he'll happily take whatever is useful to him and adapt / improve, I know. Want to know I'm safe to just hand things over and you know we're behind there somewhere. If I take things straight to him, I'm not going to get credit the same way. I think you now have enough evidence to just trust me going forward. I'll keep finding more silver bullets, and this plan has a bunch more to it planned around FM advances as is. 
		+ **R&D Spec-Driven-LLM-Dev only**. David M and Domac are both supporters [check] of the idea to make a wild rule: No deliverable work where each package (module) wasn't a 'oner' - i.e., built in one go by agents following a spec. This skill is SO much more valuable 
	+ Steve → see above.
	+ 3 A* engineers.
	+ David Domac → need to pay him $120k. Insane he's on $70, was promised and we'll lose him eventually.
	+ Agentry Automation Guild Challenge to find more diamonds.
	+ [Wagemaker]([[Vision Doc 1#WAGEMAKER]]) → was made redundant whilst I was trying to poach him. Wasn't delivering enough but [notes on him delivering]... I'm very confident he'll deliver for me, concerned there's some political BS going on that I don't want to wade into / drag his name through the dirt for. We're pretty isolated and I'd have him focused just on our team.
	+ Team isolation. The constant distractions / being asked to do all kinds of random pieces doesn't work. Kills speed by >70%. Urgency just wins over 'hard important thing with minimal positive feedback until nailed it'. Not zero external work, but anchoring to say no. Steve & Wagemaker to protect.
	+ Revi → $1500 a month. Help closing partnership. They can adapt their agentic research machine for the proxy of **Territory**.
	+ **Britton** I've** spent $3k personally on software experiments I didn't have time to get approved. Special permission for R&D.... Also, this is going to spend a lot of tokens. Like, a huge number. Will need some Foundry/similar GPUs.
	+ Andrew Hicks + John Zhu + Ernest. Heavily Split Attention.
	+ David Melville → best engineer for this in the org. Paired with Will (focused), Domac and [JZ&Ernst hopefully] we can crush this.
	+ Should we pitch Alexei/Aaron/Kit to collaborate, and how without sharp elbows. **Do not want** that. Long story but violence and lies ruined a whole chunk of my life. Want to build. Team + tech + strategy.
	+ Junaid + Fine Tuning. Current plan doesn't hold technical water. But we can apply it here.
	+ Data Quality have been pouring through examples since Prompt Swarm in Feb → want them testing this with KPIs to hold accountable. **Need  many more real enterprise examples from SE/Sales. Ask them to get them. Not 'the hardest thing that org can't do' but 'everything they spend a lot of non-comms human hours on.**
	+ Caspar, Marek, Tom down to sell. Jenny will get it.
	+ New
		+ 10 hires a month + Steve
		+ 



The AI scaling partner 
   for enterprises 
Invisible has trained 80% of the top foundation models and is the leading partner for enterprise AI transformation.
10 year track record  
Founded in 2015, exploring AI solutions for enterprise
Hypergrowth 
3 years’ explosive revenue growth aligned to Big Tech expansion 
Profitable & cash generating 
Doubled revenue in 2024 to $134m with 3 years’ profitability and positive cashflow 
AI native  
2.3m hours training 80% of leading LLM models to date
Humans in the loop 
~300 FTE and a global network of elite training experts with 26 domains and 30+ languages 
17
Invisible Technologies

18
I'm writing an AI R&D update memo to the CEO → The core focus is to alert him to the hiring opportunities that we have to make immediate and high levels of revenue with the private equity fund that we've been working with. That's the immediate prize. We need him to unlock it. And the second is to let him know of two breakthroughs and the big opportunity that we think we have found in terms of helping our company complete its enterprise pivot. How should we do this? Slides each with link to one-pager doc? What should the slides be? We have 30 mins and have two demos + QnA to handle so really can't say that much.

Two potential wins Initial token spend. GLEAN;  We need to scale up capacity per person. We need to 10x the productivity of these 10 people. Case study: Apollo: they have a C-level driven $200 million AI transformation budget for this. we just need to take it. How? 


