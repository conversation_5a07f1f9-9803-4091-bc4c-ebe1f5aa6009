#!/usr/bin/env python3
"""
Regression tests for NotePlanBot
Ensures critical functionality continues working after changes
"""
import os
import sys
import tempfile
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from rich.console import Console

console = Console()


class RegressionTests:
    """Collection of regression tests with single-line descriptions"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.tests = []
    
    def test(self, description: str):
        """Decorator for regression tests"""
        def decorator(func):
            self.tests.append((description, func))
            return func
        return decorator
    
    def run_all(self):
        """Run all regression tests"""
        console.print("[bold blue]Running NotePlanBot Regression Tests[/bold blue]\n")
        
        for description, test_func in self.tests:
            try:
                test_func()
                self.passed += 1
                console.print(f"✅ {description}")
            except Exception as e:
                self.failed += 1
                console.print(f"❌ {description} - {str(e)}")
        
        console.print(f"\n[bold]Results: {self.passed} passed, {self.failed} failed[/bold]")
        return self.failed == 0


# Create test instance
tests = RegressionTests()


@tests.test("if bridge_utils._get_bridge() doesn't read files then broken")
def test_bridge_utils():
    from NotePlanBot.utils.bridge_utils import _get_bridge
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write("test content")
        f.flush()
        content = _get_bridge(f.name)
        assert content == "test content"
        os.unlink(f.name)


@tests.test("if Bridge model doesn't parse timestamps then broken")
def test_bridge_timestamps():
    from NotePlanBot.files.bridge.bridge_model import Bridge
    bridge = Bridge(bridge="Test content")
    bridge.add_to_history("test content")
    assert len(bridge.bridge_history) == 1
    assert isinstance(bridge.bridge_history[0][0], datetime)
    assert bridge.bridge_history[0][1] == "test content"


@tests.test("if Bridge.from_file() doesn't read content then broken")
def test_bridge_parsing():
    from NotePlanBot.files.bridge.bridge_model import Bridge
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
        f.write("""<Title>Test Title</Title>
<Date>2025-01-07</Date>

# Content
TASKS
1. [ ] Test task
""")
        f.flush()
        bridge = Bridge.from_file(f.name)
        assert "Test Title" in bridge.bridge
        assert "2025-01-07" in bridge.bridge
        assert "Test task" in bridge.bridge
        os.unlink(f.name)


@tests.test("if TodoListMonitor doesn't detect > 1 LOC changes then broken")
def test_monitor_changes():
    from NotePlanBot.todolist_monitor import TodoListMonitor
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
        f.write("Line 1\nLine 2")
        f.flush()
        
        monitor = TodoListMonitor(f.name)
        # First check should return False (no previous history)
        result1 = monitor.check_for_changes()
        assert not result1, "First check should return False"
        
        # Modify existing lines AND add new ones to ensure > 1 LOC change
        with open(f.name, 'w') as f2:
            f2.write("Modified Line 1\nModified Line 2\nLine 3\nLine 4\nLine 5")
        
        # Should detect > 1 line change
        result2 = monitor.check_for_changes()
        assert result2, "Should detect changes (modified 2 lines + added 3)"
        os.unlink(f.name)


@tests.test("if CFG.TODO_CHECK_INTERVAL isn't 10 seconds then broken")
def test_config_interval():
    from NotePlanBot.config import CFG
    assert CFG.get('TODO_CHECK_INTERVAL', 30) == 10


@tests.test("if todolist_monitor doesn't use CFG config then broken")
def test_monitor_uses_cfg():
    from NotePlanBot.config import CFG
    from NotePlanBot.todolist_monitor import TodoListMonitor
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
        f.write("test")
        f.flush()
        monitor = TodoListMonitor(f.name)
        assert monitor.check_interval == CFG.get('TODO_CHECK_INTERVAL', 30)
        os.unlink(f.name)


@tests.test("if bridge history doesn't limit to 100 entries then broken")
def test_history_limit():
    from NotePlanBot.files.bridge.bridge_model import Bridge
    bridge = Bridge(bridge="Test content")
    
    # Add 150 entries
    for i in range(150):
        bridge.add_to_history(f"content {i}")
    
    # Should only have 100
    assert len(bridge.bridge_history) == 100
    # First entry should be content 50 (0-49 were dropped)
    assert bridge.bridge_history[0][1] == "content 50"


@tests.test("if NotePlanBot main doesn't create bridge file then broken")
def test_main_creates_bridge():
    from NotePlanBot.main import NotePlanBot
    with tempfile.TemporaryDirectory() as tmpdir:
        bridge_path = Path(tmpdir) / "test_bridge.md"
        bot = NotePlanBot(str(bridge_path))
        bot.setup_bridge_file()
        assert bridge_path.exists()
        content = bridge_path.read_text()
        assert "TASKS" in content


@tests.test("if line counting doesn't handle edge cases then broken")
def test_line_counting_edge_cases():
    from NotePlanBot.todolist_monitor import TodoListMonitor
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
        f.write("test")
        f.flush()
        monitor = TodoListMonitor(f.name)
        
        # Empty vs content
        changes = monitor._count_line_changes("", "Line 1\nLine 2")
        assert changes == 0  # Edge case handling
        
        # Same content
        changes = monitor._count_line_changes("Same", "Same")
        assert changes == 0
        
        os.unlink(f.name)


@tests.test("if bridge doesn't store file content then broken")
def test_bridge_stores_content():
    from NotePlanBot.files.bridge.bridge_model import Bridge
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.md') as f:
        f.write("""TASKS
1. [x] Completed task
2. [ ] Pending task
3. [X] Also completed
""")
        f.flush()
        bridge = Bridge.from_file(f.name)
        assert "[x] Completed task" in bridge.bridge
        assert "[ ] Pending task" in bridge.bridge
        assert "[X] Also completed" in bridge.bridge
        os.unlink(f.name)


@tests.test("if bridge model doesn't have all required fields then broken")
def test_bridge_model_fields():
    from NotePlanBot.files.bridge.bridge_model import Bridge
    bridge = Bridge(bridge="Test content")
    assert hasattr(bridge, 'bridge')
    assert hasattr(bridge, 'bridge_history')
    assert hasattr(bridge, 'add_to_history')
    assert hasattr(bridge, 'get_history_changes')


@tests.test("if Claude command doesn't use --dangerously-skip-permissions then broken")
def test_claude_command_flags():
    # Check monitor file uses the correct flag
    import NotePlanBot.todolist_monitor as monitor_module
    
    # Read the source to check command construction
    monitor_src = Path(monitor_module.__file__).read_text()
    
    assert "--dangerously-skip-permissions" in monitor_src


# Run tests if executed directly
if __name__ == "__main__":
    success = tests.run_all()
    
    # Suggest additional tests
    if success:
        console.print("\n[green]All tests passed![/green]")
        console.print("\n[yellow]Additional regression tests to consider:[/yellow]")
        console.print("- if NotePlan reader doesn't find notes then broken")
        console.print("- if NotePlan editor doesn't update tasks then broken")
        console.print("- if visualizer doesn't show three panes then broken")
        console.print("- if streamed_response doesn't detect triggers then broken")
        console.print("- if monitor doesn't restart after errors then broken")
    
    sys.exit(0 if success else 1)