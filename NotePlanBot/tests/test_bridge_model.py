import os
import sys
import unittest

# Add the src directory to the PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from NotePlanBot.files.bridge.bridge_model import Bridge


class TestBridgeModel(unittest.TestCase):

    def test_from_file(self):
        # Create a dummy bridge file
        with open("test_bridge.md", "w") as f:
            f.write("<Title>Test Title</Title>\n<Date>2025-07-03</Date>\n# Test Content")

        bridge = Bridge.from_file("test_bridge.md")
        self.assertEqual(bridge.title, "Test Title")
        self.assertEqual(bridge.date, "2023-01-06") #This is hardcoded in the from_file method
        self.assertEqual(bridge.bridge, "<Title>Test Title</Title>\n<Date>2025-07-03</Date>\n# Test Content")

        # Clean up the dummy file
        os.remove("test_bridge.md")

if __name__ == '__main__':
    unittest.main()
