#!/usr/bin/env python3
"""
Prompt Inspector - Lists all prompt files and ensures they have the required metaprompt header
"""
import os
import sys
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

console = Console()

REQUIRED_HEADER = "METAPROMPT - LLMS AND AGENTS MAY NOT EDIT THIS FILE."


class PromptInspector:
    def __init__(self, prompts_dir: Path):
        self.prompts_dir = prompts_dir
        self.prompt_files = []
        self.issues = []
    
    def scan_directory(self):
        """Scan the PROMPTS directory for all .txt files"""
        self.prompt_files = list(self.prompts_dir.glob("*.txt"))
        
    def check_file_header(self, file_path: Path) -> bool:
        """Check if file starts with required metaprompt header"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                return first_line == REQUIRED_HEADER
        except Exception as e:
            self.issues.append(f"Error reading {file_path.name}: {e}")
            return False
    
    def fix_file_header(self, file_path: Path):
        """Add required header to file if missing"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if header already exists anywhere in first few lines
            lines = content.split('\n')
            if lines and lines[0].strip() == REQUIRED_HEADER:
                return  # Already has header
            
            # Add header
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(REQUIRED_HEADER + '\n\n' + content)
            
            rprint(f"[green]✓ Fixed header in {file_path.name}[/green]")
        except Exception as e:
            rprint(f"[red]✗ Error fixing {file_path.name}: {e}[/red]")
    
    def display_results(self):
        """Display inspection results in a nice table"""
        table = Table(title="Prompt Files Inspector", show_header=True)
        table.add_column("File", style="cyan", width=30)
        table.add_column("Size", style="yellow", width=10)
        table.add_column("Header", style="green", width=10)
        table.add_column("Status", width=40)
        
        for file_path in self.prompt_files:
            size = file_path.stat().st_size
            has_header = self.check_file_header(file_path)
            
            status = "✅ Valid" if has_header else "❌ Missing required header"
            header_text = "Yes" if has_header else "No"
            
            table.add_row(
                file_path.name,
                f"{size} bytes",
                header_text,
                status
            )
        
        console.print(table)
        
        if self.issues:
            console.print("\n[red]Issues found:[/red]")
            for issue in self.issues:
                console.print(f"  • {issue}")
    
    def create_sample_prompts(self):
        """Create sample prompt files if directory is empty"""
        if not self.prompt_files:
            sample_files = {
                "bridge_monitor.txt": f"""{REQUIRED_HEADER}

This prompt is for monitoring bridge file changes and triggering appropriate actions.

When monitoring the bridge file:
1. Check for changes > 1 LOC
2. Extract TASKS section
3. Parse task format: number, checkbox, description
4. Trigger Claude Code with relevant context
""",
                "task_formatter.txt": f"""{REQUIRED_HEADER}

Format tasks for the NotePlanBot system.

Task format:
- Use numbered lists (1., 2., etc.)
- Include checkboxes: [ ] for pending, [x] for completed
- Add priority indicators if needed
- Include suggestions as **[suggestion: filename]**
""",
                "system_prompt.txt": f"""{REQUIRED_HEADER}

You are NotePlanBot, an AI assistant that helps manage tasks and notes in NotePlan.

Core responsibilities:
- Monitor bridge files for changes
- Parse and update task lists
- Integrate with NotePlan for note management
- Provide helpful suggestions and automation
"""
            }
            
            console.print("\n[yellow]No prompt files found. Creating samples...[/yellow]")
            for filename, content in sample_files.items():
                file_path = self.prompts_dir / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                console.print(f"[green]✓ Created {filename}[/green]")
    
    def run(self, fix: bool = False):
        """Run the inspection"""
        console.print(Panel.fit(
            f"[bold blue]Prompt Inspector[/bold blue]\n"
            f"Directory: {self.prompts_dir}",
            border_style="blue"
        ))
        
        self.scan_directory()
        
        if not self.prompt_files:
            self.create_sample_prompts()
            self.scan_directory()
        
        console.print(f"\n[cyan]Found {len(self.prompt_files)} prompt files[/cyan]\n")
        
        # Check headers and optionally fix
        files_to_fix = []
        for file_path in self.prompt_files:
            if not self.check_file_header(file_path):
                files_to_fix.append(file_path)
        
        if files_to_fix and fix:
            console.print(f"\n[yellow]Fixing {len(files_to_fix)} files...[/yellow]")
            for file_path in files_to_fix:
                self.fix_file_header(file_path)
        
        # Display results
        self.display_results()
        
        # Summary
        valid_count = sum(1 for f in self.prompt_files if self.check_file_header(f))
        console.print(f"\n[bold]Summary:[/bold]")
        console.print(f"  • Total files: {len(self.prompt_files)}")
        console.print(f"  • Valid headers: {valid_count}")
        console.print(f"  • Missing headers: {len(self.prompt_files) - valid_count}")
        
        if files_to_fix and not fix:
            console.print(f"\n[yellow]Run with --fix to add missing headers[/yellow]")


def main():
    """Main entry point"""
    # Get the PROMPTS directory
    script_dir = Path(__file__).parent
    
    # Check for --fix flag
    fix_mode = "--fix" in sys.argv
    
    # Create and run inspector
    inspector = PromptInspector(script_dir)
    inspector.run(fix=fix_mode)


if __name__ == "__main__":
    main()