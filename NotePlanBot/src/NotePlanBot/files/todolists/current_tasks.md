# TODO LIST - Managed by Claude Code
Last Updated: 2025-07-03 16:44 (Auto-updated)

## ACTIVE TASKS (from bridge.md):
1. [ ] Create todolist.py which runs every TODO_CHECK_INTERVAL = 30 # seconds. It should inherit from Bridge.config["TODO_CHECK_INTERVAL"]
2. [ ] 1 self.bridge.
3. [ ] 2 check for > 1 LOC change from previous bridge check (self.bridge_history[-1])
4. [ ] 1 it should programmatically run Claude Code using subprocess.run() (check CC documentation online).
5. [ ] Test the Claude Code integration
6. [ ] I could use a 'trigger direction streamed response function. - Claude <PERSON>, please build.
7. [ ] Background Task Attempters + suggestions format. See <1> below.
8. [ ] Come up with better way of reporting on suggestions. Likely something like a suggestions.md which has one line per entry starting with a filepath to the dir with the report and the new files, diagrams etc.
9. [ ] Come up with better way of reporting on suggestions. Likely something like a suggestions.md which has one line per entry starting with a filepath to the dir with the report and the new files, diagrams etc.
10. [ ] Come up with better way of reporting on suggestions. Likely something like a suggestions.md which has one line per entry starting with a filepath to the dir with the report and the new files, diagrams etc.
11. [ ] There's no way of easily glancing at the todolist at the moment to see when things are added or removed. We could make it a sticky Rich Dashboard.. but those also have issues. A readable time like 12.30pm could be good. [[NEW]] could be added, and removed after CFG: EDITS_UNTIL_NEW_REMOVED = 5, programatically?
12. [ ] What do the three different color emoji do in the task list? And Does the monitor / claude task executor check the task list or the bridge and something else checks the bridge and updates the task list? Is the full context of the bridge handed to Claude Code in it's instructions? (could just be told to always check bridge and other docs for context.)
13. [ ] Generally need some solution ideas for how to ensure new claude code instances are given enough context of past actions by other agents to know what's going on what to do next... not obvious how to solve that.
14. [ ] 30pm OK so the cc bots are building the ret of the repo so I should think about what I'm going to do next whilst they do that.

## LAST CC SESSION (2025-07-03 16:44):
### Tasks Completed:

---
*Auto-managed by NotePlanBot - Last sync: 2025-07-03 16:44*
