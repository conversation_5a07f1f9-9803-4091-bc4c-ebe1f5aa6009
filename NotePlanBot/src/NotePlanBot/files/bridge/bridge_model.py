from datetime import datetime
from typing import List, <PERSON><PERSON>

import pydantic
from rich import print as rprint

from NotePlanBot.utils.bridge_utils import _get_bridge


class Bridge(pydantic.BaseModel):
    """Simplified Bridge model - let LLMs handle content interpretation"""
    bridge: str = ""  # Full content of bridge file
    bridge_history: List[Tuple[datetime, str]] = pydantic.Field(default_factory=list)

    @classmethod
    def from_file(cls, path: str):
        """Simply read the file content without parsing"""
        content = _get_bridge(path)
        return cls(bridge=content)

    
    def add_to_history(self, content: str):
        """Add content to bridge history with timestamp"""
        timestamp = datetime.now()
        self.bridge_history.append((timestamp, content))
        
        # Keep only last 100 entries to prevent memory issues
        if len(self.bridge_history) > 100:
            self.bridge_history = self.bridge_history[-100:]
    
    def get_history_changes(self, minutes: int = 60) -> List[Tuple[datetime, str]]:
        """Get bridge history changes within the last N minutes"""
        from datetime import timedelta
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [(ts, content) for ts, content in self.bridge_history if ts >= cutoff_time]

# Test code to show output
if __name__ == "__main__":
    rprint("[green]Running bridge_model.py test[/green]")
    bridge = Bridge.from_file("/Users/<USER>/Library/Containers/co.noteplan.NotePlan-setapp/Data/Library/Application Support/co.noteplan.NotePlan-setapp/NotePlanBot/src/NotePlanBot/files/bridge/bridge.md")
    rprint(f"Bridge object created: {bridge}")
    rprint(f"Bridge content attribute: {bridge.bridge}")