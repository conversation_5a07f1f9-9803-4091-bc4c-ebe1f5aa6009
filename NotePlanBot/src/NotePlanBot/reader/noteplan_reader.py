"""
NotePlan Reader - Reads and parses NotePlan notes
"""
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from rich import print as rprint

from NotePlanBot.config import CFG


class NotePlanReader:
    """Reads and parses NotePlan notes from the Notes directory"""
    
    def __init__(self, notes_path: Optional[str] = None):
        """Initialize with NotePlan notes directory path"""
        if notes_path is None:
            # Try to get from env/config first
            notes_path = os.getenv('NOTEPLAN_NOTES_PATH')
            if not notes_path:
                # Use config default
                notes_path = CFG.get('NOTES_DIR')
            if not notes_path:
                # Fallback to relative path
                base_path = Path(__file__).parents[5]  # Up to NotePlan app support dir
                notes_path = base_path / "Notes"
        
        self.notes_path = Path(notes_path)
        if not self.notes_path.exists():
            raise ValueError(f"Notes directory not found: {self.notes_path}")
    
    def read_note(self, note_path: str) -> str:
        """Read a single note file"""
        full_path = self.notes_path / note_path
        if not full_path.exists():
            raise FileNotFoundError(f"Note not found: {full_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def list_notes(self, pattern: Optional[str] = None) -> List[Path]:
        """List all notes, optionally filtered by pattern"""
        if pattern:
            return list(self.notes_path.glob(pattern))
        else:
            return list(self.notes_path.rglob("*.txt")) + list(self.notes_path.rglob("*.md"))
    
    def parse_tasks(self, content: str) -> List[Dict[str, any]]:
        """Parse tasks from note content
        
        Returns list of tasks with:
        - text: The task text
        - completed: Boolean indicating if task is done
        - line_number: Line number in the file
        - indent_level: Indentation level (for subtasks)
        """
        tasks = []
        lines = content.split('\n')
        
        # Regex patterns for tasks
        task_pattern = re.compile(r'^(\s*)[-*]\s*\[([ xX])\]\s*(.+)$')
        
        for i, line in enumerate(lines):
            match = task_pattern.match(line)
            if match:
                indent, status, text = match.groups()
                tasks.append({
                    'text': text.strip(),
                    'completed': status.lower() == 'x',
                    'line_number': i + 1,
                    'indent_level': len(indent),
                    'raw_line': line
                })
        
        return tasks
    
    def extract_metadata(self, content: str) -> Dict[str, str]:
        """Extract metadata from note (title, date, tags)"""
        metadata = {}
        lines = content.split('\n')
        
        # Extract title (first non-empty line)
        for line in lines:
            if line.strip():
                metadata['title'] = line.strip().lstrip('#').strip()
                break
        
        # Extract date from various formats
        date_patterns = [
            r'Date:\s*(\d{4}-\d{2}-\d{2})',
            r'(\d{4}-\d{2}-\d{2})',
            r'(\d{1,2}/\d{1,2}/\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, content)
            if match:
                metadata['date'] = match.group(1)
                break
        
        # Extract tags (hashtags)
        tags = re.findall(r'#(\w+)', content)
        if tags:
            metadata['tags'] = list(set(tags))
        
        return metadata
    
    def find_notes_with_tasks(self) -> List[Tuple[Path, List[Dict]]]:
        """Find all notes containing tasks"""
        notes_with_tasks = []
        
        for note_path in self.list_notes():
            try:
                content = self.read_note(note_path.relative_to(self.notes_path))
                tasks = self.parse_tasks(content)
                if tasks:
                    notes_with_tasks.append((note_path, tasks))
            except Exception as e:
                rprint(f"[red]Error reading {note_path}: {e}[/red]")
        
        return notes_with_tasks
    
    def get_daily_note(self, date: Optional[datetime] = None) -> Optional[str]:
        """Get the daily note for a specific date"""
        if date is None:
            date = datetime.now()
        
        # NotePlan daily note format
        date_str = date.strftime("%Y-%m-%d")
        
        # Check Calendar directory first
        calendar_path = self.notes_path.parent / "Calendar" / f"{date_str}.txt"
        if calendar_path.exists():
            with open(calendar_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        # Check Notes directory
        for note_path in self.list_notes(f"*{date_str}*"):
            return self.read_note(note_path.relative_to(self.notes_path))
        
        return None


if __name__ == "__main__":
    # Test the reader
    try:
        reader = NotePlanReader()
        rprint(f"[green]NotePlan notes directory: {reader.notes_path}[/green]")
        
        # List some notes
        notes = reader.list_notes()[:5]
        rprint(f"\n[cyan]Found {len(reader.list_notes())} notes. First 5:[/cyan]")
        for note in notes:
            rprint(f"  - {note.relative_to(reader.notes_path)}")
        
        # Find notes with tasks
        notes_with_tasks = reader.find_notes_with_tasks()
        rprint(f"\n[cyan]Found {len(notes_with_tasks)} notes with tasks[/cyan]")
        
        if notes_with_tasks:
            note_path, tasks = notes_with_tasks[0]
            rprint(f"\n[yellow]Tasks in {note_path.name}:[/yellow]")
            for task in tasks[:3]:
                status = "✓" if task['completed'] else "○"
                rprint(f"  {status} {task['text']}")
    
    except Exception as e:
        rprint(f"[red]Error: {e}[/red]")