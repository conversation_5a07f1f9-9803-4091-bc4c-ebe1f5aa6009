"""
Calendar-specific functions for NotePlan
"""
from datetime import datetime, timedelta
from pathlib import Path
import re


class NotePlanCalendar:
    """Handle calendar-specific operations for NotePlan"""
    
    def __init__(self, calendar_path=None):
        """Initialize with calendar directory path"""
        if calendar_path is None:
            # Default to standard NotePlan calendar location
            base_path = Path(__file__).parents[5]
            calendar_path = base_path / "Calendar"
        self.calendar_path = Path(calendar_path)
        
    def get_calendar_entries(self, start_date=None, end_date=None):
        """Get all calendar entries within a date range
        
        Args:
            start_date: datetime object (defaults to today)
            end_date: datetime object (defaults to 7 days from start)
            
        Returns:
            Dict with dates as keys, containing events, tasks, and checkboxes
        """
        if start_date is None:
            start_date = datetime.now()
        if end_date is None:
            end_date = start_date + timedelta(days=7)
            
        calendar_entries = {}
        
        # Iterate through date range
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime("%Y%m%d")
            calendar_file = self.calendar_path / f"{date_str}.txt"
            
            if calendar_file.exists():
                with open(calendar_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Extract all types of entries
                entries = {
                    'date': current_date.strftime("%Y-%m-%d"),
                    'day_name': current_date.strftime("%A"),
                    'events': self._extract_events(content),
                    'tasks': self._extract_tasks(content),
                    'checkboxes': self._extract_checkboxes(content),
                    'raw_content': content
                }
                
                calendar_entries[date_str] = entries
                
            current_date += timedelta(days=1)
            
        return calendar_entries
    
    def add_calendar_event(self, date, time_str, event_text):
        """Add an event to a specific calendar date
        
        Args:
            date: datetime object or string in YYYYMMDD format
            time_str: Time string in HH:MM format
            event_text: Event description
            
        Returns:
            Success message
        """
        # Convert date to string if needed
        if isinstance(date, datetime):
            date_str = date.strftime("%Y%m%d")
        else:
            date_str = date
            
        calendar_file = self.calendar_path / f"{date_str}.txt"
        new_line = f"{time_str} {event_text}"
        
        if calendar_file.exists():
            # Read existing content
            with open(calendar_file, 'r', encoding='utf-8') as f:
                content = f.read()
            lines = content.split('\n')
            
            # Find correct position to insert (time order)
            inserted = False
            for i, line in enumerate(lines):
                # Check if this line has a time
                if re.match(r'^\d{1,2}:\d{2}', line.strip()):
                    existing_time = line.strip().split()[0]
                    if existing_time > time_str:
                        lines.insert(i, new_line)
                        inserted = True
                        break
                        
            if not inserted:
                # Add at end if no later time found
                lines.append(new_line)
                
            # Write back
            with open(calendar_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
        else:
            # Create new calendar file
            calendar_file.parent.mkdir(parents=True, exist_ok=True)
            with open(calendar_file, 'w', encoding='utf-8') as f:
                f.write(new_line)
            
        return f"Added: {time_str} {event_text} to {date_str}"
    
    def create_ical_event(self, date, time_str, event_text, duration_hours=1):
        """Create an iCal format event string
        
        Args:
            date: datetime object or string
            time_str: Time string in HH:MM format
            event_text: Event description
            duration_hours: Event duration in hours
            
        Returns:
            iCal format string
        """
        # Parse date and time
        if isinstance(date, str):
            date = datetime.strptime(date, "%Y%m%d")
        
        hour, minute = map(int, time_str.split(':'))
        start_time = date.replace(hour=hour, minute=minute)
        end_time = start_time + timedelta(hours=duration_hours)
        
        # Generate iCal format
        ical = f"""BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//NotePlan//PA System//EN
BEGIN:VEVENT
UID:{datetime.now().timestamp()}@noteplan.pa
DTSTAMP:{datetime.now().strftime('%Y%m%dT%H%M%SZ')}
DTSTART:{start_time.strftime('%Y%m%dT%H%M%S')}
DTEND:{end_time.strftime('%Y%m%dT%H%M%S')}
SUMMARY:{event_text}
END:VEVENT
END:VCALENDAR"""
        
        return ical
    
    def _extract_events(self, content):
        """Extract events (lines starting with time) from content"""
        lines = content.split('\n')
        events = []
        time_pattern = re.compile(r'^\d{1,2}:\d{2}')
        for line in lines:
            if time_pattern.match(line.strip()):
                events.append(line.strip())
        return events
    
    def _extract_tasks(self, content):
        """Extract tasks (- [ ] or - [x]) from content"""
        lines = content.split('\n')
        tasks = []
        for line in lines:
            if line.strip().startswith('- [ ]') or line.strip().startswith('- [x]'):
                tasks.append(line.strip())
        return tasks
    
    def _extract_checkboxes(self, content):
        """Extract checkboxes (+ [ ] or + [x]) from content"""
        lines = content.split('\n')
        checkboxes = []
        for line in lines:
            if line.strip().startswith('+ [ ]') or line.strip().startswith('+ [x]'):
                checkboxes.append(line.strip())
        return checkboxes


if __name__ == "__main__":
    # Test the calendar functions
    from rich import print as rprint
    
    cal = NotePlanCalendar()
    
    # Test getting calendar entries
    rprint("[cyan]Calendar entries for next 7 days:[/cyan]")
    entries = cal.get_calendar_entries()
    
    for date_str, data in entries.items():
        rprint(f"\n[yellow]{data['day_name']}, {data['date']}:[/yellow]")
        if data['events']:
            rprint("  Events:")
            for event in data['events']:
                rprint(f"    {event}")
        if data['tasks']:
            rprint("  Tasks:")
            for task in data['tasks'][:3]:
                rprint(f"    {task}")
    
    # Verify Thursday prep time
    thursday = datetime(2025, 7, 17)
    thursday_str = thursday.strftime("%Y%m%d")
    if thursday_str in entries:
        rprint(f"\n[green]Thursday {thursday.strftime('%B %d')} schedule confirmed:[/green]")
        for event in entries[thursday_str]['events']:
            rprint(f"  {event}")