"""
Todo list monitor that watches bridge files for changes and runs Claude Code
"""
import difflib
import json
import re
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from rich import print as rprint
from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from rich.text import Text

from NotePlanBot.config import CFG
from NotePlanBot.files.bridge.bridge_model import Bridge
from NotePlanBot.utils.bridge_utils import _get_bridge


class TodoListMonitor:
    def __init__(self, bridge_file_path: str):
        self.bridge_file_path = Path(bridge_file_path)
        self.bridge = Bridge()
        # Bridge history is now managed in the Bridge model itself
        self.check_interval = CFG.get('TODO_CHECK_INTERVAL', 30)
        self.console = Console()
        self.last_check_time = None
        self.check_count = 0
        
        # Set up CC session logging directory
        self.cc_logs_dir = Path(__file__).parent / "files" / "logs" / "cc_sessions"
        self.cc_logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize with current bridge content
        current_content = self._get_current_bridge_content()
        if current_content:
            self.bridge.add_to_history(current_content)
    
    def _get_current_bridge_content(self) -> Optional[str]:
        """Get the current content of the bridge file"""
        try:
            return _get_bridge(str(self.bridge_file_path))
        except Exception as e:
            rprint(f"[red]Error reading bridge file: {e}[/red]")
            return None
    
    def _count_line_changes(self, old_content: str, new_content: str) -> int:
        """Count the number of changed lines between two contents"""
        if not old_content or not new_content:
            return 0
            
        old_lines = old_content.splitlines()
        new_lines = new_content.splitlines()
        
        differ = difflib.unified_diff(old_lines, new_lines, lineterm='')
        changes = sum(1 for line in differ if line.startswith(('+', '-')) and not line.startswith(('+++', '---')))
        
        return changes // 2  # Divide by 2 since each change shows as both + and -
    
    def _parse_tasks_from_bridge(self, bridge_content: str) -> List[Dict[str, Any]]:
        """Parse tasks from bridge content TASKS section"""
        tasks = []
        
        # Find TASKS section
        if "TASKS" not in bridge_content:
            return tasks
        
        # Extract content after TASKS section
        tasks_section = bridge_content.split("TASKS")[1]
        
        # Split by lines and parse
        lines = tasks_section.split('\n')
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Parse numbered tasks (1. 2. etc.)
            if re.match(r'^\d+\.', line):
                # Check if task is completed
                completed = '[x]' in line.lower() or '✅' in line
                # Extract task text
                text = re.sub(r'^\d+\.\s*(\[.\])?\s*', '', line)
                tasks.append({
                    'text': text,
                    'completed': completed
                })
            # Parse sub-tasks (1.1, 1.2, etc.)
            elif re.match(r'^\s+\d+\.\d+', line):
                completed = '[x]' in line.lower() or '✅' in line
                text = re.sub(r'^\s+\d+\.\d+\s*', '', line)
                tasks.append({
                    'text': f"  {text}",  # Indent subtasks
                    'completed': completed
                })
                
        return tasks
    
    def _run_claude_code(self, prompt: str):
        """Run Claude Code with the given prompt"""
        try:
            # Build context for Claude Code
            context = self.build_context_for_claude()
            
            # Combine context with prompt
            full_prompt = f"""# CONTEXT FOR CLAUDE CODE SESSION

{context}

# CURRENT REQUEST

{prompt}

---
Please consider the context above when responding. Focus on the current request while being aware of recent activities and task status."""
            
            # Use full claude path from config
            claude_cmd = CFG.get('CLAUDE_COMMAND', '/Users/<USER>/.claude/local/claude')
            cmd = [claude_cmd, "--dangerously-skip-permissions", "-p", full_prompt]
            rprint(f"[cyan]Running Claude Code with context: {claude_cmd} -p ...[/cyan]")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False  # Don't raise on non-zero exit
            )
            
            # Log the session
            log_file = self.log_cc_session(
                prompt=prompt,
                output=result.stdout,
                stderr=result.stderr,
                exit_code=result.returncode
            )
            
            rprint(f"[green]Claude Code output:[/green]\n{result.stdout}")
            
            if result.stderr:
                rprint(f"[yellow]Claude Code stderr:[/yellow]\n{result.stderr}")
            
            if result.returncode != 0:
                rprint(f"[red]Claude Code exited with code {result.returncode}[/red]")
            
            rprint(f"[dim]Session logged to: {log_file.relative_to(Path(__file__).parent)}[/dim]")
            
            # Update current tasks based on CC output
            # First, re-parse bridge file to get latest tasks
            try:
                bridge = Bridge.from_file(str(self.bridge_file_path))
                # Parse tasks from bridge content
                bridge_tasks = self._parse_tasks_from_bridge(bridge.bridge)
                self.update_current_tasks(result.stdout, bridge_tasks)
            except Exception as e:
                rprint(f"[yellow]Warning: Could not update tasks: {e}[/yellow]")
                
        except FileNotFoundError:
            rprint("[red]Claude Code not found. Make sure 'claude' is in your PATH[/red]")
            # Log the error
            self.log_cc_session(
                prompt=prompt,
                output="",
                stderr="Claude Code binary not found",
                exit_code=-1
            )
    
    def _run_claude_code_streamed(self, prompt: str, callback=None):
        """Run Claude Code with streaming response"""
        try:
            # Build context for Claude Code
            context = self.build_context_for_claude()
            
            # Combine context with prompt
            full_prompt = f"""# CONTEXT FOR CLAUDE CODE SESSION

{context}

# CURRENT REQUEST

{prompt}

---
Please consider the context above when responding. Focus on the current request while being aware of recent activities and task status."""
            
            # Use full claude path from config
            claude_cmd = CFG.get('CLAUDE_COMMAND', '/Users/<USER>/.claude/local/claude')
            cmd = [claude_cmd, "--dangerously-skip-permissions", "-p", full_prompt]
            rprint(f"[cyan]Running Claude Code (streamed) with context: {claude_cmd} -p ...[/cyan]")
            
            # Start process with streaming
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            output_lines = []
            stderr_lines = []
            
            # Stream output in real-time
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_lines.append(output.strip())
                    rprint(f"[dim]>> {output.strip()}[/dim]")
                    
                    # Call callback if provided
                    if callback:
                        callback(output.strip())
            
            # Get any remaining stderr
            stderr_output = process.stderr.read()
            if stderr_output:
                stderr_lines.append(stderr_output)
                rprint(f"[yellow]Claude Code stderr:[/yellow]\n{stderr_output}")
            
            # Wait for process to finish
            exit_code = process.wait()
            
            # Join all output
            full_output = '\n'.join(output_lines)
            full_stderr = '\n'.join(stderr_lines)
            
            # Log the session
            log_file = self.log_cc_session(
                prompt=prompt,
                output=full_output,
                stderr=full_stderr,
                exit_code=exit_code
            )
            
            if exit_code != 0:
                rprint(f"[red]Claude Code exited with code {exit_code}[/red]")
            
            rprint(f"[dim]Session logged to: {log_file.relative_to(Path(__file__).parent)}[/dim]")
            
            # Update current tasks based on CC output
            try:
                bridge = Bridge.from_file(str(self.bridge_file_path))
                bridge_tasks = self._parse_tasks_from_bridge(bridge.bridge)
                self.update_current_tasks(full_output, bridge_tasks)
            except Exception as e:
                rprint(f"[yellow]Warning: Could not update tasks: {e}[/yellow]")
                
            return full_output
                
        except FileNotFoundError:
            rprint("[red]Claude Code not found. Make sure 'claude' is in your PATH[/red]")
            # Log the error
            self.log_cc_session(
                prompt=prompt,
                output="",
                stderr="Claude Code binary not found",
                exit_code=-1
            )
            return ""
    
    def trigger_claude_code_stream(self, prompt: str = None, callback=None):
        """Trigger Claude Code with streaming response - can be called manually"""
        if not prompt:
            # Get current bridge content as prompt
            current_content = self._get_current_bridge_content()
            if not current_content:
                rprint("[red]No bridge content available[/red]")
                return
            
            # Extract tasks from the bridge content
            if "TASKS" in current_content:
                tasks_section = current_content.split("TASKS")[1].split('---')[0]
                prompt = f"Review and update the task list based on these changes:\n{tasks_section}"
            else:
                prompt = f"Review this bridge content and provide suggestions:\n{current_content}"
        
        rprint("[cyan]Triggering Claude Code stream...[/cyan]")
        return self._run_claude_code_streamed(prompt, callback)
    
    def get_status_text(self) -> str:
        """Get status text for display"""
        if self.last_check_time:
            time_str = self.last_check_time.strftime("%I:%M:%S%p").lower()
            return f"[cyan]Last checked: {time_str} - No changes detected[/cyan]\n[dim]Check #{self.check_count} | Interval: {self.check_interval}s[/dim]"
        else:
            return f"[yellow]Starting monitor...[/yellow]\n[dim]Check interval: {self.check_interval}s[/dim]"
    
    def create_dashboard(self) -> Table:
        """Create a sticky Rich Dashboard showing current tasks"""
        table = Table(title="📋 TODO LIST DASHBOARD", show_header=True, header_style="bold magenta")
        table.add_column("ID", style="cyan", width=3)
        table.add_column("Status", style="green", width=6)
        table.add_column("Task", style="white", width=50)
        table.add_column("Tags", style="yellow", width=8)
        table.add_column("Added", style="dim", width=12)
        
        try:
            # Get current bridge tasks
            current_content = self._get_current_bridge_content()
            if current_content:
                bridge_tasks = self._parse_tasks_from_bridge(current_content)
                
                # Load task tracking for timestamps
                tasks_file = Path(__file__).parent / "files" / "todolists" / "current_tasks.md"
                tracking_file = tasks_file.parent / "task_tracking.json"
                tracking_data = {}
                
                if tracking_file.exists():
                    try:
                        with open(tracking_file, 'r') as f:
                            tracking_data = json.load(f)
                    except Exception:
                        pass
                
                # Add tasks to table
                for i, task in enumerate(bridge_tasks, 1):
                    task_text = task.get('text', '')
                    completed = task.get('completed', False)
                    
                    # Status with emoji
                    status = "✅ Done" if completed else "⏳ Todo"
                    
                    # Check if task is new
                    is_new = False
                    added_time = "Unknown"
                    if task_text in tracking_data:
                        is_new = tracking_data[task_text].get('is_new', False)
                        first_seen = tracking_data[task_text].get('first_seen', '')
                        if first_seen:
                            try:
                                dt = datetime.fromisoformat(first_seen)
                                added_time = dt.strftime("%I:%M%p").lower()
                            except Exception:
                                pass
                    
                    # Tags
                    tags = ""
                    if is_new:
                        tags = "[[NEW]]"
                    
                    # Truncate long task text
                    display_text = task_text[:47] + "..." if len(task_text) > 50 else task_text
                    
                    table.add_row(
                        str(i),
                        status,
                        display_text,
                        tags,
                        added_time
                    )
                
                # Add summary row
                total_tasks = len(bridge_tasks)
                completed_tasks = sum(1 for task in bridge_tasks if task.get('completed', False))
                new_tasks = sum(1 for task in bridge_tasks if tracking_data.get(task.get('text', ''), {}).get('is_new', False))
                
                table.add_section()
                table.add_row(
                    "📊",
                    f"{completed_tasks}/{total_tasks}",
                    f"Tasks: {total_tasks} | Completed: {completed_tasks} | New: {new_tasks}",
                    "",
                    datetime.now().strftime("%I:%M%p").lower()
                )
                
        except Exception as e:
            table.add_row("❌", "Error", f"Could not load tasks: {str(e)}", "", "")
            
        return table
    
    def log_cc_session(self, prompt: str, output: str, stderr: str = "", exit_code: int = 0) -> Path:
        """Log a Claude Code session and return the log file path"""
        timestamp = datetime.now()
        session_id = timestamp.strftime("%Y%m%d_%H%M%S")
        log_file = self.cc_logs_dir / f"session_{session_id}.md"
        
        # Create session log
        log_content = f"""# Claude Code Session - {timestamp.strftime("%Y-%m-%d %H:%M:%S")}

## Prompt
```
{prompt}
```

## Output
```
{output}
```

## Exit Code: {exit_code}
"""
        
        if stderr:
            log_content += f"\n## Stderr\n```\n{stderr}\n```\n"
        
        # Write log file
        log_file.write_text(log_content)
        
        # Update index
        self.update_cc_index(session_id, timestamp, prompt, exit_code)
        
        return log_file
    
    def update_cc_index(self, session_id: str, timestamp: datetime, prompt: str, exit_code: int):
        """Update the CC sessions index file"""
        index_file = self.cc_logs_dir / "index.md"
        
        # Read existing index or create new
        if index_file.exists():
            content = index_file.read_text()
        else:
            content = """# Claude Code Sessions Index

This file tracks all Claude Code runs triggered by the bridge monitor.

| Timestamp | Session ID | Status | Summary | Log File |
|-----------|------------|--------|---------|----------|
"""
        
        # Extract summary (first line of prompt)
        summary = prompt.split('\n')[0][:50] + "..." if len(prompt) > 50 else prompt.split('\n')[0]
        
        # Add new entry
        status = "✅ Success" if exit_code == 0 else f"❌ Failed ({exit_code})"
        new_entry = f"| {timestamp.strftime('%Y-%m-%d %H:%M:%S')} | {session_id} | {status} | {summary} | [session_{session_id}.md](session_{session_id}.md) |\n"
        
        # Insert new entry after header (at line 5)
        lines = content.split('\n')
        lines.insert(5, new_entry)
        
        # Write updated index
        index_file.write_text('\n'.join(lines))
        
        # Also update AI research log
        self.update_ai_research_log(session_id, timestamp, prompt, exit_code)
    
    def update_ai_research_log(self, session_id: str, timestamp: datetime, prompt: str, exit_code: int):
        """Update the AI research log with session summary"""
        ai_log_file = Path(__file__).parent / "files" / "logs" / "ai_research_log.md"
        
        # Read existing log or create new
        if ai_log_file.exists():
            content = ai_log_file.read_text()
        else:
            content = """# AI Research Log

This log tracks Claude Code activities and suggestions from the NotePlanBot system.

---

"""
        
        # Create session entry
        status = "✅" if exit_code == 0 else "❌"
        session_entry = f"""
## {status} CC Session - {timestamp.strftime("%Y-%m-%d %H:%M:%S")}
**Session ID:** {session_id}  
**Prompt Summary:** {prompt.split(chr(10))[0][:80]}...  
**Log:** [View full session log](cc_sessions/session_{session_id}.md)

---
"""
        
        # Append to log
        content += session_entry
        ai_log_file.parent.mkdir(parents=True, exist_ok=True)
        ai_log_file.write_text(content)
    
    def update_current_tasks(self, cc_output: str, bridge_tasks: List[Dict[str, Any]]):
        """Update current_tasks.md based on CC output and bridge tasks"""
        tasks_file = Path(__file__).parent / "files" / "todolists" / "current_tasks.md"
        now = datetime.now()
        timestamp_24h = now.strftime("%Y-%m-%d %H:%M")
        timestamp_12h = now.strftime("%Y-%m-%d %I:%M%p").lower()
        
        # Load existing tasks to track NEW tags
        existing_tasks = self._load_existing_tasks(tasks_file)
        
        # Parse CC output for task changes
        completed_markers = ["✅", "completed", "done", "[x]", "Fixed", "Created", "Implemented"]
        new_tasks = []
        completed_tasks = []
        suggestions = []
        
        for line in cc_output.split('\n'):
            # Check for completed tasks
            if any(marker in line for marker in completed_markers):
                completed_tasks.append(line.strip())
            # Check for new tasks mentioned
            if "TODO:" in line or "TASK:" in line or "- [ ]" in line:
                new_tasks.append(line.strip())
            # Check for suggestions mentioned
            if "suggestion:" in line.lower() or "suggest" in line.lower():
                suggestions.append(line.strip())
        
        # Update suggestions.md file
        if suggestions:
            self.update_suggestions_file(suggestions, timestamp_24h)
        
        # Build updated task list
        content = f"""# TODO LIST - Managed by Claude Code
Last Updated: {timestamp_12h} (Auto-updated)

## ACTIVE TASKS (from bridge.md):
"""
        
        # Add tasks from bridge file with NEW tags
        for i, task in enumerate(bridge_tasks, 1):
            status = "[x]" if task.get('completed') else "[ ]"
            text = task.get('text', '')
            
            # Check if this is a new task
            new_tag = ""
            if self._is_new_task(text, existing_tasks):
                new_tag = " [[NEW]]"
                
            content += f"{i}. {status} {text}{new_tag}\n"
            
            # Check if CC added suggestions
            suggestion_match = re.search(r'\*\*\[suggestion: ([^\]]+)\]\*\*', cc_output)
            if suggestion_match and text in cc_output:
                content += f"   **[suggestion: {suggestion_match.group(1)}]**\n"
        
        # Add CC session summary with 12-hour format
        content += f"""
## LAST CC SESSION ({timestamp_12h}):
### Tasks Completed:
"""
        for task in completed_tasks[:5]:  # Limit to 5 most recent
            content += f"- {task}\n"
        
        if new_tasks:
            content += "\n### New Tasks Identified:\n"
            for task in new_tasks[:5]:
                content += f"- {task}{' [[NEW]]' if self._is_new_task(task, existing_tasks) else ''}\n"
        
        if suggestions:
            content += "\n### Suggestions Created:\n"
            for suggestion in suggestions[:3]:
                content += f"- {suggestion}\n"
        
        content += f"""
---
*Auto-managed by NotePlanBot - Last sync: {timestamp_12h}*
"""
        
        tasks_file.parent.mkdir(parents=True, exist_ok=True)
        tasks_file.write_text(content)
        
        # Update task tracking for NEW tag management
        self._update_task_tracking(bridge_tasks, tasks_file)
        
    def _load_existing_tasks(self, tasks_file: Path) -> List[str]:
        """Load existing task texts from file"""
        if not tasks_file.exists():
            return []
            
        try:
            content = tasks_file.read_text()
            tasks = []
            for line in content.split('\n'):
                if line.strip() and (line.strip().startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.', '11.', '12.', '13.', '14.', '15.'))):
                    # Extract task text without status and NEW tags
                    task_text = re.sub(r'^\d+\.\s*\[.\]\s*', '', line.strip())
                    task_text = re.sub(r'\s*\[\[NEW\]\]', '', task_text)
                    tasks.append(task_text)
            return tasks
        except Exception:
            return []
    
    def _is_new_task(self, task_text: str, existing_tasks: List[str]) -> bool:
        """Check if a task is new compared to existing tasks"""
        # Clean task text for comparison
        clean_text = task_text.strip()
        return clean_text not in existing_tasks and len(clean_text) > 0
    
    def _update_task_tracking(self, bridge_tasks: List[Dict[str, Any]], tasks_file: Path):
        """Update task tracking for NEW tag management"""
        # Load or create task tracking file
        tracking_file = tasks_file.parent / "task_tracking.json"
        
        try:
            if tracking_file.exists():
                with open(tracking_file, 'r') as f:
                    tracking_data = json.load(f)
            else:
                tracking_data = {}
            
            # Update edit counts for existing tasks
            for task in bridge_tasks:
                text = task.get('text', '').strip()
                if text in tracking_data:
                    tracking_data[text]['edits'] += 1
                else:
                    tracking_data[text] = {
                        'edits': 0,
                        'first_seen': datetime.now().isoformat(),
                        'is_new': True
                    }
            
            # Mark tasks as no longer new after CFG threshold
            edits_until_new_removed = CFG.get('EDITS_UNTIL_NEW_REMOVED', 5)
            for task_text in tracking_data:
                if tracking_data[task_text]['edits'] >= edits_until_new_removed:
                    tracking_data[task_text]['is_new'] = False
            
            # Save updated tracking
            with open(tracking_file, 'w') as f:
                json.dump(tracking_data, f, indent=2)
                
        except Exception as e:
            rprint(f"[yellow]Warning: Could not update task tracking: {e}[/yellow]")
    
    def update_suggestions_file(self, suggestions: List[str], timestamp: str):
        """Update suggestions.md file with new suggestions"""
        suggestions_file = Path(__file__).parent / "files" / "suggestions" / "suggestions.md"
        suggestions_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Read existing suggestions or create new file
        if suggestions_file.exists():
            content = suggestions_file.read_text()
        else:
            content = """# Claude Code Suggestions Log

This file tracks suggestions made by Claude Code sessions.

Each line represents a suggestion with format:
`[timestamp] [suggestion_id]: [description] -> [filepath/dirname]`

---

"""
        
        # Add new suggestions
        for i, suggestion in enumerate(suggestions):
            suggestion_id = f"cc_{timestamp.replace(' ', '_').replace(':', '').replace('-', '')}_{i+1}"
            
            # Create suggestion directory
            suggestion_dir = suggestions_file.parent / suggestion_id
            suggestion_dir.mkdir(exist_ok=True)
            
            # Create suggestion detail file
            suggestion_detail_file = suggestion_dir / "suggestion.md"
            suggestion_detail_file.write_text(f"""# Suggestion {suggestion_id}

**Created:** {timestamp}
**Source:** Claude Code session

## Description
{suggestion}

## Files Created
- suggestion.md (this file)

## Status
- [ ] Reviewed
- [ ] Implemented
- [ ] Tested
- [ ] Closed

---
*Auto-generated by NotePlanBot*
""")
            
            # Add to main suggestions file
            suggestion_line = f"[{timestamp}] {suggestion_id}: {suggestion[:80]}... -> {suggestion_id}/\n"
            content += suggestion_line
        
        # Write updated suggestions file
        suggestions_file.write_text(content)
    
    def build_context_for_claude(self) -> str:
        """Build context summary for new Claude Code instances"""
        context_lines = []
        
        # Add current bridge content
        current_bridge = self._get_current_bridge_content()
        if current_bridge:
            context_lines.append("## CURRENT BRIDGE CONTENT")
            context_lines.append(current_bridge)
            context_lines.append("")
        
        # Add recent CC sessions summary
        context_lines.append("## RECENT CLAUDE CODE SESSIONS")
        recent_sessions = self._get_recent_sessions(limit=3)
        for session in recent_sessions:
            context_lines.append(f"### Session {session['id']} ({session['timestamp']})")
            context_lines.append(f"**Status:** {session['status']}")
            context_lines.append(f"**Summary:** {session['summary']}")
            context_lines.append("")
        
        # Add current task status
        try:
            bridge = Bridge.from_file(str(self.bridge_file_path))
            bridge_tasks = self._parse_tasks_from_bridge(bridge.bridge)
            if bridge_tasks:
                context_lines.append("## CURRENT TASK STATUS")
                for i, task in enumerate(bridge_tasks, 1):
                    status = "✅" if task.get('completed') else "⏳"
                    context_lines.append(f"{i}. {status} {task.get('text', '')}")
                context_lines.append("")
        except Exception as e:
            context_lines.append(f"## TASK STATUS: Error loading - {e}")
            context_lines.append("")
        
        # Add recent suggestions
        suggestions_file = Path(__file__).parent / "files" / "suggestions" / "suggestions.md"
        if suggestions_file.exists():
            try:
                suggestions_content = suggestions_file.read_text()
                lines = suggestions_content.split('\n')
                recent_suggestions = [line for line in lines if line.startswith('[') and ']' in line][-3:]
                if recent_suggestions:
                    context_lines.append("## RECENT SUGGESTIONS")
                    for suggestion in recent_suggestions:
                        context_lines.append(f"- {suggestion}")
                    context_lines.append("")
            except Exception:
                pass
        
        return "\n".join(context_lines)
    
    def _get_recent_sessions(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent Claude Code sessions"""
        sessions = []
        
        # Read from CC sessions index
        index_file = self.cc_logs_dir / "index.md"
        if index_file.exists():
            try:
                content = index_file.read_text()
                lines = content.split('\n')
                
                # Find table entries (skip header)
                for line in lines:
                    if line.startswith('| 20') and '|' in line:  # Table row with timestamp
                        parts = [p.strip() for p in line.split('|')]
                        if len(parts) >= 5:
                            timestamp = parts[1]
                            session_id = parts[2]
                            status = parts[3]
                            summary = parts[4]
                            
                            sessions.append({
                                'id': session_id,
                                'timestamp': timestamp,
                                'status': status,
                                'summary': summary
                            })
                            
                            if len(sessions) >= limit:
                                break
                                
            except Exception as e:
                rprint(f"[yellow]Warning: Could not read recent sessions: {e}[/yellow]")
        
        return sessions
    
    def check_for_changes(self) -> bool:
        """Check if bridge file has > 1 LOC change from previous check"""
        current_content = self._get_current_bridge_content()
        if not current_content:
            return False
        
        if not self.bridge.bridge_history:
            self.bridge.add_to_history(current_content)
            return False
        
        # Get the last recorded content
        _, last_content = self.bridge.bridge_history[-1]
        
        # Count line changes
        changes = self._count_line_changes(last_content, current_content)
        
        if changes > 1:
            rprint(f"[yellow]Detected {changes} line changes in bridge file[/yellow]")
            self.bridge.add_to_history(current_content)
            return True
        
        return False
    
    def run(self):
        """Main monitoring loop"""
        rprint(f"[green]Starting TodoList monitor for {self.bridge_file_path}[/green]")
        rprint(f"[green]Checking every {self.check_interval} seconds for > 1 LOC changes[/green]")
        
        with Live(refresh_per_second=1) as live:
            while True:
                try:
                    # Update status display
                    status_panel = Panel(
                        self.get_status_text(),
                        title="Monitor Status",
                        border_style="cyan"
                    )
                    live.update(status_panel)
                    
                    if self.check_for_changes():
                        # Clear the status when changes are detected
                        live.stop()
                        
                        # Extract tasks from the bridge content
                        current_content = self.bridge.bridge_history[-1][1]
                        
                        # Simple task extraction - look for TASKS section
                        if "TASKS" in current_content:
                            tasks_section = current_content.split("TASKS")[1].split('"""')[0]
                            prompt = f"Review and update the task list based on these changes:\n{tasks_section}"
                            self._run_claude_code(prompt)
                        else:
                            rprint("[yellow]No TASKS section found in bridge file[/yellow]")
                        
                        # Restart live display
                        live.start()
                        # Reset last check time since we found changes
                        self.last_check_time = None
                    else:
                        # Update last check time when no changes found
                        self.last_check_time = datetime.now()
                        self.check_count += 1
                    
                    time.sleep(self.check_interval)
                    
                except KeyboardInterrupt:
                    rprint("\n[red]Stopping TodoList monitor[/red]")
                    break
                except Exception as e:
                    rprint(f"[red]Error in monitoring loop: {e}[/red]")
                    time.sleep(self.check_interval)


if __name__ == "__main__":
    # Monitor bridge.md file
    import os
    bridge_file = os.path.join(
        os.path.dirname(__file__),
        "files", "bridge", "bridge.md"
    )
    
    if not os.path.exists(bridge_file):
        rprint(f"[red]Bridge file not found: {bridge_file}[/red]")
        rprint("[yellow]Creating default bridge.md file...[/yellow]")
        os.makedirs(os.path.dirname(bridge_file), exist_ok=True)
        with open(bridge_file, 'w') as f:
            f.write("""# Bridge File
Date: 2025-01-07

## Current Context
Working on NotePlanBot three-pane system setup.

## TASKS
1. [ ] Set up three-pane VSCode layout
2. [ ] Test monitor functionality
3. [ ] Implement Claude Code integration

## Notes
- Monitor checks this file periodically
- Changes > 1 LOC trigger Claude Code
""")
    
    monitor = TodoListMonitor(bridge_file)
    monitor.run()