"""
NotePlanBot - Main application logic
Orchestrates the three-pane system: bridge files, todo lists, and AI suggestions
"""
import os
import sys
import threading
from pathlib import Path
from typing import Optional

from rich import print as rprint
from rich.console import Console

from NotePlanBot.config import CFG
from NotePlanBot.todolist_monitor import TodoListMonitor

console = Console()


class NotePlanBot:
    """Main NotePlanBot application"""
    
    def __init__(self, bridge_file_path: Optional[str] = None):
        """Initialize NotePlanBot with bridge file path"""
        if bridge_file_path is None:
            # Default bridge file location
            bridge_file_path = os.path.join(
                os.path.dirname(__file__),
                "files", "bridge", "bridge.md"
            )
        
        self.bridge_file_path = Path(bridge_file_path)
        self.monitor = None
        self.monitor_thread = None
        
    def setup_bridge_file(self):
        """Ensure bridge file exists with default content"""
        if not self.bridge_file_path.exists():
            rprint(f"[yellow]Creating bridge file at: {self.bridge_file_path}[/yellow]")
            self.bridge_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.bridge_file_path, 'w') as f:
                f.write("""# Bridge File
Date: 2025-01-07

## Current Context
Working on NotePlanBot three-pane system.

## TASKS
1. [ ] Monitor this file for changes
2. [ ] Trigger Claude Code on > 1 LOC changes
3. [ ] Update todo list automatically

## Notes
- Monitor checks this file periodically
- Edit this file to trigger AI assistance
- Tasks are managed by Claude Code
""")
    
    def start_monitor(self):
        """Start the todo list monitor in a separate thread"""
        rprint("[green]Starting NotePlanBot monitor...[/green]")
        self.monitor = TodoListMonitor(str(self.bridge_file_path))
        
        # Run monitor in background thread
        self.monitor_thread = threading.Thread(
            target=self.monitor.run,
            daemon=True
        )
        self.monitor_thread.start()
        
    def run(self):
        """Run the NotePlanBot application"""
        console.print("[bold blue]NotePlanBot v0.1.0[/bold blue]")
        console.print("A three-pane system for NotePlan task management\n")
        
        # Setup
        self.setup_bridge_file()
        
        # Start monitoring
        self.start_monitor()
        
        # Keep main thread alive
        console.print(f"[cyan]Monitoring bridge file: {self.bridge_file_path}[/cyan]")
        console.print(f"[cyan]Check interval: {CFG.get('TODO_CHECK_INTERVAL', 30)} seconds[/cyan]")
        console.print("\n[yellow]Edit the bridge file to trigger AI assistance[/yellow]")
        console.print("[dim]Press Ctrl+C to stop[/dim]\n")
        
        try:
            # Keep the main thread running
            while True:
                if self.monitor_thread and not self.monitor_thread.is_alive():
                    rprint("[red]Monitor thread stopped unexpectedly[/red]")
                    break
                # Sleep to prevent busy waiting
                threading.Event().wait(1)
        except KeyboardInterrupt:
            console.print("\n[red]Stopping NotePlanBot...[/red]")


def main():
    """Main entry point for NotePlanBot"""
    # Check for custom bridge file path from command line
    bridge_file = None
    if len(sys.argv) > 1:
        bridge_file = sys.argv[1]
    
    # Create and run bot
    bot = NotePlanBot(bridge_file)
    bot.run()


if __name__ == "__main__":
    main()