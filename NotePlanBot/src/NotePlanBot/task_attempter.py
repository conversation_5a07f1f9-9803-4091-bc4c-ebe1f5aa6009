"""
Background Task Attempter + Suggestions Format System

This module provides a system for Claude<PERSON><PERSON> to safely attempt tasks
and document suggestions in a clean format.
"""
import json
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

from rich.console import Console
from rich.table import Table

from NotePlanBot.config import CFG

console = Console()


class TaskAttempt:
    """Represents a single task attempt with results and suggestions"""
    
    def __init__(self, task_id: str, task_description: str, attempt_id: str = None):
        self.task_id = task_id
        self.task_description = task_description
        self.attempt_id = attempt_id or f"attempt_{int(time.time())}"
        self.timestamp = datetime.now().isoformat()
        self.status = "pending"  # pending, running, completed, failed
        self.output = []
        self.suggestions = []
        self.files_created = []
        self.files_modified = []
        self.command_run = None
        self.exit_code = None
        
    def add_output(self, output_type: str, content: str):
        """Add output to the attempt"""
        self.output.append({
            "type": output_type,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
    def add_suggestion(self, suggestion: str, filename: str = None):
        """Add a suggestion with optional filename"""
        self.suggestions.append({
            "suggestion": suggestion,
            "filename": filename,
            "timestamp": datetime.now().isoformat()
        })
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "task_id": self.task_id,
            "task_description": self.task_description,
            "attempt_id": self.attempt_id,
            "timestamp": self.timestamp,
            "status": self.status,
            "output": self.output,
            "suggestions": self.suggestions,
            "files_created": self.files_created,
            "files_modified": self.files_modified,
            "command_run": self.command_run,
            "exit_code": self.exit_code
        }


class BackgroundTaskAttempter:
    """
    Safely attempts tasks in the background and documents results
    """
    
    def __init__(self, suggestions_dir: str = None):
        self.suggestions_dir = Path(suggestions_dir or CFG.get('SUGGESTIONS_DIR', 'suggestions'))
        self.suggestions_dir.mkdir(exist_ok=True)
        self.attempts_file = self.suggestions_dir / "attempts.json"
        self.attempts_log = self.suggestions_dir / "attempts.log"
        self.active_attempts = {}
        
        # Load existing attempts
        self.load_attempts()
        
    def load_attempts(self):
        """Load existing attempts from disk"""
        if self.attempts_file.exists():
            try:
                with open(self.attempts_file, 'r') as f:
                    data = json.load(f)
                    self.active_attempts = {
                        k: TaskAttempt(**v) for k, v in data.items()
                    }
            except Exception as e:
                console.print(f"[red]Error loading attempts: {e}[/red]")
                
    def save_attempts(self):
        """Save attempts to disk"""
        try:
            data = {k: v.to_dict() for k, v in self.active_attempts.items()}
            with open(self.attempts_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            console.print(f"[red]Error saving attempts: {e}[/red]")
            
    def create_attempt(self, task_id: str, task_description: str) -> TaskAttempt:
        """Create a new task attempt"""
        attempt = TaskAttempt(task_id, task_description)
        self.active_attempts[attempt.attempt_id] = attempt
        self.save_attempts()
        
        console.print(f"[cyan]Created task attempt: {attempt.attempt_id}[/cyan]")
        return attempt
        
    def attempt_task_safely(self, task_id: str, task_description: str, 
                           command: str = None, timeout: int = 300) -> TaskAttempt:
        """
        Safely attempt a task with ClaudeCode
        
        Args:
            task_id: Unique identifier for the task
            task_description: Description of what to attempt
            command: Optional specific command to run
            timeout: Timeout in seconds (default 5 minutes)
            
        Returns:
            TaskAttempt object with results
        """
        attempt = self.create_attempt(task_id, task_description)
        
        try:
            attempt.status = "running"
            self.save_attempts()
            
            # Build ClaudeCode command
            if command:
                cmd = [CFG.get('CLAUDE_COMMAND', 'claude'), '-p', command]
            else:
                cmd = [CFG.get('CLAUDE_COMMAND', 'claude'), '-p', task_description]
                
            attempt.command_run = ' '.join(cmd)
            attempt.add_output("info", f"Running command: {attempt.command_run}")
            
            # Run with timeout and capture output
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            attempt.exit_code = result.returncode
            
            if result.stdout:
                attempt.add_output("stdout", result.stdout)
                
            if result.stderr:
                attempt.add_output("stderr", result.stderr)
                
            # Determine success/failure
            if result.returncode == 0:
                attempt.status = "completed"
                attempt.add_suggestion(
                    f"Task completed successfully: {task_description}",
                    self._generate_suggestion_filename(task_id)
                )
            else:
                attempt.status = "failed"
                attempt.add_suggestion(
                    f"Task failed with exit code {result.returncode}: {task_description}",
                    self._generate_suggestion_filename(task_id)
                )
                
        except subprocess.TimeoutExpired:
            attempt.status = "failed"
            attempt.add_output("error", f"Task timed out after {timeout} seconds")
            attempt.add_suggestion(
                f"Task timed out - consider breaking into smaller tasks: {task_description}",
                self._generate_suggestion_filename(task_id)
            )
            
        except Exception as e:
            attempt.status = "failed"
            attempt.add_output("error", str(e))
            attempt.add_suggestion(
                f"Task failed with exception: {str(e)}",
                self._generate_suggestion_filename(task_id)
            )
            
        finally:
            self.save_attempts()
            self._write_suggestion_file(attempt)
            
        return attempt
        
    def _generate_suggestion_filename(self, task_id: str) -> str:
        """Generate a filename for suggestions"""
        safe_id = "".join(c for c in task_id if c.isalnum() or c in "-_").lower()
        return f"suggestion_{safe_id}.md"
        
    def _write_suggestion_file(self, attempt: TaskAttempt):
        """Write a suggestion file for the attempt"""
        filename = self._generate_suggestion_filename(attempt.task_id)
        suggestion_file = self.suggestions_dir / filename
        
        content = f"""# Task Attempt: {attempt.task_id}

**Description:** {attempt.task_description}
**Status:** {attempt.status}
**Timestamp:** {attempt.timestamp}

## Command Run
```bash
{attempt.command_run}
```

## Output
"""
        
        for output in attempt.output:
            content += f"""
### {output['type'].upper()}
```
{output['content']}
```
"""
            
        if attempt.suggestions:
            content += "\n## Suggestions\n"
            for suggestion in attempt.suggestions:
                content += f"- {suggestion['suggestion']}\n"
                
        try:
            with open(suggestion_file, 'w') as f:
                f.write(content)
                
            console.print(f"[green]Suggestion written to: {filename}[/green]")
            
        except Exception as e:
            console.print(f"[red]Error writing suggestion file: {e}[/red]")
            
    def get_suggestions_summary(self) -> str:
        """Get a summary of all suggestions in the format requested"""
        summary = []
        
        for attempt in self.active_attempts.values():
            for suggestion in attempt.suggestions:
                filename = suggestion.get('filename', 'general')
                summary.append(f"**[suggestion: {filename}]** {suggestion['suggestion']}")
                
        return "\n".join(summary)
        
    def display_attempts_table(self):
        """Display a table of all attempts"""
        table = Table(title="Task Attempts")
        table.add_column("ID", style="cyan")
        table.add_column("Description", style="white")
        table.add_column("Status", style="magenta")
        table.add_column("Timestamp", style="green")
        
        for attempt in self.active_attempts.values():
            status_color = {
                "completed": "[green]✓[/green]",
                "failed": "[red]✗[/red]",
                "running": "[yellow]⏳[/yellow]",
                "pending": "[blue]⏸[/blue]"
            }.get(attempt.status, attempt.status)
            
            table.add_row(
                attempt.attempt_id,
                attempt.task_description[:50] + "..." if len(attempt.task_description) > 50 else attempt.task_description,
                status_color,
                attempt.timestamp.split('T')[0]
            )
            
        console.print(table)


# Integration with TodoListMonitor
def create_attempter_hook(bridge_file_path: str) -> BackgroundTaskAttempter:
    """
    Create a task attempter that can be used with the TodoListMonitor
    
    Args:
        bridge_file_path: Path to the bridge file
        
    Returns:
        BackgroundTaskAttempter instance
    """
    suggestions_dir = Path(bridge_file_path).parent / "suggestions"
    attempter = BackgroundTaskAttempter(str(suggestions_dir))
    
    return attempter


# CLI interface for testing
if __name__ == "__main__":
    console.print("[bold green]Testing Background Task Attempter[/bold green]\n")
    
    # Create test attempter
    attempter = BackgroundTaskAttempter("test_suggestions")
    
    # Test task attempt
    test_task = "Create a simple Python function that adds two numbers"
    attempt = attempter.attempt_task_safely(
        "test_add_function",
        test_task,
        command="Create a simple Python function called add_numbers that takes two parameters and returns their sum"
    )
    
    console.print(f"\n[yellow]Attempt Status:[/yellow] {attempt.status}")
    console.print(f"[yellow]Attempt ID:[/yellow] {attempt.attempt_id}")
    
    # Display attempts table
    attempter.display_attempts_table()
    
    # Show suggestions summary
    console.print("\n[blue]Suggestions Summary:[/blue]")
    console.print(attempter.get_suggestions_summary())