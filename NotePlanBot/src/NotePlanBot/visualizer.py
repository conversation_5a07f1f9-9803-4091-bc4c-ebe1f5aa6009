"""
Three-pane visualizer for NotePlanBot
Provides a terminal UI showing bridge file, tasks, and AI suggestions
"""
import time
from datetime import datetime
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table
from rich.text import Text

from NotePlanBot.config import CFG
from NotePlanBot.reader.noteplan_reader import Note<PERSON><PERSON><PERSON>eader
from NotePlanBot.utils.bridge_utils import _get_bridge


class ThreePaneVisualizer:
    """Terminal UI for the three-pane NotePlanBot system"""
    
    def __init__(self, bridge_file: Optional[str] = None):
        self.console = Console()
        self.bridge_file = Path(bridge_file or CFG.get('BRIDGE_FILE'))
        self.reader = NotePlanReader()
        self.layout = self._create_layout()
        self.running = False
        
        # State tracking
        self.bridge_content = ""
        self.tasks = []
        self.ai_log = []
        self.check_count = 0
        self.last_check = None
        self.last_change = None
        
    def _create_layout(self) -> Layout:
        """Create the three-pane layout"""
        layout = Layout()
        
        # Create header and body
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # Split body into three panes
        layout["body"].split_row(
            Layout(name="bridge", ratio=1),
            Layout(name="tasks", ratio=1),
            Layout(name="ai_log", ratio=1)
        )
        
        return layout
    
    def _update_header(self):
        """Update header panel"""
        header_text = Text("🤖 NotePlanBot Three-Pane System", style="bold blue")
        header_text.append(f" | Monitoring: {self.bridge_file.name}", style="cyan")
        header_text.append(f" | Checks: {self.check_count}", style="green")
        self.layout["header"].update(Panel(header_text, style="blue"))
    
    def _update_bridge_pane(self):
        """Update bridge file pane"""
        try:
            self.bridge_content = _get_bridge(str(self.bridge_file))
            # Use syntax highlighting for markdown
            syntax = Syntax(
                self.bridge_content,
                "markdown",
                theme="monokai",
                line_numbers=True,
                word_wrap=True
            )
            self.layout["bridge"].update(
                Panel(syntax, title="[bold cyan]Bridge File (Pane 1)[/bold cyan]", border_style="cyan")
            )
        except Exception as e:
            self.layout["bridge"].update(
                Panel(f"[red]Error reading bridge file: {e}[/red]", title="Bridge File", border_style="red")
            )
    
    def _update_tasks_pane(self):
        """Update tasks pane"""
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Status", width=8)
        table.add_column("Task", ratio=1)
        table.add_column("Source", width=20)
        
        # Get tasks from NotePlan
        try:
            # Get today's tasks
            daily_content = self.reader.get_daily_note()
            if daily_content:
                daily_tasks = self.reader.parse_tasks(daily_content)
                for task in daily_tasks[:10]:  # Limit to 10 tasks
                    status = "✅" if task['completed'] else "⬜"
                    table.add_row(status, task['text'], "Today")
            
            # Get tasks from bridge file
            if hasattr(self, 'bridge') and self.bridge and self.bridge.ongoing_tasks:
                for task in self.bridge.ongoing_tasks:
                    status = "✅" if task.get('completed') else "⬜"
                    table.add_row(status, task.get('text', ''), "Bridge")
        except Exception as e:
            table.add_row("❌", f"Error loading tasks: {e}", "System")
        
        self.layout["tasks"].update(
            Panel(table, title="[bold magenta]Task List (Pane 2)[/bold magenta]", border_style="magenta")
        )
    
    def _update_ai_log_pane(self):
        """Update AI suggestions/log pane"""
        log_content = Text()
        
        # Add recent AI interactions
        for i, entry in enumerate(self.ai_log[-10:]):  # Last 10 entries
            timestamp = entry.get('timestamp', '')
            message = entry.get('message', '')
            log_content.append(f"[{timestamp}] ", style="dim")
            log_content.append(f"{message}\n", style="green")
        
        # Add status info
        if self.last_check:
            log_content.append(f"\nLast check: {self.last_check}", style="dim")
        if self.last_change:
            log_content.append(f"\nLast change: {self.last_change}", style="yellow")
        
        self.layout["ai_log"].update(
            Panel(log_content or "[dim]Waiting for AI suggestions...[/dim]", 
                  title="[bold yellow]AI Log (Pane 3)[/bold yellow]", 
                  border_style="yellow")
        )
    
    def _update_footer(self):
        """Update footer panel"""
        footer_text = Text()
        footer_text.append("Press ", style="dim")
        footer_text.append("Ctrl+C", style="bold red")
        footer_text.append(" to exit | ", style="dim")
        footer_text.append("Edit bridge.md", style="bold cyan")
        footer_text.append(" to trigger AI | ", style="dim")
        footer_text.append(f"Interval: {CFG.get('TODO_CHECK_INTERVAL')}s", style="green")
        
        self.layout["footer"].update(Panel(footer_text, style="dim"))
    
    def add_ai_log(self, message: str):
        """Add entry to AI log"""
        self.ai_log.append({
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'message': message
        })
    
    def update_display(self):
        """Update all panes"""
        self.check_count += 1
        self.last_check = datetime.now().strftime("%H:%M:%S")
        
        self._update_header()
        self._update_bridge_pane()
        self._update_tasks_pane()
        self._update_ai_log_pane()
        self._update_footer()
    
    def run(self):
        """Run the visualizer"""
        self.running = True
        
        with Live(self.layout, refresh_per_second=1, screen=True):
            while self.running:
                try:
                    self.update_display()
                    time.sleep(CFG.get('TODO_CHECK_INTERVAL', 30))
                except KeyboardInterrupt:
                    self.running = False
                    break
                except Exception as e:
                    self.add_ai_log(f"Error: {e}")
        
        self.console.print("\n[red]Visualizer stopped[/red]")


def main():
    """Run the three-pane visualizer"""
    visualizer = ThreePaneVisualizer()
    
    # Add initial log entry
    visualizer.add_ai_log("NotePlanBot visualizer started")
    visualizer.add_ai_log("Monitoring bridge file for changes...")
    
    try:
        visualizer.run()
    except KeyboardInterrupt:
        print("\nStopping visualizer...")


if __name__ == "__main__":
    main()