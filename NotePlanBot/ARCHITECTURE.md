# NotePlanBot Architecture

## How It All Works

NotePlanBot uses a layered monitoring system with different UI options:

### Core Components

1. **Bridge File** (`src/NotePlanBot/files/bridge/bridge.md`)
   - A markdown file where you type context, tasks, and requests
   - Monitored for changes every 30 seconds
   - When > 1 line changes, triggers Claude Code

2. **Monitor Engine** (`todolist_monitor.py`)
   - The core monitoring logic
   - Tracks file changes using difflib
   - Runs Claude Code via subprocess when triggered
   - Can run standalone or be embedded in UIs

3. **NotePlan Integration**
   - **Reader**: Scans NotePlan notes for tasks
   - **Editor**: Creates/updates notes and task status
   - Works with both Notes/ and Calendar/ directories

### UI Options (from simple to complex)

1. **No UI** (`just run`)
   - Bare monitoring in console
   - Shows basic print statements
   - Good for background operation

2. **Simple Status UI** (`just monitor-simple`)
   - Shows monitoring status in a nice table
   - Updates every second with status info
   - Runs monitor in background thread
   - Good for checking if system is working

3. **Full Three-Pane UI** (`just monitor-ui` or `just monitor`)
   - **Left pane**: Bridge file with syntax highlighting
   - **Middle pane**: Task list from NotePlan
   - **Right pane**: AI suggestions and log
   - Full terminal UI with Rich library
   - This is the recommended interface

### Workflow

1. Start with `just monitor` (full UI)
2. Edit bridge.md in your editor (VSCode/Vim/etc)
3. Type your context and tasks
4. Save with > 1 line change
5. Claude Code automatically triggered
6. See results in the AI log pane

### File Flow

```
You edit → bridge.md → Monitor detects → Claude Code runs → Updates tasks
                ↓                               ↓
           UI displays                   NotePlan notes updated
```

### Quick Start

```bash
# First time setup
just install
just venv
source .venv/bin/activate

# Run with full UI
just monitor

# Or run specific variants
just run              # No UI
just monitor-simple   # Basic status
just monitor-ui       # Full three-pane
```