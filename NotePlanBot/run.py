#!/usr/bin/env python3
"""
NotePlanBot - Main entry point
A bot that interfaces with NotePlan for task management using a three-pane system
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

# Load environment variables
load_dotenv()

# Import after path setup
from NotePlanBot.main import main


def run():
    """Run the NotePlanBot application"""
    try:
        main()
    except KeyboardInterrupt:
        print("\nNotePlanBot stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error running NotePlanBot: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run()