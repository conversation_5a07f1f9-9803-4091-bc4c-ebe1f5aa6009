# Three-Pane System Workflow Documentation

## Overview

The NotePlanBot implements a three-pane workflow system for AI-assisted task management:

1. **Bridge File** (Left) - User input and context
2. **Task List** (Middle) - AI-managed tasks from NotePlan  
3. **AI Log** (Right) - Claude Code suggestions and actions

## System Architecture

### Bridge File Monitoring (`todolist_monitor.py`)
**Primary Responsibility:** Monitor bridge.md for changes and trigger Claude Code

**Workflow:**
1. Monitors `bridge.md` every `TODO_CHECK_INTERVAL` seconds (default: 30)
2. Uses `difflib` to detect > 1 line of changes (LOC)
3. Triggers Claude Code subprocess when changes detected
4. Logs all Claude Code sessions with timestamps
5. Updates task list based on Claude Code output

**Key Methods:**
- `check_for_changes()` - Detects file changes using difflib
- `_run_claude_code()` - Executes Claude Code subprocess
- `_run_claude_code_streamed()` - Streams Claude Code responses
- `trigger_claude_code_stream()` - Manual trigger for testing

### Task List Management (`current_tasks.md`)
**Primary Responsibility:** Display and track active tasks from bridge.md

**Workflow:**
1. Parses TASKS section from bridge.md content
2. Identifies new tasks and marks with [[NEW]] tags
3. Tracks task completion status (✅ vs ⏳)
4. Removes [[NEW]] tags after 5 edits (configurable)
5. Updates with 12-hour format timestamps (e.g., 12:30pm)

**Features:**
- **[[NEW]] Tag System:** Automatically tracks new tasks
- **Task Tracking:** JSON persistence for edit counts
- **Rich Dashboard:** Beautiful table format with emojis
- **Timestamp Formatting:** 12-hour format throughout

### AI Log System (`cc_sessions/` + `ai_research_log.md`)
**Primary Responsibility:** Log Claude Code activities and suggestions

**Workflow:**
1. Every Claude Code session creates a timestamped log file
2. Sessions are indexed in `cc_sessions/index.md`
3. Summaries added to `ai_research_log.md`
4. Exit codes and errors tracked
5. Context building for future Claude Code instances

## Three-Pane System Roles

### 1. Bridge File (User Input)
- **Role:** User types context, activities, and requests
- **Format:** Markdown with TASKS section
- **Monitoring:** Checked every 30 seconds for changes
- **Trigger:** > 1 line of changes triggers Claude Code

### 2. Task List (AI Management)
- **Role:** AI-managed tasks parsed from bridge.md
- **Format:** Numbered list with status indicators
- **Updates:** Automatic after each Claude Code session
- **Features:** NEW tags, timestamps, completion tracking

### 3. AI Log (Claude Code Actions)
- **Role:** Claude Code suggestions, actions, and session logs
- **Format:** Markdown logs with structured data
- **Persistence:** JSON tracking + markdown summaries
- **Context:** Builds context for future Claude Code instances

## Data Flow

```
1. User edits bridge.md
     ↓
2. TodoListMonitor detects > 1 LOC change
     ↓
3. Extract TASKS section from bridge.md
     ↓
4. Build context from recent sessions + bridge content
     ↓
5. Trigger Claude Code subprocess with context
     ↓
6. Log Claude Code session (stdout/stderr/exit code)
     ↓
7. Parse Claude Code output for task updates
     ↓
8. Update current_tasks.md with new timestamps/tags
     ↓
9. Update suggestions.md with **[suggestion: filename]** format
     ↓
10. Update ai_research_log.md with session summary
```

## Key Configuration

**Environment Variables:**
- `TODO_CHECK_INTERVAL`: Monitor check interval (default: 30s)
- `CLAUDE_COMMAND`: Path to Claude Code binary
- `EDITS_UNTIL_NEW_REMOVED`: Edits before removing [[NEW]] tag (default: 5)

**File Locations:**
- `bridge.md`: User input file (monitored)
- `current_tasks.md`: AI-managed task list
- `cc_sessions/`: Claude Code session logs
- `suggestions.md`: AI suggestions in **[suggestion: filename]** format
- `task_tracking.json`: NEW tag tracking persistence

## Monitoring vs Task Management

### TodoListMonitor (Bridge Monitoring)
- **Purpose:** Watch bridge.md for changes
- **Trigger:** File change detection
- **Action:** Run Claude Code subprocess
- **Output:** Session logs and task updates

### TaskList (Task Management)
- **Purpose:** Display and track tasks
- **Source:** Parses bridge.md TASKS section
- **Updates:** After each Claude Code session
- **Features:** NEW tags, timestamps, completion status

### Bridge vs Task List Relationship
- **Bridge File:** Contains user input + TASKS section
- **Task List:** Extracted and formatted view of TASKS section
- **Synchronization:** Task list updates after bridge changes trigger Claude Code
- **Context:** Full bridge content (not just tasks) passed to Claude Code

## Context Sharing Solution

### Problem
New Claude Code instances need context of past actions to understand what's happening and what to do next.

### Solution: Context Building System
1. **Recent Sessions:** Last 3 Claude Code sessions with summaries
2. **Current Bridge Content:** Full bridge.md content
3. **Task Status:** Current task completion status
4. **Recent Suggestions:** Last 3 suggestions from suggestions.md
5. **Session Index:** Structured log of all past sessions

### Implementation
- `build_context_for_claude()` method creates comprehensive context
- Context includes session summaries, task status, and recent activities
- Passed to Claude Code via full_prompt parameter
- Enables continuity across Claude Code instances

## Color Emoji System

The three-pane system uses consistent emoji indicators:

### Task Status
- ⏳ **Todo:** Pending task
- ✅ **Done:** Completed task
- 📋 **Dashboard:** Task list header

### Session Status
- ✅ **Success:** Claude Code session completed successfully
- ❌ **Failed:** Claude Code session failed
- 🔄 **Running:** Currently processing

### File Types
- 📝 **Bridge:** User input file
- 📊 **Tasks:** Task list file
- 🤖 **AI Log:** Claude Code session log

## Testing and Debugging

### Manual Testing
```bash
# Test bridge monitoring
python -m src.NotePlanBot.todolist_monitor

# Test trigger function
python -c "
from src.NotePlanBot.todolist_monitor import TodoListMonitor
monitor = TodoListMonitor('bridge.md')
monitor.trigger_claude_code_stream('Test prompt')
"

# Test dashboard
python -c "
from src.NotePlanBot.todolist_monitor import TodoListMonitor
monitor = TodoListMonitor('bridge.md')
dashboard = monitor.create_dashboard()
print(dashboard)
"
```

### Log Files
- `cc_sessions/session_*.md`: Individual session logs
- `cc_sessions/index.md`: Session index table
- `ai_research_log.md`: Session summaries
- `task_tracking.json`: NEW tag persistence

## Future Enhancements

1. **Real-time Dashboard:** Live updating Rich dashboard
2. **Voice Integration:** Voice-to-text bridge input
3. **Multi-bridge Support:** Multiple bridge files
4. **Advanced Filtering:** Task filtering by status/tags
5. **Integration APIs:** REST API for external tools