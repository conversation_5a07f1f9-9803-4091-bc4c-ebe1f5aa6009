{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Gmail Tools Tutorial\n", "## A Comprehensive Guide to Gmail API Functions\n", "\n", "This notebook provides a complete tutorial and demonstration of all Gmail tool functions available in the system. We'll work backwards from the complete system architecture to individual functions.\n", "\n", "### Table of Contents\n", "1. [System Architecture Overview](#architecture)\n", "2. [Core Components](#core)\n", "3. [Authentication & Service](#auth)\n", "4. [Email Operations](#operations)\n", "5. [AI-Powered Features](#ai)\n", "6. [Task & Snooze Management](#management)\n", "7. [Project Context Storage](#storage)\n", "8. [UI Functions](#ui)\n", "\n", "---\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. System Architecture Overview <a id=\"architecture\"></a>\n", "\n", "The Gmail tools system is designed as a modular architecture with several interconnected components. Here's the complete system overview:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First, let's set up our environment and create system architecture diagram\n", "import sys\n", "import os\n", "from IPython.display import Image, display\n", "import base64\n", "\n", "# Add the src directory to the Python path\n", "sys.path.append(os.path.join(os.getcwd(), 'src'))\n", "\n", "# Create mermaid diagram for system architecture\n", "mermaid_code = \"\"\"\n", "graph TB\n", "    subgraph \"Gmail Tools System\"\n", "        A[Gmail Auth<br/>Authentication & Token Management] --> B[Gmail Service<br/>Core API Operations]\n", "        \n", "        B --> C[UI Module<br/>Interactive Console]\n", "        B --> D[Email Operations]\n", "        B --> E[AI Features]\n", "        B --> F[Management Features]\n", "        \n", "        subgraph \"Core Operations\"\n", "            D --> D1[List Messages]\n", "            D --> D2[Get Thread]\n", "            D --> D3[Send Email]\n", "            D --> D4[<PERSON>]\n", "        end\n", "        \n", "        subgraph \"AI-Powered Features\"\n", "            E --> E1[Email Grouper<br/>Smart Categorization]\n", "            E --> E2[Draft Replies<br/>Claude <PERSON>]\n", "            E --> E3[Find Projects<br/>Context Extraction]\n", "            E --> E4[Context Retrieval<br/>Semantic Search]\n", "        end\n", "        \n", "        subgraph \"Management\"\n", "            F --> F1[Task Manager<br/>Email to Tasks]\n", "            F --> F2[Snooze Manager<br/>Time-based Filtering]\n", "        end\n", "        \n", "        G[Project Store<br/>Excel Storage] --> E2\n", "        G --> E3\n", "        G --> E4\n", "        \n", "        H[Models<br/>Data Structures] --> F1\n", "        H --> F2\n", "        H --> E1\n", "    end\n", "    \n", "    style A fill:#f9d71c\n", "    style B fill:#27ae60\n", "    style E1 fill:#3498db\n", "    style E2 fill:#3498db\n", "    style E3 fill:#3498db\n", "    style E4 fill:#3498db\n", "\"\"\"\n", "\n", "# Note: In a real notebook, you would use a mermaid renderer\n", "# For now, we'll just display the diagram code\n", "print(\"System Architecture Diagram (Mermaid):\")\n", "print(mermaid_code)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. Core Components <a id=\"core\"></a>\n", "\n", "### Data Models\n", "The system uses strongly-typed data models to ensure consistency across all modules:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import and showcase the data models\n", "from dataclasses import dataclass\n", "from datetime import datetime\n", "from typing import Optional, List, Dict\n", "from enum import Enum\n", "\n", "# Task Management Models\n", "class TaskStatus(str, Enum):\n", "    \"\"\"Task status options.\"\"\"\n", "    PENDING = \"pending\"\n", "    IN_PROGRESS = \"in_progress\" \n", "    COMPLETED = \"completed\"\n", "    CANCELLED = \"cancelled\"\n", "\n", "@dataclass\n", "class Task:\n", "    \"\"\"Task created from email.\"\"\"\n", "    id: str\n", "    title: str\n", "    description: str\n", "    deadline: Optional[datetime]\n", "    source_thread_id: str\n", "    created_at: datetime\n", "    status: TaskStatus\n", "\n", "# Snooze Management Models\n", "@dataclass\n", "class SnoozedEmail:\n", "    \"\"\"Snoozed email entry.\"\"\"\n", "    thread_id: str\n", "    message_id: str\n", "    snooze_until: datetime\n", "    original_position: int\n", "    snoozed_at: datetime\n", "\n", "# Email Grouping Models\n", "@dataclass\n", "class EmailGroup:\n", "    \"\"\"Email grouping result from AI analysis.\"\"\"\n", "    groups: Dict[int, str]  # Group ID to name mapping\n", "    assignments: Dict[str, int]  # Thread ID to group ID\n", "    reasoning: str  # LLM reasoning\n", "    created_at: datetime\n", "\n", "# Display example usage\n", "print(\"Example Task:\")\n", "example_task = Task(\n", "    id=\"task-001\",\n", "    title=\"Review Q4 budget proposal\",\n", "    description=\"Review and provide feedback on the Q4 budget\",\n", "    deadline=datetime(2024, 3, 15),\n", "    source_thread_id=\"thread_12345\",\n", "    created_at=datetime.now(),\n", "    status=TaskStatus.PENDING\n", ")\n", "print(f\"Task: {example_task.title}\")\n", "print(f\"Status: {example_task.status.value}\")\n", "print(f\"Deadline: {example_task.deadline.strftime('%Y-%m-%d') if example_task.deadline else 'None'}\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. Authentication & Service <a id=\"auth\"></a>\n", "\n", "### Gmail Authentication\n", "The authentication module handles OAuth2 flow and token management:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gmail Authentication Functions\n", "import pickle\n", "from typing import List, Optional\n", "\n", "class GmailAuth:\n", "    \"\"\"Gmail API authentication handler.\"\"\"\n", "    \n", "    DEFAULT_SCOPES = [\n", "        'https://www.googleapis.com/auth/gmail.send',\n", "        'https://www.googleapis.com/auth/gmail.readonly',\n", "        'https://www.googleapis.com/auth/gmail.modify'\n", "    ]\n", "    \n", "    def __init__(self, token_path: str = 'gmail_token.pickle', creds_path: str = 'creds.json'):\n", "        self.token_path = token_path\n", "        self.creds_path = creds_path\n", "        self.scopes = self.DEFAULT_SCOPES\n", "        self._creds = None\n", "    \n", "    def get_service(self):\n", "        \"\"\"Get authenticated Gmail service.\"\"\"\n", "        # In production, this would:\n", "        # 1. Check for existing token\n", "        # 2. Refresh if expired\n", "        # 3. Create new token if needed\n", "        # 4. Return authenticated service\n", "        print(f\"📧 Authenticating with Gmail API...\")\n", "        print(f\"   Token path: {self.token_path}\")\n", "        print(f\"   Scopes: {', '.join(self.scopes)}\")\n", "        return \"MockGmailService\"  # Placeholder for demo\n", "\n", "# Example usage\n", "auth = GmailAuth()\n", "print(\"\\n🔐 Gmail Authentication Configuration:\")\n", "print(f\"Token storage: {auth.token_path}\")\n", "print(f\"Credentials file: {auth.creds_path}\")\n", "print(f\"Required scopes: {len(auth.scopes)}\")\n", "\n", "# In production: service = auth.get_service()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. Email Operations <a id=\"operations\"></a>\n", "\n", "### Gmail Service Class\n", "The Gmail Service provides all core email operations:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gmail Service - Core Email Operations\n", "from typing import List, Dict, Optional\n", "from datetime import datetime, timedelta\n", "\n", "class GmailService:\n", "    \"\"\"Handle Gmail API operations.\"\"\"\n", "    \n", "    SYSTEM_LABELS = {\n", "        'INBOX': '📥 Inbox',\n", "        'UNREAD': '🔵 Unread',\n", "        'IMPORTANT': '⭐ Important',\n", "        'CATEGORY_PERSONAL': '👤 Primary',\n", "        'CATEGORY_SOCIAL': '👥 Social',\n", "        'CATEGORY_PROMOTIONS': '🏷️ Promotions',\n", "        'CATEGORY_UPDATES': '🔔 Updates',\n", "        'CATEGORY_FORUMS': '💬 Forums',\n", "        'DRAFT': '📝 Draft',\n", "        'SENT': '➡️ Sent',\n", "        'SPAM': '⚠️ Spam',\n", "        'TRASH': '🗑️ Trash'\n", "    }\n", "    \n", "    def __init__(self):\n", "        # In production, this would initialize with real Gmail service\n", "        self.service = None\n", "        self.labels = {}\n", "    \n", "    def list_unread_messages(self, max_results: int = 10, days_back: int = 7, \n", "                           label_filter: Optional[List[str]] = None) -> List[Dict]:\n", "        \"\"\"Get unread messages with filtering options.\"\"\"\n", "        # Demo implementation\n", "        print(f\"📧 Fetching unread messages...\")\n", "        print(f\"   Max results: {max_results}\")\n", "        print(f\"   Days back: {days_back}\")\n", "        print(f\"   Label filter: {label_filter or 'All labels'}\")\n", "        \n", "        # Mock data for demonstration\n", "        return [\n", "            {\n", "                'id': 'msg_001',\n", "                'thread_id': 'thread_001',\n", "                'subject': 'Q4 Budget Review',\n", "                'from': '<PERSON> <<EMAIL>>',\n", "                'date': 'Mon, 25 Mar 2024 10:30:00 -0700',\n", "                'labels': ['CATEGORY_PERSONAL', 'INBOX'],\n", "                'snippet': 'Hi, please review the attached Q4 budget...'\n", "            },\n", "            {\n", "                'id': 'msg_002',\n", "                'thread_id': 'thread_002',\n", "                'subject': 'Project Alpha Update',\n", "                'from': '<PERSON> <<EMAIL>>',\n", "                'date': 'Mon, 25 Mar 2024 09:15:00 -0700',\n", "                'labels': ['CATEGORY_PERSONAL', 'INBOX', 'IMPORTANT'],\n", "                'snippet': 'Update on Project Alpha milestones...'\n", "            }\n", "        ]\n", "    \n", "    def get_thread_messages(self, thread_id: str) -> List[Dict]:\n", "        \"\"\"Get all messages in a thread.\"\"\"\n", "        print(f\"📬 Fetching thread: {thread_id}\")\n", "        \n", "        # Mock thread data\n", "        return [\n", "            {\n", "                'from': '<PERSON> <<EMAIL>>',\n", "                'date': 'Mon, 25 Mar 2024 10:30:00 -0700',\n", "                'content': 'Hi team,\\n\\nPlease review the attached Q4 budget proposal.',\n", "                'labels': ['CATEGORY_PERSONAL', 'INBOX']\n", "            },\n", "            {\n", "                'from': 'You <<EMAIL>>',\n", "                'date': 'Mon, 25 Mar 2024 11:00:00 -0700',\n", "                'content': 'Thanks <PERSON>, I\\'ll review this today.',\n", "                'labels': ['SENT']\n", "            }\n", "        ]\n", "    \n", "    def send_message(self, to: str, subject: str, body: str, \n", "                    reply_to: Optional[Dict] = None) -> Optional[str]:\n", "        \"\"\"Send an email message.\"\"\"\n", "        print(f\"✉️ Sending email:\")\n", "        print(f\"   To: {to}\")\n", "        print(f\"   Subject: {subject}\")\n", "        print(f\"   Reply to: {reply_to.get('message_id') if reply_to else 'New thread'}\")\n", "        print(f\"   Body length: {len(body)} chars\")\n", "        \n", "        return \"msg_sent_001\"  # <PERSON><PERSON> sent message ID\n", "    \n", "    def mark_as_read(self, message_id: str) -> bool:\n", "        \"\"\"Mark a message as read by removing UNREAD label.\"\"\"\n", "        print(f\"✅ Marking message {message_id} as read\")\n", "        return True\n", "    \n", "    def get_label_names(self, label_ids: List[str]) -> List[str]:\n", "        \"\"\"Convert label IDs to readable names.\"\"\"\n", "        return [self.SYSTEM_LABELS.get(lid, lid) for lid in label_ids]\n", "\n", "# Demonstrate the Gmail Service\n", "service = GmailService()\n", "\n", "print(\"🏷️ Available System Labels:\")\n", "for label_id, label_name in service.SYSTEM_LABELS.items():\n", "    print(f\"   {label_id}: {label_name}\")\n", "\n", "print(\"\\n📧 Example Operations:\")\n", "messages = service.list_unread_messages(max_results=5)\n", "print(f\"\\nFound {len(messages)} unread messages\")\n", "\n", "for msg in messages:\n", "    print(f\"\\n📨 {msg['subject']}\")\n", "    print(f\"   From: {msg['from']}\")\n", "    print(f\"   Labels: {', '.join(service.get_label_names(msg['labels']))}\")\n", "    print(f\"   Preview: {msg['snippet'][:50]}...\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. AI-Powered Features <a id=\"ai\"></a>\n", "\n", "### Email Grouper - Smart Categorization\n", "Uses OpenAI GPT-4 to intelligently group emails based on content:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Email Grouper - AI-Powered Smart Categorization\n", "import json\n", "import asyncio\n", "from typing import List, Dict\n", "\n", "class EmailGrouper:\n", "    \"\"\"Group emails using LLM analysis.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # In production, would initialize with OpenAI client\n", "        self.client = None\n", "    \n", "    async def group_emails(self, threads: List[Dict]) -> EmailGroup:\n", "        \"\"\"Group emails intelligently using AI.\"\"\"\n", "        print(\"🤖 Analyzing emails for smart grouping...\")\n", "        \n", "        # Build email summaries for LLM\n", "        email_summaries = []\n", "        for i, thread in enumerate(threads):\n", "            summary = {\n", "                \"index\": i,\n", "                \"thread_id\": thread.get(\"thread_id\", f\"thread_{i}\"),\n", "                \"subject\": thread.get(\"subject\", \"No Subject\"),\n", "                \"participants\": thread.get(\"participants\", []),\n", "                \"preview\": thread.get(\"last_preview\", \"\"),\n", "                \"message_count\": thread.get(\"msg_count\", 0)\n", "            }\n", "            email_summaries.append(summary)\n", "        \n", "        print(f\"📊 Processing {len(email_summaries)} threads...\")\n", "        \n", "        # Mock AI response for demonstration\n", "        mock_response = EmailGroup(\n", "            groups={\n", "                1: \"Budget & Finance\",\n", "                2: \"Project Updates\", \n", "                3: \"Team Communications\",\n", "                4: \"External Partners\"\n", "            },\n", "            assignments={\n", "                \"thread_001\": 1,  # Q4 Budget Review -> Budget & Finance\n", "                \"thread_002\": 2,  # Project Alpha Update -> Project Updates\n", "            },\n", "            reasoning=\"Grouped emails based on subject matter and participants. Budget-related emails were grouped together, project status updates in another group.\",\n", "            created_at=datetime.now()\n", "        )\n", "        \n", "        return mock_response\n", "\n", "# Demonstrate email grouping\n", "async def demo_email_grouping():\n", "    grouper = EmailGrouper()\n", "    \n", "    # Sample email threads\n", "    sample_threads = [\n", "        {\n", "            \"thread_id\": \"thread_001\",\n", "            \"subject\": \"Q4 Budget Review\",\n", "            \"participants\": [\"<EMAIL>\", \"<EMAIL>\"],\n", "            \"last_preview\": \"Please review the attached Q4 budget...\",\n", "            \"msg_count\": 3\n", "        },\n", "        {\n", "            \"thread_id\": \"thread_002\", \n", "            \"subject\": \"Project Alpha Update\",\n", "            \"participants\": [\"<EMAIL>\", \"<EMAIL>\"],\n", "            \"last_preview\": \"Update on Project Alpha milestones...\",\n", "            \"msg_count\": 5\n", "        }\n", "    ]\n", "    \n", "    result = await grouper.group_emails(sample_threads)\n", "    \n", "    print(\"\\n🏷️ Email Groups Created:\")\n", "    for group_id, group_name in result.groups.items():\n", "        print(f\"   Group {group_id}: {group_name}\")\n", "    \n", "    print(\"\\n📧 Email Assignments:\")\n", "    for thread_id, group_id in result.assignments.items():\n", "        group_name = result.groups[group_id]\n", "        print(f\"   {thread_id} → {group_name}\")\n", "    \n", "    print(f\"\\n💭 AI Reasoning: {result.reasoning}\")\n", "\n", "# Run the demo\n", "await demo_email_grouping()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Draft Replies - <PERSON><PERSON><PERSON><PERSON> Email Assistant\n", "Uses <PERSON><PERSON><PERSON>'s <PERSON> to generate contextual email replies:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Draft Replies - AI-Powered Email Response Generation\n", "class ClaudeDraftAssistant:\n", "    \"\"\"Draft email replies using <PERSON>.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # User context for drafting\n", "        self.user_email = \"<EMAIL>\"\n", "        self.user_name = \"<PERSON>\"\n", "        self.user_role = \"R&D Lead at Invisible\"\n", "        # In production, would initialize <PERSON> client\n", "        self.client = None\n", "    \n", "    async def analyze_thread(self, thread_messages: List[Dict]) -> Dict:\n", "        \"\"\"Analyze thread to determine reply context.\"\"\"\n", "        print(\"🔍 Analyzing email thread...\")\n", "        \n", "        # Extract key information\n", "        last_msg = thread_messages[-1]\n", "        last_sender = last_msg['from'].split('<')[0].strip()\n", "        \n", "        # Look for questions and action items\n", "        key_points = []\n", "        content = last_msg.get('content', '')\n", "        \n", "        # Find questions\n", "        if '?' in content:\n", "            questions = [line.strip() for line in content.split('\\n') if '?' in line]\n", "            key_points.extend(questions[:2])\n", "        \n", "        # Find action requests\n", "        action_keywords = ['can you', 'could you', 'please', 'need', 'would you']\n", "        for keyword in action_keywords:\n", "            if keyword in content.lower():\n", "                key_points.append(f\"Action requested: {keyword}\")\n", "        \n", "        return {\n", "            'subject': thread_messages[0].get('subject', 'No Subject'),\n", "            'last_sender': last_sender,\n", "            'key_points': key_points,\n", "            'thread_messages': thread_messages\n", "        }\n", "    \n", "    async def generate_draft(self, thread_analysis: Dict) -> str:\n", "        \"\"\"Generate draft reply using AI.\"\"\"\n", "        print(\"✍️ Generating draft reply...\")\n", "        \n", "        # Mock draft generation for demo\n", "        if thread_analysis['key_points']:\n", "            draft = f\"Hi {thread_analysis['last_sender']},\\n\\n\"\n", "            draft += \"Thank you for your email. \"\n", "            \n", "            if any('?' in point for point in thread_analysis['key_points']):\n", "                draft += \"To answer your questions:\\n\\n\"\n", "                draft += \"• I'll review the budget proposal and provide feedback by EOD.\\n\"\n", "                draft += \"• The timeline looks reasonable, but I'd like to discuss the Q4 targets.\\n\\n\"\n", "            \n", "            draft += \"Let me know if you need any additional information.\\n\\n\"\n", "            draft += f\"Best regards,\\n{self.user_name}\"\n", "        else:\n", "            draft = f\"Hi {thread_analysis['last_sender']},\\n\\n\"\n", "            draft += \"Thanks for the update. I'll review this and get back to you shortly.\\n\\n\"\n", "            draft += f\"Best regards,\\n{self.user_name}\"\n", "        \n", "        return draft\n", "\n", "# Demonstrate draft reply generation\n", "async def demo_draft_replies():\n", "    assistant = <PERSON><PERSON><PERSON><PERSON><PERSON>ant()\n", "    \n", "    # Sample thread\n", "    thread = [\n", "        {\n", "            'from': '<PERSON> <<EMAIL>>',\n", "            'subject': 'Q4 Budget Review',\n", "            'content': '<PERSON> <PERSON>,\\n\\nCan you please review the attached Q4 budget proposal? I need your feedback by Friday.\\n\\nAlso, do you think the timeline is realistic?\\n\\nThanks,\\nJohn'\n", "        }\n", "    ]\n", "    \n", "    # Analyze and generate draft\n", "    analysis = await assistant.analyze_thread(thread)\n", "    draft = await assistant.generate_draft(analysis)\n", "    \n", "    print(f\"\\n📧 Thread Analysis:\")\n", "    print(f\"   Subject: {analysis['subject']}\")\n", "    print(f\"   Last sender: {analysis['last_sender']}\")\n", "    print(f\"   Key points: {len(analysis['key_points'])}\")\n", "    for point in analysis['key_points']:\n", "        print(f\"     • {point}\")\n", "    \n", "    print(f\"\\n📝 Generated Draft:\")\n", "    print(\"─\" * 50)\n", "    print(draft)\n", "    print(\"─\" * 50)\n", "\n", "# Run the demo\n", "await demo_draft_replies()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. Task & Snooze Management <a id=\"management\"></a>\n", "\n", "### Task Manager - Convert Emails to Actionable Tasks\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Task Manager - Email to Task Conversion\n", "import json\n", "import uuid\n", "from pathlib import Path\n", "\n", "class TaskManager:\n", "    \"\"\"Manage tasks created from emails.\"\"\"\n", "    \n", "    def __init__(self, filepath: str = \"gmail_tasks.json\"):\n", "        self.filepath = Path(filepath)\n", "        self.tasks: List[Task] = []\n", "        # In production, would load from file\n", "    \n", "    def create_task(self, title: str, description: str, \n", "                   source_thread_id: str, deadline: Optional[datetime] = None) -> Task:\n", "        \"\"\"Create new task from email.\"\"\"\n", "        task = Task(\n", "            id=str(uuid.uuid4())[:8],\n", "            title=title,\n", "            description=description,\n", "            deadline=deadline,\n", "            source_thread_id=source_thread_id,\n", "            created_at=datetime.now(),\n", "            status=TaskStatus.PENDING\n", "        )\n", "        self.tasks.append(task)\n", "        print(f\"✅ Created task: {task.title}\")\n", "        return task\n", "    \n", "    def generate_task_title(self, email_subject: str, email_content: str = \"\") -> str:\n", "        \"\"\"Generate task title using AI (<PERSON>).\"\"\"\n", "        print(f\"🤖 Generating task title from: {email_subject}\")\n", "        \n", "        # Mock AI-generated title for demo\n", "        if \"budget\" in email_subject.lower():\n", "            return \"Review and approve Q4 budget proposal\"\n", "        elif \"project\" in email_subject.lower():\n", "            return \"Provide feedback on project milestones\"\n", "        else:\n", "            # Fallback: clean up subject\n", "            title = email_subject.replace(\"Re:\", \"\").replace(\"Fwd:\", \"\").strip()\n", "            return f\"Follow up: {title[:40]}\"\n", "    \n", "    def get_active_tasks(self) -> List[Task]:\n", "        \"\"\"Get non-completed tasks.\"\"\"\n", "        active = [t for t in self.tasks \n", "                 if t.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]]\n", "        # Sort by deadline\n", "        active.sort(key=lambda t: (t.deadline is None, t.deadline))\n", "        return active\n", "    \n", "    def update_task_status(self, task_id: str, status: TaskStatus) -> bool:\n", "        \"\"\"Update task status.\"\"\"\n", "        for task in self.tasks:\n", "            if task.id == task_id:\n", "                task.status = status\n", "                print(f\"📝 Updated task {task_id} to {status.value}\")\n", "                return True\n", "        return False\n", "\n", "# Demonstrate task management\n", "task_manager = TaskManager()\n", "\n", "# Create tasks from emails\n", "print(\"📧 Converting emails to tasks:\\n\")\n", "\n", "# Example 1: Budget email\n", "title1 = task_manager.generate_task_title(\n", "    \"Q4 Budget Review\", \n", "    \"Please review the attached budget proposal\"\n", ")\n", "task1 = task_manager.create_task(\n", "    title=title1,\n", "    description=\"Review Q4 budget proposal from <PERSON>\",\n", "    source_thread_id=\"thread_001\",\n", "    deadline=datetime(2024, 3, 29)\n", ")\n", "\n", "# Example 2: Project email  \n", "title2 = task_manager.generate_task_title(\n", "    \"Project Alpha Update\",\n", "    \"Update on project milestones\"\n", ")\n", "task2 = task_manager.create_task(\n", "    title=title2,\n", "    description=\"Review project status and provide feedback\",\n", "    source_thread_id=\"thread_002\"\n", ")\n", "\n", "# Display active tasks\n", "print(\"\\n📋 Active Tasks:\")\n", "for task in task_manager.get_active_tasks():\n", "    deadline_str = task.deadline.strftime(\"%Y-%m-%d\") if task.deadline else \"No deadline\"\n", "    print(f\"   [{task.status.value}] {task.title}\")\n", "    print(f\"        ID: {task.id} | Deadline: {deadline_str}\")\n", "    print(f\"        From: {task.source_thread_id}\\n\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Snooze Manager - Time-based Email Filtering\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Snooze Manager - Time-based Email Filtering\n", "from typing import Set\n", "\n", "class SnoozeManager:\n", "    \"\"\"Manage snoozed emails.\"\"\"\n", "    \n", "    def __init__(self, filepath: str = \"gmail_snooze_queue.json\"):\n", "        self.filepath = Path(filepath)\n", "        self.snoozed: List[SnoozedEmail] = []\n", "        # In production, would load from file\n", "    \n", "    def snooze_email(self, thread_id: str, message_id: str, \n", "                    until: datetime, position: int) -> None:\n", "        \"\"\"Add email to snooze queue.\"\"\"\n", "        # Remove existing snooze for this thread\n", "        self.snoozed = [s for s in self.snoozed if s.thread_id != thread_id]\n", "        \n", "        # Add new snooze\n", "        entry = SnoozedEmail(\n", "            thread_id=thread_id,\n", "            message_id=message_id,\n", "            snooze_until=until,\n", "            original_position=position,\n", "            snoozed_at=datetime.now()\n", "        )\n", "        self.snoozed.append(entry)\n", "        print(f\"😴 Snoozed {thread_id} until {until.strftime('%Y-%m-%d %H:%M')}\")\n", "    \n", "    def get_snoozed_thread_ids(self) -> Set[str]:\n", "        \"\"\"Get currently snoozed thread IDs.\"\"\"\n", "        now = datetime.now()\n", "        active_snoozes = set()\n", "        expired = []\n", "        \n", "        for entry in self.snoozed:\n", "            if entry.snooze_until > now:\n", "                active_snoozes.add(entry.thread_id)\n", "            else:\n", "                expired.append(entry.thread_id)\n", "        \n", "        # Remove expired entries\n", "        if expired:\n", "            self.snoozed = [s for s in self.snoozed if s.thread_id not in expired]\n", "            print(f\"⏰ {len(expired)} emails unsnoozed\")\n", "        \n", "        return active_snoozes\n", "    \n", "    def is_snoozed(self, thread_id: str) -> bool:\n", "        \"\"\"Check if thread is snoozed.\"\"\"\n", "        return thread_id in self.get_snoozed_thread_ids()\n", "\n", "# Demonstrate snooze functionality\n", "snooze_manager = SnoozeManager()\n", "\n", "print(\"😴 Email Snooze Management:\\n\")\n", "\n", "# Snooze some emails\n", "tomorrow = datetime.now() + <PERSON><PERSON><PERSON>(days=1)\n", "next_week = datetime.now() + <PERSON><PERSON><PERSON>(days=7)\n", "\n", "snooze_manager.snooze_email(\n", "    thread_id=\"thread_001\",\n", "    message_id=\"msg_001\",\n", "    until=tomorrow,\n", "    position=0\n", ")\n", "\n", "snooze_manager.snooze_email(\n", "    thread_id=\"thread_003\",\n", "    message_id=\"msg_003\", \n", "    until=next_week,\n", "    position=2\n", ")\n", "\n", "# Check snoozed status\n", "print(f\"\\n📊 Snooze Status:\")\n", "print(f\"   Total snoozed: {len(snooze_manager.snoozed)}\")\n", "print(f\"   Active snoozes: {len(snooze_manager.get_snoozed_thread_ids())}\")\n", "\n", "# Check individual threads\n", "test_threads = [\"thread_001\", \"thread_002\", \"thread_003\"]\n", "for thread_id in test_threads:\n", "    status = \"Snoozed\" if snooze_manager.is_snoozed(thread_id) else \"Active\"\n", "    print(f\"   {thread_id}: {status}\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 7. Project Context Storage <a id=\"storage\"></a>\n", "\n", "### Project Context Store - Excel-based Knowledge Management\n", "Stores project information, RACI matrices, and document contexts:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Project Context Store - Data Structure Diagram\n", "mermaid_project_store = \"\"\"\n", "classDiagram\n", "    class ProjectInfo {\n", "        +name: str\n", "        +description: str\n", "        +priority: int\n", "        +status: str\n", "        +start_date: str\n", "        +end_date: Optional[str]\n", "        +key_stakeholders: List[str]\n", "        +primary_contact: str\n", "        +tags: List[str]\n", "    }\n", "    \n", "    class RACIEntry {\n", "        +project_name: str\n", "        +person_email: str\n", "        +person_name: str\n", "        +role: str [R,A,C,I]\n", "        +notes: Optional[str]\n", "    }\n", "    \n", "    class DocumentContext {\n", "        +project_name: str\n", "        +filepath: str\n", "        +doc_type: str\n", "        +summary: str\n", "        +key_points: List[str]\n", "        +last_updated: str\n", "        +relevance_score: float\n", "    }\n", "    \n", "    class PersonContext {\n", "        +email: str\n", "        +name: str\n", "        +role: str\n", "        +timezone: str\n", "        +preferred_contact: str\n", "        +response_time_hours: int\n", "        +notes: str\n", "        +last_interaction: str\n", "    }\n", "    \n", "    class ProjectContextStore {\n", "        +projects: Dict[str, ProjectInfo]\n", "        +raci_matrix: List[RACIEntry]\n", "        +documents: List[DocumentContext]\n", "        +people: Dict[str, <PERSON><PERSON><PERSON><PERSON><PERSON>]\n", "        +load()\n", "        +save()\n", "        +add_project()\n", "        +get_project_context()\n", "        +search_documents()\n", "    }\n", "    \n", "    ProjectContextStore \"1\" --> \"*\" ProjectInfo\n", "    ProjectContextStore \"1\" --> \"*\" RACIEntry\n", "    ProjectContextStore \"1\" --> \"*\" DocumentContext\n", "    ProjectContextStore \"1\" --> \"*\" PersonContext\n", "    \n", "    RACIEntry --> ProjectInfo : references\n", "    DocumentContext --> ProjectInfo : references\n", "    RACIEntry --> PersonContext : references\n", "\"\"\"\n", "\n", "print(\"Project Context Store Structure (Mermaid):\")\n", "print(mermaid_project_store)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Project Context Store Implementation\n", "from dataclasses import dataclass, asdict\n", "from typing import Any\n", "\n", "@dataclass\n", "class ProjectInfo:\n", "    \"\"\"Core project information.\"\"\"\n", "    name: str\n", "    description: str\n", "    priority: int  # 1-5\n", "    status: str  # active, pending, completed\n", "    start_date: str\n", "    end_date: Optional[str]\n", "    key_stakeholders: List[str]\n", "    primary_contact: str\n", "    tags: List[str]\n", "\n", "@dataclass\n", "class RACIEntry:\n", "    \"\"\"RACI matrix entry.\"\"\"\n", "    project_name: str\n", "    person_email: str\n", "    person_name: str\n", "    role: str  # R, A, <PERSON>, <PERSON>\n", "    notes: Optional[str] = None\n", "\n", "@dataclass\n", "class DocumentContext:\n", "    \"\"\"Document context for projects.\"\"\"\n", "    project_name: str\n", "    filepath: str\n", "    doc_type: str  # spec, design, notes, email, meeting\n", "    summary: str\n", "    key_points: List[str]\n", "    last_updated: str\n", "    relevance_score: float  # 0-1\n", "\n", "class ProjectContextStore:\n", "    \"\"\"Manage project contexts in Excel.\"\"\"\n", "    \n", "    def __init__(self, filepath: str = \"project_contexts.xlsx\"):\n", "        self.filepath = Path(filepath)\n", "        self.projects: Dict[str, ProjectInfo] = {}\n", "        self.raci_matrix: List[RACIEntry] = []\n", "        self.documents: List[DocumentContext] = []\n", "        self.people: Dict[str, Any] = {}  # PersonContext objects\n", "        # In production, would load from Excel\n", "    \n", "    def add_project(self, project: ProjectInfo) -> None:\n", "        \"\"\"Add or update project.\"\"\"\n", "        self.projects[project.name] = project\n", "        print(f\"✅ Added project: {project.name}\")\n", "    \n", "    def add_raci_entry(self, entry: RACIEntry) -> None:\n", "        \"\"\"Add RACI matrix entry.\"\"\"\n", "        # Remove existing entry if exists\n", "        self.raci_matrix = [e for e in self.raci_matrix \n", "                           if not (e.project_name == entry.project_name and \n", "                                  e.person_email == entry.person_email)]\n", "        self.raci_matrix.append(entry)\n", "        print(f\"👥 Added RACI: {entry.person_name} as {entry.role} for {entry.project_name}\")\n", "    \n", "    def get_project_context(self, project_name: str) -> Dict[str, Any]:\n", "        \"\"\"Get all context for a project.\"\"\"\n", "        project = self.projects.get(project_name)\n", "        if not project:\n", "            return None\n", "        \n", "        # Get RACI entries\n", "        raci_entries = [e for e in self.raci_matrix \n", "                       if e.project_name == project_name]\n", "        \n", "        # Get documents\n", "        documents = [d for d in self.documents \n", "                    if d.project_name == project_name]\n", "        \n", "        return {\n", "            'project': project,\n", "            'raci': raci_entries,\n", "            'documents': documents,\n", "            'people': {}  # Would include PersonContext in production\n", "        }\n", "    \n", "    def search_documents(self, query: str, project_name: Optional[str] = None) -> List[DocumentContext]:\n", "        \"\"\"Search document contexts.\"\"\"\n", "        docs = self.documents\n", "        if project_name:\n", "            docs = [d for d in docs if d.project_name == project_name]\n", "        \n", "        query_lower = query.lower()\n", "        matches = []\n", "        \n", "        for doc in docs:\n", "            if (query_lower in doc.summary.lower() or\n", "                any(query_lower in point.lower() for point in doc.key_points)):\n", "                matches.append(doc)\n", "        \n", "        # Sort by relevance\n", "        matches.sort(key=lambda d: d.relevance_score, reverse=True)\n", "        return matches\n", "\n", "# Demonstrate project context storage\n", "store = ProjectContextStore()\n", "\n", "# Add a project\n", "project = ProjectInfo(\n", "    name=\"LLM Safety System\",\n", "    description=\"Safety guardrails and monitoring for LLM applications\",\n", "    priority=5,\n", "    status=\"active\",\n", "    start_date=\"2024-01-01\",\n", "    end_date=None,\n", "    key_stakeholders=[\"<EMAIL>\", \"<EMAIL>\"],\n", "    primary_contact=\"<EMAIL>\",\n", "    tags=[\"safety\", \"llm\", \"critical\", \"compliance\"]\n", ")\n", "store.add_project(project)\n", "\n", "# Add RACI entries\n", "store.add_raci_entry(RACIEntry(\n", "    project_name=\"LLM Safety System\",\n", "    person_email=\"<EMAIL>\",\n", "    person_name=\"<PERSON>\",\n", "    role=\"A\",\n", "    notes=\"Technical lead and accountable for delivery\"\n", "))\n", "\n", "store.add_raci_entry(RACIEntry(\n", "    project_name=\"LLM Safety System\",\n", "    person_email=\"<EMAIL>\",\n", "    person_name=\"Safety Lead\",\n", "    role=\"R\",\n", "    notes=\"Responsible for safety requirements and testing\"\n", "))\n", "\n", "# Add document context\n", "store.documents.append(DocumentContext(\n", "    project_name=\"LLM Safety System\",\n", "    filepath=\"docs/safety/design.md\",\n", "    doc_type=\"design\",\n", "    summary=\"System design for LLM safety features including content filtering and monitoring\",\n", "    key_points=[\"Content filtering\", \"Real-time monitoring\", \"Incident response\"],\n", "    last_updated=\"2024-01-15\",\n", "    relevance_score=0.9\n", "))\n", "\n", "# Query the project\n", "print(\"\\n📁 Project Context Retrieval:\")\n", "context = store.get_project_context(\"LLM Safety System\")\n", "if context:\n", "    print(f\"\\nProject: {context['project'].name}\")\n", "    print(f\"Priority: {'⭐' * context['project'].priority}\")\n", "    print(f\"Status: {context['project'].status}\")\n", "    print(f\"Tags: {', '.join(context['project'].tags)}\")\n", "    \n", "    print(f\"\\nRACI Matrix ({len(context['raci'])} entries):\")\n", "    for entry in context['raci']:\n", "        print(f\"  • {entry.person_name} ({entry.role}): {entry.notes}\")\n", "    \n", "    print(f\"\\nDocuments ({len(context['documents'])} found):\")\n", "    for doc in context['documents']:\n", "        print(f\"  • {doc.doc_type}: {doc.summary[:60]}...\")\n", "        print(f\"    Key points: {', '.join(doc.key_points)}\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 8. UI Functions <a id=\"ui\"></a>\n", "\n", "### Interactive Gmail UI\n", "The UI module provides a rich console interface for email management:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# UI Flow Diagram\n", "mermaid_ui_flow = \"\"\"\n", "graph TD\n", "    A[Start UI] --> B[Load Configuration]\n", "    B --> C[Display Thread Table]\n", "    \n", "    C --> D{User Input}\n", "    \n", "    D -->|1-10| E[Open Thread]\n", "    D -->|11,12...| F[Open Thread 1,2...]\n", "    D -->|3| G[Group Emails with AI]\n", "    D -->|4x| H[Snooze Thread x]\n", "    D -->|5x| I[Create Task from Thread x]\n", "    D -->|n/p| J[Navigate Pages]\n", "    D -->|c| K[<PERSON><PERSON><PERSON>]\n", "    D -->|q| L[Quit]\n", "    \n", "    E --> M[Display Thread Messages]\n", "    M --> N[Action Menu]\n", "    N -->|r| O[<PERSON>]\n", "    N -->|u| P[Mark as Unread]\n", "    N -->|d| Q[Delete]\n", "    N -->|reply| R[Reply to Email]\n", "    \n", "    G --> S[AI Groups Emails]\n", "    S --> T[Display Grouped View]\n", "    \n", "    H --> U[Snooze Options]\n", "    U -->|1h| V[Snooze 1 hour]\n", "    U -->|4h| W[Snooze 4 hours]\n", "    U -->|tomorrow| X[Snooze until tomorrow]\n", "    U -->|week| Y[Snooze 1 week]\n", "    \n", "    I --> Z[Generate Task Title]\n", "    Z -->|d| AA[Use AI Title]\n", "    Z -->|custom| AB[Custom Title]\n", "    \n", "    style A fill:#27ae60\n", "    style L fill:#e74c3c\n", "    style G fill:#3498db\n", "    style I fill:#f39c12\n", "\"\"\"\n", "\n", "print(\"Gmail UI Flow (Mermaid):\")\n", "print(mermaid_ui_flow)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# UI Functions Demonstration\n", "from datetime import timedelta\n", "from typing import Tuple\n", "\n", "class GmailUI:\n", "    \"\"\"Interactive Gmail UI.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.page = 0\n", "        self.page_size = 10\n", "        self.grouped_view = False\n", "        self.column_config = {\n", "            'show_labels': True,\n", "            'show_date': True,\n", "            'show_preview': True\n", "        }\n", "    \n", "    def display_thread_table(self, threads: List[Dict]) -> None:\n", "        \"\"\"Display threads in a formatted table.\"\"\"\n", "        print(\"\\n📧 Gmail Inbox\")\n", "        print(\"=\" * 80)\n", "        \n", "        for i, thread in enumerate(threads):\n", "            # Thread number\n", "            num = f\"[{i+1}]\"\n", "            \n", "            # Subject and sender\n", "            subject = thread['subject'][:40] + \"...\" if len(thread['subject']) > 40 else thread['subject']\n", "            sender = thread['from'].split('<')[0].strip()[:20]\n", "            \n", "            # Optional columns\n", "            labels = \"\"\n", "            if self.column_config['show_labels']:\n", "                labels = f\" [{','.join(thread.get('labels', [])[:2])}]\"\n", "            \n", "            date = \"\"\n", "            if self.column_config['show_date']:\n", "                date = f\" {thread['date'].split(',')[0]}\"\n", "            \n", "            preview = \"\"\n", "            if self.column_config['show_preview']:\n", "                preview = f\"\\n     {thread.get('snippet', '')[:60]}...\"\n", "            \n", "            # Print row\n", "            print(f\"{num:>4} {subject:<40} - {sender:<20}{labels}{date}{preview}\")\n", "        \n", "        print(\"=\" * 80)\n", "        print(f\"Page {self.page + 1} | Commands: 1-10 (open), 3 (group), 4x (snooze), 5x (task), n/p (page), c (cols), q (quit)\")\n", "    \n", "    def handle_snooze(self, thread_idx: int) -> datetime:\n", "        \"\"\"Handle snooze interaction.\"\"\"\n", "        print(f\"\\n😴 Snooze thread {thread_idx + 1}:\")\n", "        print(\"  1. 1 hour\")\n", "        print(\"  2. 4 hours\") \n", "        print(\"  3. Until tomorrow 9am\")\n", "        print(\"  4. 1 week\")\n", "        \n", "        # Mock user selection\n", "        choice = 3  # Simulate choosing \"tomorrow\"\n", "        \n", "        now = datetime.now()\n", "        if choice == 1:\n", "            return now + <PERSON><PERSON><PERSON>(hours=1)\n", "        elif choice == 2:\n", "            return now + <PERSON><PERSON><PERSON>(hours=4)\n", "        elif choice == 3:\n", "            tomorrow = now + <PERSON><PERSON><PERSON>(days=1)\n", "            return tomorrow.replace(hour=9, minute=0, second=0)\n", "        else:\n", "            return now + <PERSON><PERSON><PERSON>(days=7)\n", "    \n", "    def handle_task_creation(self, thread: Dict) -> Tuple[str, str]:\n", "        \"\"\"Handle task creation interaction.\"\"\"\n", "        print(f\"\\n📋 Create task from: {thread['subject']}\")\n", "        print(\"\\nAI suggested title: 'Review and approve Q4 budget proposal'\")\n", "        print(\"Press 'd' to use this title, or enter custom title:\")\n", "        \n", "        # Mock user choosing AI title\n", "        choice = 'd'\n", "        \n", "        if choice == 'd':\n", "            title = \"Review and approve Q4 budget proposal\"\n", "        else:\n", "            title = choice  # Custom title\n", "        \n", "        description = f\"Task created from email: {thread['subject']}\"\n", "        return title, description\n", "\n", "# Demonstrate UI functions\n", "ui = GmailUI()\n", "\n", "# Mock email data\n", "mock_threads = [\n", "    {\n", "        'id': 'msg_001',\n", "        'thread_id': 'thread_001',\n", "        'subject': 'Q4 Budget Review - Action Required',\n", "        'from': '<PERSON> <<EMAIL>>',\n", "        'date': 'Mon, 25 Mar 2024',\n", "        'labels': ['INBOX', 'IMPORTANT'],\n", "        'snippet': 'Hi team, please review the attached Q4 budget proposal and provide feedback...'\n", "    },\n", "    {\n", "        'id': 'msg_002',\n", "        'thread_id': 'thread_002',\n", "        'subject': 'Project Alpha: Milestone Update',\n", "        'from': '<PERSON> <<EMAIL>>',\n", "        'date': 'Mon, 25 Mar 2024',\n", "        'labels': ['INBOX'],\n", "        'snippet': 'Quick update on Project Alpha progress. We\\'ve completed phase 1 and are...'\n", "    },\n", "    {\n", "        'id': 'msg_003',\n", "        'thread_id': 'thread_003',\n", "        'subject': 'Team Meeting Tomorrow @ 2pm',\n", "        'from': 'Team Lead <<EMAIL>>',\n", "        'date': 'Sun, 24 Mar 2024',\n", "        'labels': ['INBOX', 'MEETINGS'],\n", "        'snippet': 'Reminder: We have our weekly team sync tomorrow at 2pm. Agenda includes...'\n", "    }\n", "]\n", "\n", "# Display the UI\n", "ui.display_thread_table(mock_threads)\n", "\n", "# Demonstrate snooze\n", "print(\"\\n\\n🎯 Demo: Snoozing email\")\n", "snooze_until = ui.handle_snooze(0)\n", "print(f\"✅ Thread will be snoozed until: {snooze_until.strftime('%Y-%m-%d %H:%M')}\")\n", "\n", "# Demonstrate task creation\n", "print(\"\\n\\n🎯 Demo: Creating task from email\")\n", "title, desc = ui.handle_task_creation(mock_threads[0])\n", "print(f\"✅ Task created: '{title}'\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Summary\n", "\n", "This notebook has demonstrated all the key functions in the Gmail tools system:\n", "\n", "### Core Features\n", "1. **Authentication** - OAuth2 token management\n", "2. **Email Operations** - List, read, send, mark as read\n", "3. **AI Features** - Smart grouping, draft generation, project extraction\n", "4. **Management** - Task creation, email snoozing\n", "5. **Storage** - Excel-based project context storage\n", "6. **UI** - Rich interactive console interface\n", "\n", "### Key Integration Points\n", "- **Gmail API** for email operations\n", "- **OpenAI GPT-4** for email grouping\n", "- **Anthrop<PERSON>** for draft replies and task title generation\n", "- **Excel storage** for persistent project context\n", "\n", "### Usage Examples\n", "```bash\n", "# Interactive UI\n", "just ui\n", "\n", "# Generate draft replies\n", "just drafts\n", "\n", "# Find projects in emails\n", "just find-projects\n", "\n", "# Run tests\n", "just test\n", "```\n", "\n", "This system provides a comprehensive email management solution with AI-powered features for productivity enhancement.\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}