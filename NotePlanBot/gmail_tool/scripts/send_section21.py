#!/usr/bin/env python3
"""Send Section 21 notice via PC2Paper service."""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.gmail.pc2paper import send_section21_notice


def main():
    """Send the Section 21 notice to <PERSON><PERSON> Warburton."""
    # Read the cover letter content
    cover_letter_path = "/Users/<USER>/Documents/Code3/Archive/gcal/.tmp/section21_email.txt"
    with open(cover_letter_path, 'r') as f:
        lines = f.readlines()
        # Skip the email headers and get the body
        body_start = 0
        for i, line in enumerate(lines):
            if line.strip() == "":
                body_start = i + 1
                break
        cover_letter_body = ''.join(lines[body_start:])
    
    # Send the notice
    message_id = send_section21_notice(
        recipient_name="Ms Nadine Warburton",
        recipient_address="61 Somerset Close, Catterick Garrison, DL9 3HF",
        cover_letter_body=cover_letter_body,
        attachment_path="/Users/<USER>/Documents/Code3/Archive/gcal/.tmp/keri/Form 6A Nadine Warburton IS SIGNED - BOTH.pdf",
        cc_email="<EMAIL>"
    )
    
    if message_id:
        print(f"✅ Section 21 notice sent successfully! Message ID: {message_id}")
        print("📧 Email sent to: <EMAIL>")
        print("📄 CC: <EMAIL>")
        print("📎 Attachment: Form 6A Nadine Warburton IS SIGNED - BOTH.pdf")
    else:
        print("❌ Failed to send Section 21 notice")
        sys.exit(1)


if __name__ == "__main__":
    main()