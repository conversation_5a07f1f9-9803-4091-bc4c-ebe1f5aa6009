"""Demo all Gmail tools features.

1. Show UI capabilities
2. Show project context
3. Show draft generation
v1
"""
import asyncio
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from src.tools.gmail.storage.project_store import ProjectContextStore, ProjectInfo, RACIEntry, DocumentContext
from src.tools.gmail.ui import CFG


console = Console()


def demo_ui_configuration():
    """Demo UI configuration options."""
    console.print("\n[bold cyan]Gmail UI Configuration Demo[/bold cyan]\n")
    
    cfg = CFG()
    
    # Show current settings
    table = Table(title="Current UI Settings")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Threads per page", str(cfg.threads_per_page))
    table.add_row("Content preview length", str(cfg.max_content_preview))
    table.add_row("Days to look back", str(cfg.days_back))
    table.add_row("Show participants", str(cfg.show_participants))
    table.add_row("Show duration", str(cfg.show_duration))
    table.add_row("Show last message", str(cfg.show_last_message))
    
    console.print(table)
    
    console.print("\n[yellow]Features:[/yellow]")
    console.print("• Numbered rows for easy selection (1-10)")
    console.print("• Extended numbering: 11 = thread 1, 12 = thread 2, etc.")
    console.print("• Page navigation with 'n' and 'p'")
    console.print("• Column visibility settings with 'c'")
    console.print("• Real-time refresh with 'r'")


def demo_project_store():
    """Demo project context store."""
    console.print("\n[bold cyan]Project Context Store Demo[/bold cyan]\n")
    
    # Create demo store
    store = ProjectContextStore("demo_projects.xlsx")
    
    # Add demo projects
    projects = [
        ProjectInfo(
            name="Apollo Integration",
            description="Integration with 5-20 Apollo platforms for agentic access",
            priority=5,
            status="active",
            start_date="2025-06-01",
            end_date=None,
            key_stakeholders=["<EMAIL>", "<EMAIL>"],
            primary_contact="<EMAIL>",
            tags=["integration", "apollo", "automation"]
        ),
        ProjectInfo(
            name="Shop My Data",
            description="Data project with custom client integration requirements",
            priority=4,
            status="completed",
            start_date="2025-06-19",
            end_date="2025-06-25",
            key_stakeholders=["<EMAIL>", "<EMAIL>"],
            primary_contact="<EMAIL>",
            tags=["data", "client", "custom"]
        )
    ]
    
    for project in projects:
        store.add_project(project)
        console.print(f"[green]✓[/green] Added project: {project.name}")
    
    # Add RACI entries
    raci_entries = [
        ("Apollo Integration", "<EMAIL>", "Alex Foster", "C", "Technical consultation"),
        ("Apollo Integration", "<EMAIL>", "Daniel Mason", "A", "Anon.com CEO"),
        ("Shop My Data", "<EMAIL>", "Jordan Cealey", "R", "Implementation lead"),
    ]
    
    for project, email, name, role, notes in raci_entries:
        store.add_raci_entry(RACIEntry(project, email, name, role, notes))
    
    console.print(f"\n[green]✓[/green] Added {len(raci_entries)} RACI entries")
    
    # Add documents
    docs = [
        DocumentContext(
            project_name="Apollo Integration",
            filepath="docs/apollo/tech-spec.md",
            doc_type="spec",
            summary="Technical specification for browser automation and API integration across Apollo platforms",
            key_points=["Authentication handling", "Rate limiting", "Error recovery"],
            last_updated="2025-06-23",
            relevance_score=0.9
        )
    ]
    
    for doc in docs:
        store.add_document(doc)
    
    console.print(f"[green]✓[/green] Added {len(docs)} documents")
    
    # Show project context retrieval
    console.print("\n[yellow]Project Context Retrieval Example:[/yellow]")
    ctx = store.get_project_context("Apollo Integration")
    
    if ctx:
        panel = Panel(
            f"Project: {ctx['project'].name}\n"
            f"Status: {ctx['project'].status}\n"
            f"Priority: {ctx['project'].priority}/5\n"
            f"RACI Team: {len(ctx['raci'])} members\n"
            f"Documents: {len(ctx['documents'])}\n"
            f"Key Stakeholders: {', '.join(ctx['project'].key_stakeholders[:2])}",
            title="Apollo Integration Context"
        )
        console.print(panel)


def demo_features_summary():
    """Show summary of all features."""
    console.print("\n[bold cyan]Gmail Tools Feature Summary[/bold cyan]\n")
    
    features = {
        "Interactive UI": [
            "Rich table display with pagination",
            "Flexible column configuration",
            "Thread navigation with numbered shortcuts",
            "Real-time refresh and updates"
        ],
        "Project Context Store": [
            "Excel-based persistent storage",
            "RACI matrix management",
            "Document context tracking",
            "People and stakeholder management"
        ],
        "Email Reply Drafting": [
            "Claude-powered contextual replies",
            "Project context integration",
            "Professional tone maintenance",
            "Key point extraction"
        ],
        "Project Finding": [
            "Automatic project mention detection",
            "Context extraction from emails",
            "Draft project updates",
            "Confidence scoring"
        ],
        "Hierarchical Context Retrieval": [
            "Two-tier retrieval system",
            "Parallel processing for speed",
            "Intelligent LLM-based ranking",
            "Context summarization"
        ]
    }
    
    for feature, capabilities in features.items():
        console.print(f"\n[bold yellow]{feature}:[/bold yellow]")
        for cap in capabilities:
            console.print(f"  • {cap}")


def main():
    """Run all demos."""
    console.print("[bold magenta]Gmail Tools Feature Demo[/bold magenta]")
    console.print("=" * 60)
    
    demo_ui_configuration()
    demo_project_store()
    demo_features_summary()
    
    console.print("\n[green]All features demonstrated successfully![/green]")
    console.print("\n[dim]To use these tools:[/dim]")
    console.print("• UI: python -m src.tools.gmail.ui")
    console.print("• Draft replies: python -m src.tools.gmail.draft_replies")
    console.print("• Find projects: python -m src.tools.gmail.find_projects")
    console.print("• Context retrieval: python -m src.tools.gmail.context_retrieval")


if __name__ == '__main__':
    main()