"""Test message parsing patterns.

1. Test calendar parsing
2. Test doc comments
3. Test formatting
v7
"""
import re
from rich.console import Console
from rich.text import Text
from dataclasses import dataclass
from typing import Dict, List, Optional
from bs4 import BeautifulSoup
import html


console = Console()


@dataclass
class TestResult:
    """Test result with details."""
    success: bool
    expected: str
    got: str
    details: Dict = None

    def __post_init__(self):
        self.details = self.details or {}


def clean_html_content(html_content: str) -> str:
    """Clean HTML content.
    
    1. Parse HTML
    2. Extract text
    3. Clean whitespace
    v2
    """
    # Unescape HTML entities first
    html_content = html.unescape(html_content)
    
    # Parse HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()
    
    # Replace <br> and </p> with newlines
    for br in soup.find_all(['br', 'p']):
        br.replace_with('\n' + br.text)
    
    # Get text
    text = soup.get_text()
    
    # Clean up whitespace
    lines = []
    for line in text.splitlines():
        line = line.strip()
        if line:
            lines.append(line)
    
    # Join lines with appropriate spacing
    text = ' '.join(lines)
    
    # Fix multiple spaces
    text = re.sub(r'\s+', ' ', text)
    
    return text.strip()


def extract_doc_links(content: str) -> Dict[str, str]:
    """Extract Google Doc/Sheet links.
    
    1. Find doc/sheet links
    2. Extract IDs
    3. Return mapping
    v1
    """
    links = {}
    
    # Doc link patterns
    patterns = {
        'document': r'https://docs\.google\.com/document/d/([a-zA-Z0-9_-]+)(?:/|$)',
        'spreadsheet': r'https://docs\.google\.com/spreadsheets/d/([a-zA-Z0-9_-]+)(?:/|$)',
        'presentation': r'https://docs\.google\.com/presentation/d/([a-zA-Z0-9_-]+)(?:/|$)',
        'drawing': r'https://docs\.google\.com/drawings/d/([a-zA-Z0-9_-]+)(?:/|$)',
    }
    
    for doc_type, pattern in patterns.items():
        matches = re.finditer(pattern, content)
        for match in matches:
            doc_id = match.group(1)
            full_url = match.group(0).rstrip('/')
            links[doc_id] = {
                'type': doc_type,
                'url': full_url
            }
    
    return links


def test_calendar_parsing():
    """Test calendar event parsing patterns.
    
    1. Test various formats
    2. Extract details
    3. Compare results
    v6
    """
    test_cases = [
        # Basic calendar invite
        {
            'content': "Accepted: Team Meeting @ Weekly from 2pm to 3pm\nWhen: Every Wednesday\nWhere: Google Meet",
            'subject': "Accepted: Team Meeting @ Weekly from 2pm to 3pm",
            'expected': {
                'action': 'Accepted',
                'title': 'Team Meeting',
                'time': '2pm to 3pm',
                'when': 'Every Wednesday',
                'where': 'Google Meet'
            }
        },
        # Empty content but detailed subject
        {
            'content': "",
            'subject': "Accepted: Quick Sync @ 3pm to 3:30pm on Thursday (GMT)",
            'expected': {
                'action': 'Accepted',
                'title': 'Quick Sync',
                'time': '3pm to 3:30pm',
                'day': 'Thursday',
                'timezone': 'GMT'
            }
        },
        # Real example from Sandy
        {
            'content': "",
            'subject': "Accepted: Sandy <> Alex @ Weekly from 2pm to 2:30pm on Wednesday (GMT) (Alex Foster)",
            'expected': {
                'action': 'Accepted',
                'title': 'Sandy <> Alex',
                'time': '2pm to 2:30pm',
                'day': 'Wednesday',
                'timezone': 'GMT'
            }
        },
        # Single time with timezone
        {
            'content': "",
            'subject': "Accepted: Quick Chat @ at 3pm on Friday (PST)",
            'expected': {
                'action': 'Accepted',
                'title': 'Quick Chat',
                'time': '3pm',
                'day': 'Friday',
                'timezone': 'PST'
            }
        },
        # Complex title with @
        {
            'content': "",
            'subject': "Accepted: Discussion @ Product Team @ Weekly from 1pm to 2pm (GMT)",
            'expected': {
                'action': 'Accepted',
                'title': 'Discussion @ Product Team',
                'time': '1pm to 2pm',
                'timezone': 'GMT'
            }
        }
    ]
    
    # Calendar parsing patterns
    patterns = {
        # Main subject pattern with named groups
        'subject_full': re.compile(r"""
            ^(?P<action>Accepted|Declined|Tentative|Updated):\s*
            (?P<raw_title>.+?)$
        """, re.X | re.I),
        # Time pattern
        'time': re.compile(r"""
            (?:
                (?:from|at|@)\s+(?P<start_time>\d+(?::\d+)?(?:am|pm))
                (?:\s+to\s+(?P<end_time>\d+(?::\d+)?(?:am|pm)))?
            )
        """, re.X | re.I),
        # Recurrence pattern
        'recurrence': re.compile(r"""
            @\s+(?P<recurrence>Weekly|Monthly|Daily)(?:\s|$)
        """, re.X | re.I),
        # Day pattern
        'day': re.compile(r"""
            \s+on\s+(?P<day>[^(@\s][^(@]*?)(?:\s|$)
        """, re.X | re.I),
        # Timezone pattern
        'timezone': re.compile(r"""
            \((?P<timezone>[^)]+)\)(?:\s*\([^)]+\))?$
        """, re.X | re.I),
        # Content patterns
        'content_when': re.compile(r"When:\s*(?P<when>.+?)(?:\r|\n|$)", re.I),
        'content_where': re.compile(r"Where:\s*(?P<where>.+?)(?:\r|\n|$)", re.I),
        'content_status': re.compile(r"(?:Meeting|Event) Status:\s*(?P<status>.+?)(?:\r|\n|$)", re.I),
        'content_organizer': re.compile(r"Organizer:\s*(?P<organizer>.+?)(?:\r|\n|$)", re.I),
    }
    
    console.print("\n[cyan]Running Calendar Parsing Tests[/cyan]")
    
    for i, test_case in enumerate(test_cases, 1):
        content = test_case['content']
        subject = test_case['subject']
        expected = test_case['expected']
        
        # Try to extract data
        data = {}
        
        # Try subject first
        subject_match = patterns['subject_full'].search(subject)
        if subject_match:
            data.update({k: v.strip() if v else v for k, v in subject_match.groupdict().items() if v})
            
            # Extract components from raw title
            raw_title = data.pop('raw_title')
            
            # Extract timezone first (from the end)
            timezone_match = patterns['timezone'].search(raw_title)
            if timezone_match:
                data.update({k: v for k, v in timezone_match.groupdict().items() if v})
                # Remove timezone part from raw title
                raw_title = patterns['timezone'].sub('', raw_title)
            
            # Extract time
            time_match = patterns['time'].search(raw_title)
            if time_match:
                time_data = {k: v for k, v in time_match.groupdict().items() if v}
                if 'single_time' in time_data:
                    data['time'] = time_data['single_time']
                elif 'start_time' in time_data:
                    time_str = time_data['start_time']
                    if 'end_time' in time_data:
                        time_str += f" to {time_data['end_time']}"
                    data['time'] = time_str
                # Remove time part from raw title
                raw_title = patterns['time'].sub('', raw_title)
            
            # Extract recurrence
            recurrence_match = patterns['recurrence'].search(raw_title)
            if recurrence_match:
                data.update({k: v for k, v in recurrence_match.groupdict().items() if v})
                # Remove recurrence part from raw title
                raw_title = patterns['recurrence'].sub('', raw_title)
            
            # Extract day
            day_match = patterns['day'].search(raw_title)
            if day_match:
                data.update({k: v.strip() for k, v in day_match.groupdict().items() if v})
                # Remove day part from raw title
                raw_title = patterns['day'].sub('', raw_title)
            
            # Clean up title
            title = raw_title.strip()
            # Remove trailing @ if it exists
            title = re.sub(r'\s*@\s*$', '', title)
            # Remove extra whitespace
            title = re.sub(r'\s+', ' ', title)
            data['title'] = title
        
        # Try content if available
        if content:
            for name, pattern in patterns.items():
                if name.startswith('content_'):
                    match = pattern.search(content)
                    if match:
                        data.update({k: v.strip() if v else v for k, v in match.groupdict().items() if v})
        
        # Compare results
        success = all(
            data.get(k) == v
            for k, v in expected.items()
        )
        
        console.print(f"\n[yellow]Test {i}:[/yellow] {'✓' if success else '✗'}")
        if not success:
            console.print("[red]Expected:[/red]")
            console.print(expected)
            console.print("[red]Got:[/red]")
            console.print(data)
            
            # Show what's missing or different
            for k, v in expected.items():
                if k not in data:
                    console.print(f"Missing key: {k}")
                elif data[k] != v:
                    console.print(f"Mismatch for {k}:")
                    console.print(f"  Expected: {v}")
                    console.print(f"  Got: {data[k]}")
        else:
            console.print("Extracted:", data)
            
        # Show formatted output
        console.print("\nFormatted output:")
        console.print(format_calendar_event(data))


def test_doc_comment_parsing():
    """Test Google Doc comment parsing.
    
    1. Test various formats
    2. Extract details
    3. Compare results
    v10
    """
    test_cases = [
        # Basic doc comment
        {
            'content': """New activity in document
Project Notes
(2 comments
User1: First comment
User2: Reply to that""",
            'expected': {
                'doc_type': 'document',
                'title': 'Project Notes',
                'comment_count': 2,
                'comments': [
                    {'user': 'User1', 'text': 'First comment'},
                    {'user': 'User2', 'text': 'Reply to that'}
                ]
            }
        },
        # Action item with HTML
        {
            'content': """Jian Ouyang replied to an action item in the following document
Safety Project Scoping Questions
(1 comment
Alex Foster | We would need to jail-break our internal llama. See <a href="https://docs.google.com/document/d/1234567890abcdef/edit">design doc</a>.""",
            'expected': {
                'doc_type': 'document',
                'title': 'Safety Project Scoping Questions',
                'comment_count': 1,
                'comments': [
                    {'user': 'Alex Foster', 'text': 'We would need to jail-break our internal llama. See design doc.'}
                ],
                'is_action_item': True,
                'links': {
                    '1234567890abcdef': {
                        'type': 'document',
                        'url': 'https://docs.google.com/document/d/1234567890abcdef'
                    }
                }
            }
        },
        # Sheet comment with link
        {
            'content': """New activity in spreadsheet
xAI Biology/Chemistry Autoseeding v0.3
(8 comments
Kyle Haustein | Consider a tetrahedral molecule... See data at <a href="https://docs.google.com/spreadsheets/d/abcdef1234567890/edit#gid=0">results</a>""",
            'expected': {
                'doc_type': 'spreadsheet',
                'title': 'xAI Biology/Chemistry Autoseeding v0.3',
                'comment_count': 8,
                'comments': [
                    {'user': 'Kyle Haustein', 'text': 'Consider a tetrahedral molecule... See data at results'}
                ],
                'links': {
                    'abcdef1234567890': {
                        'type': 'spreadsheet',
                        'url': 'https://docs.google.com/spreadsheets/d/abcdef1234567890'
                    }
                }
            }
        },
        # Multiple comments with URLs and HTML formatting
        {
            'content': """New activity in document
Design Doc
(3 comments
User1: Check out <a href="https://example.com">this link</a>
User2 | See the diagram at <a href="https://docs.google.com/drawings/d/xyz123456789/edit">architecture</a>
User3: Looks <b>good</b> to me!""",
            'expected': {
                'doc_type': 'document',
                'title': 'Design Doc',
                'comment_count': 3,
                'comments': [
                    {'user': 'User1', 'text': 'Check out this link'},
                    {'user': 'User2', 'text': 'See the diagram at architecture'},
                    {'user': 'User3', 'text': 'Looks good to me!'}
                ],
                'links': {
                    'xyz123456789': {
                        'type': 'drawing',
                        'url': 'https://docs.google.com/drawings/d/xyz123456789'
                    }
                }
            }
        },
        # Comment with complex formatting and presentation link
        {
            'content': """New activity in document
Technical Spec
(1 comment
Alex Foster | Here's what we need:
1. Feature A
2. Feature B (see <a href="https://docs.google.com/presentation/d/pqrs987654321/edit#slide=1">slides</a>)
- Also this
- And that""",
            'expected': {
                'doc_type': 'document',
                'title': 'Technical Spec',
                'comment_count': 1,
                'comments': [
                    {'user': 'Alex Foster', 'text': "Here's what we need:\n1. Feature A\n2. Feature B (see slides)\n- Also this\n- And that"}
                ],
                'links': {
                    'pqrs987654321': {
                        'type': 'presentation',
                        'url': 'https://docs.google.com/presentation/d/pqrs987654321'
                    }
                }
            }
        }
    ]
    
    # Doc comment patterns
    patterns = {
        # Header pattern with named groups
        'header': re.compile(r"""
            (?P<action>New\ activity|replied\ to\ (?:a\ comment|an\ action\ item))
            \ in\ (?:the\ following\ )?
            (?P<doc_type>document|spreadsheet)
        """, re.X | re.I),
        # Title pattern - matches the line after the header
        'title': re.compile(r"""
            ^(?!.*(?:New\ activity|replied|comments?|https?:|[|:]))
            (?P<title>[^\n(]+?)
            (?:\s*\(|$)
        """, re.X | re.M),
        # Comment count - match at start of line
        'comment_count': re.compile(r"^[\s(]*(?P<count>\d+)\s*comments?[\s)]*$", re.I | re.M),
        # Individual comments - handle multiline with better list handling
        'comment': re.compile(r"""
            (?:^|\n)                   # Start of line or newline
            (?P<user>[^|:\n]+?)        # Username (non-greedy)
            [|:]                       # Separator
            \s*                        # Optional whitespace
            (?P<text>                  # Comment text
                (?:                    # Group for text content
                    (?!               # Negative lookahead for comment boundaries
                        \n(?=         # Only match newline if followed by:
                            [^-\d\s][^|:\n]*[|:]  # New comment (but not list items)
                            |\([0-9]+\s*comments?\)  # Comment count
                            |\Z       # End of string
                        )
                    )
                    .|\n              # Any char or newline if not matching above
                )+?                   # Non-greedy multiple
            )
            (?=                       # Positive lookahead
                \n(?:                # Next line must be:
                    [^-\d\s][^|:\n]*[|:]  # New comment (but not list items)
                    |\([0-9]+\s*comments?\)  # Comment count
                )
                |\Z                   # Or end of string
            )
        """, re.X | re.M | re.S),
    }
    
    console.print("\n[cyan]Running Doc Comment Parsing Tests[/cyan]")
    
    for i, test_case in enumerate(test_cases, 1):
        content = test_case['content']
        expected = test_case['expected']
        
        # Try to extract data
        data = {}
        
        # Extract doc links first
        links = extract_doc_links(content)
        if links:
            data['links'] = links
        
        # Get header info
        header_match = patterns['header'].search(content)
        if header_match:
            header_data = {k: v.strip() if v else v for k, v in header_match.groupdict().items() if v}
            # Only keep doc_type from header
            if 'doc_type' in header_data:
                data['doc_type'] = header_data['doc_type']
        
        # Get title
        title_match = patterns['title'].search(content)
        if title_match:
            data['title'] = title_match.group('title').strip()
        
        # Get comment count
        count_match = patterns['comment_count'].search(content)
        if count_match:
            data['comment_count'] = int(count_match.group('count'))
        
        # Get comments
        data['comments'] = []
        for comment_match in patterns['comment'].finditer(content):
            # Clean HTML from text while preserving structure
            text = comment_match.group('text').strip()
            
            # First clean HTML while preserving links
            text = clean_html_content(text)
            
            # Then restore list structure
            text = re.sub(r'(\d+\.|-)(?=\s)', r'\n\1', text)  # Add newlines before list markers
            text = re.sub(r'\s*\n\s*', '\n', text)  # Clean up extra whitespace around newlines
            text = text.strip()  # Remove any leading/trailing whitespace
            
            comment_data = {
                'user': comment_match.group('user').strip(),
                'text': text
            }
            data['comments'].append(comment_data)
        
        # Check for action items
        data['is_action_item'] = 'action item' in content.lower()
        
        # Compare results
        success = all(
            data.get(k) == v
            for k, v in expected.items()
        )
        
        console.print(f"\n[yellow]Test {i}:[/yellow] {'✓' if success else '✗'}")
        if not success:
            console.print("[red]Expected:[/red]")
            console.print(expected)
            console.print("[red]Got:[/red]")
            console.print(data)
            
            # Show what's missing or different
            for k, v in expected.items():
                if k not in data:
                    console.print(f"Missing key: {k}")
                elif data[k] != v:
                    console.print(f"Mismatch for {k}:")
                    console.print(f"  Expected: {v}")
                    console.print(f"  Got: {data[k]}")
        else:
            console.print("Extracted:", data)
            
        # Show formatted output
        console.print("\nFormatted output:")
        console.print(format_doc_comment(data))


def format_calendar_event(data: Dict) -> str:
    """Format calendar event nicely.
    
    1. Format title/time
    2. Add details
    3. Return formatted
    v1
    """
    parts = []
    
    # Format title and time
    if data.get('title'):
        parts.append(f"Event: {data['title']}")
    
    time_str = ""
    if data.get('start_time'):
        time_str = f"Time: {data['start_time']}"
        if data.get('end_time'):
            time_str += f" to {data['end_time']}"
    elif data.get('time'):
        time_str = f"Time: {data['time']}"
    if time_str:
        parts.append(time_str)
    
    # Add day/recurrence
    if data.get('day'):
        parts.append(f"Day: {data['day'].strip()}")
    elif data.get('when'):
        parts.append(f"When: {data['when']}")
    
    if data.get('recurrence'):
        parts.append(f"Repeats: {data['recurrence']}")
    
    # Add location
    if data.get('where'):
        parts.append(f"Where: {data['where']}")
    
    # Add timezone if specified
    if data.get('timezone'):
        parts.append(f"Timezone: {data['timezone']}")
    
    return "\n".join(parts)


def format_doc_comment(data: Dict) -> str:
    """Format doc comment nicely.
    
    1. Format header
    2. Add comments
    3. Add links
    4. Return formatted
    v2
    """
    parts = []
    
    # Format header
    header = f"Activity in {data['doc_type']}: {data['title']}"
    if data.get('is_action_item'):
        header += " (Action Item)"
    parts.append(header)
    
    # Add comment count if available
    if data.get('comment_count'):
        parts.append(f"({data['comment_count']} comments)")
    
    # Add comments
    for comment in data.get('comments', []):
        parts.append(f"  • {comment['user']}: {comment['text']}")
    
    # Add links if available
    if data.get('links'):
        parts.append("\nLinked documents:")
        for doc_id, link_data in data['links'].items():
            parts.append(f"  • {link_data['type'].title()}: {link_data['url']}")
    
    return '\n'.join(parts)


if __name__ == '__main__':
    # Run parsing tests
    test_calendar_parsing()
    test_doc_comment_parsing()
    
    # Test formatting
    console.print("\n[cyan]Testing Calendar Formatting[/cyan]")
    calendar_data = {
        'action': 'Accepted',
        'title': 'Team Sync',
        'start_time': '2pm',
        'end_time': '3pm',
        'day': 'Wednesday',
        'recurrence': 'Weekly',
        'where': 'Google Meet',
        'timezone': 'GMT'
    }
    console.print("\nFormatted calendar event:")
    console.print(format_calendar_event(calendar_data))
    
    console.print("\n[cyan]Testing Doc Comment Formatting[/cyan]")
    doc_data = {
        'doc_type': 'document',
        'title': 'Project Notes',
        'comment_count': 2,
        'comments': [
            {'user': 'User1', 'text': 'First comment'},
            {'user': 'User2', 'text': 'Reply to that'}
        ],
        'is_action_item': True
    }
    console.print("\nFormatted doc comment:")
    console.print(format_doc_comment(doc_data)) 