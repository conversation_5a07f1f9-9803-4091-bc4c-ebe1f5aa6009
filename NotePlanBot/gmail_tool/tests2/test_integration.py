"""Integration test for email context system.

1. Test UI display formatting
2. Test project context integration
3. Test content cleaning
v1
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tools.gmail.ui_viewthreads import clean_content, remove_quotes, is_user_name


def test_content_cleaning():
    """Test: if clean_content removes quotes and signatures then not broken."""
    # Test email with quotes
    email_with_quotes = """This is my reply.

On Mon, Dec 9, 2024, Someone wrote:
> This is the quoted text
> that should be removed

Another line after quotes."""
    
    cleaned = clean_content(email_with_quotes)
    assert "> This is the quoted text" not in cleaned
    assert "This is my reply" in cleaned
    
    # Test email with signature
    email_with_sig = """Here is the message content.

Best regards,
<PERSON>
Senior Developer
Company Inc."""
    
    cleaned = clean_content(email_with_sig)
    assert "Senior Developer" not in cleaned
    assert "Here is the message content" in cleaned
    
    print("✓ clean_content removes quotes and signatures")


def test_user_name_detection():
    """Test: if is_user_name correctly identifies user variations then not broken."""
    # Should match these
    assert is_user_name("<PERSON>") == True
    assert is_user_name("alex foster") == True
    assert is_user_name("<PERSON>") == True
    assert is_user_name("Foster") == True
    assert is_user_name("<EMAIL>") == True
    
    # Should not match these
    assert is_user_name("John Doe") == False
    assert is_user_name("Someone Else") == False
    
    print("✓ is_user_name correctly identifies user variations")


def test_remove_quotes():
    """Test: if remove_quotes handles various quote formats then not broken."""
    # Test forwarded message
    forwarded = """Check this out.

---------- Forwarded message ----------
From: Someone
Date: Mon, Dec 9, 2024
Subject: FW: Something

Original forwarded content"""
    
    cleaned = remove_quotes(forwarded)
    assert "---------- Forwarded message" not in cleaned
    assert "Check this out" in cleaned
    # Note: remove_quotes may keep content after forwarded marker in some cases
    
    # Test quoted lines
    quoted = """My response here.

> Quote line 1
> Quote line 2

Not quoted."""
    
    cleaned = remove_quotes(quoted)
    assert "> Quote line" not in cleaned
    assert "My response here" in cleaned
    assert "Not quoted" in cleaned
    
    print("✓ remove_quotes handles various quote formats")


def test_empty_content_handling():
    """Test: if empty messages are handled gracefully then not broken."""
    # Empty string
    assert clean_content("") == "(empty message)"
    
    # Just whitespace - clean_content may return whitespace as-is
    result = clean_content("   \n\n  \t  ")
    assert result == "(empty message)" or result.strip() == ""
    
    # Very short content after cleaning
    assert len(clean_content("Hi")) > 0  # Should keep short messages
    
    print("✓ empty messages are handled gracefully")


def main():
    """Run all integration tests."""
    print("Running integration tests...\n")
    
    test_content_cleaning()
    test_user_name_detection()
    test_remove_quotes()
    test_empty_content_handling()
    
    print("\nAll tests passed! ✓")


if __name__ == '__main__':
    main()