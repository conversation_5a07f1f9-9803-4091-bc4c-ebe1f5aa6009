"""Regression tests for project context store.

1. Test Excel file creation
2. Test data persistence 
3. Test retrieval functions
v1
"""
import sys
import os
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tools.gmail.storage.project_store import (
    ProjectContextStore,
    ProjectInfo,
    RACIEntry,
    DocumentContext,
    PersonContext
)


def test_store_creation_and_save():
    """Test: if ProjectContextStore creates and saves Excel file then not broken."""
    test_file = "test_regression_store.xlsx"
    
    # Remove test file if exists
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # Create store
    store = ProjectContextStore(test_file)
    
    # Add test data
    project = ProjectInfo(
        name="Test Project",
        description="Test description",
        priority=3,
        status="active",
        start_date="2025-01-01",
        end_date=None,
        key_stakeholders=["<EMAIL>"],
        primary_contact="<EMAIL>",
        tags=["test", "regression"]
    )
    store.add_project(project)
    
    # Check file was created
    assert os.path.exists(test_file)
    
    # Cleanup
    os.remove(test_file)
    
    print("✓ ProjectContextStore creates and saves Excel file")


def test_data_persistence():
    """Test: if data persists across store instances then not broken."""
    test_file = "test_regression_persist.xlsx"
    
    # Remove test file if exists
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # Create first store and add data
    store1 = ProjectContextStore(test_file)
    
    project = ProjectInfo(
        name="Persistent Project",
        description="Should persist",
        priority=5,
        status="active", 
        start_date="2025-01-01",
        end_date=None,
        key_stakeholders=["<EMAIL>"],
        primary_contact="<EMAIL>",
        tags=["persistence"]
    )
    store1.add_project(project)
    
    # Create second store and load data
    store2 = ProjectContextStore(test_file)
    
    # Check data was loaded
    assert "Persistent Project" in store2.projects
    loaded_project = store2.projects["Persistent Project"]
    assert loaded_project.description == "Should persist"
    assert loaded_project.priority == 5
    
    # Cleanup
    os.remove(test_file)
    
    print("✓ data persists across store instances")


def test_raci_operations():
    """Test: if RACI matrix operations work correctly then not broken."""
    test_file = "test_regression_raci.xlsx"
    
    if os.path.exists(test_file):
        os.remove(test_file)
    
    store = ProjectContextStore(test_file)
    
    # Add project first
    project = ProjectInfo(
        name="RACI Test",
        description="Testing RACI",
        priority=3,
        status="active",
        start_date="2025-01-01",
        end_date=None,
        key_stakeholders=["<EMAIL>"],
        primary_contact="<EMAIL>",
        tags=[]
    )
    store.add_project(project)
    
    # Add RACI entries
    entry1 = RACIEntry(
        project_name="RACI Test",
        person_email="<EMAIL>",
        person_name="Alice",
        role="R",
        notes="Responsible for implementation"
    )
    store.add_raci_entry(entry1)
    
    entry2 = RACIEntry(
        project_name="RACI Test",
        person_email="<EMAIL>",
        person_name="Bob",
        role="A",
        notes="Accountable for delivery"
    )
    store.add_raci_entry(entry2)
    
    # Test retrieval
    context = store.get_project_context("RACI Test")
    assert len(context['raci']) == 2
    
    # Test person projects
    alice_projects = store.get_person_projects("<EMAIL>")
    assert "RACI Test" in alice_projects
    
    # Cleanup
    os.remove(test_file)
    
    print("✓ RACI matrix operations work correctly")


def test_document_search():
    """Test: if document search finds relevant documents then not broken."""
    test_file = "test_regression_search.xlsx"
    
    if os.path.exists(test_file):
        os.remove(test_file)
    
    store = ProjectContextStore(test_file)
    
    # Add documents
    doc1 = DocumentContext(
        project_name="Search Test",
        filepath="test/doc1.md",
        doc_type="design",
        summary="This document covers LLM safety features",
        key_points=["safety", "monitoring", "filtering"],
        last_updated="2025-01-01",
        relevance_score=0.9
    )
    store.add_document(doc1)
    
    doc2 = DocumentContext(
        project_name="Search Test",
        filepath="test/doc2.md", 
        doc_type="spec",
        summary="API specification for the system",
        key_points=["api", "endpoints", "authentication"],
        last_updated="2025-01-02",
        relevance_score=0.7
    )
    store.add_document(doc2)
    
    # Test search
    results = store.search_documents("safety")
    assert len(results) == 1
    assert results[0].filepath == "test/doc1.md"
    
    results = store.search_documents("api")
    assert len(results) == 1
    assert results[0].filepath == "test/doc2.md"
    
    # Cleanup
    os.remove(test_file)
    
    print("✓ document search finds relevant documents")


def main():
    """Run all regression tests."""
    print("Running project store regression tests...\n")
    
    test_store_creation_and_save()
    test_data_persistence()
    test_raci_operations()
    test_document_search()
    
    print("\nAll tests passed! ✓")


if __name__ == '__main__':
    main()