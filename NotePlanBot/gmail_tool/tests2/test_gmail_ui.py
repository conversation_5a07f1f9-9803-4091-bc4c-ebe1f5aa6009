"""Regression tests for Gmail UI functionality.

1. Test thread display data extraction
2. Test table creation
3. Test configuration
v1
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tools.gmail.ui import (
    get_thread_display_data, 
    CFG,
    create_threads_table
)


def test_thread_display_data_extraction():
    """Test: if get_thread_display_data correctly formats thread data then not broken."""
    # Mock thread messages
    thread_messages = [
        {
            'from': '<PERSON> <<EMAIL>>',
            'date': 'Mon, 23 Jun 2025 10:00:00 +0000',
            'content': 'Initial message content',
            'thread_id': 'thread123'
        },
        {
            'from': '<PERSON> <<EMAIL>>',
            'date': 'Mon, 23 Jun 2025 11:00:00 +0000', 
            'content': 'Reply to the initial message',
            'thread_id': 'thread123'
        }
    ]
    
    result = get_thread_display_data(thread_messages, "Test Subject")
    
    # Verify all expected fields are present
    assert result is not None
    assert result['subject'] == "Test Subject"
    assert result['msg_count'] == 2
    assert len(result['participants']) > 0
    assert 'duration' in result
    assert 'last_ago' in result
    assert 'last_preview' in result
    assert result['thread_id'] == 'thread123'
    
    print("✓ get_thread_display_data correctly formats thread data")


def test_cfg_defaults():
    """Test: if CFG has correct default values then not broken."""
    cfg = CFG()
    
    assert cfg.threads_per_page == 10
    assert cfg.max_content_preview == 50
    assert cfg.show_labels == True
    assert cfg.show_participants == True
    assert cfg.show_duration == True
    assert cfg.show_last_message == True
    assert cfg.days_back == 7
    assert cfg.include_categories == ['CATEGORY_PERSONAL', 'INBOX']
    
    print("✓ CFG has correct default values")


def test_table_creation():
    """Test: if create_threads_table creates table without errors then not broken."""
    # Mock thread data
    threads_data = [{
        'subject': 'Test Email',
        'msg_count': 5,
        'participants': ['[yellow]John Doe[/yellow] (3)', '[green]Alex Foster[/green] (2)'],
        'duration': '2.5d',
        'last_ago': '0.5d ago',
        'last_preview': '[dim]John:[/dim] This is a test message...',
        'thread_id': 'thread123'
    }]
    
    # Should create table without errors
    table = create_threads_table(threads_data, start_idx=0)
    
    assert table is not None
    assert len(table.columns) >= 4  # At least #, Subject, Msgs, Last
    
    print("✓ create_threads_table creates table without errors")


def test_participant_formatting():
    """Test: if participant names are cleaned and formatted correctly then not broken."""
    thread_messages = [
        {
            'from': 'John Doe (Google Sheets) <<EMAIL>>',
            'date': 'Mon, 23 Jun 2025 10:00:00 +0000',
            'content': 'Test',
            'thread_id': 'thread123'
        },
        {
            'from': 'Jane Smith via LinkedIn <<EMAIL>>',
            'date': 'Mon, 23 Jun 2025 11:00:00 +0000',
            'content': 'Test reply',
            'thread_id': 'thread123'
        }
    ]
    
    result = get_thread_display_data(thread_messages, "Test")
    
    # Check that participant names are cleaned
    participants_str = ', '.join(result['participants'])
    assert '(Google Sheets)' not in participants_str
    assert 'via LinkedIn' not in participants_str
    
    print("✓ participant names are cleaned and formatted correctly")


def main():
    """Run all regression tests."""
    print("Running Gmail UI regression tests...\n")
    
    test_thread_display_data_extraction()
    test_cfg_defaults()
    test_table_creation()
    test_participant_formatting()
    
    print("\nAll tests passed! ✓")


if __name__ == '__main__':
    main()