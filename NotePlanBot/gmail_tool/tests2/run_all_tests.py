"""Run all regression tests for Gmail tools.

1. UI tests
2. Project store tests  
3. Integration tests
v1
"""
import subprocess
import sys
from pathlib import Path


def run_test_file(test_file: str) -> bool:
    """Run a test file and return success status."""
    print(f"\n{'='*60}")
    print(f"Running {test_file}")
    print('='*60)
    
    result = subprocess.run(
        [sys.executable, test_file],
        capture_output=True,
        text=True
    )
    
    print(result.stdout)
    
    if result.returncode != 0:
        print(f"[ERROR] {test_file} failed:")
        print(result.stderr)
        return False
    
    return True


def main():
    """Run all test suites."""
    print("Gmail Tools Regression Test Suite")
    print("=================================\n")
    
    test_dir = Path(__file__).parent
    test_files = [
        "test_gmail_ui.py",
        "test_project_store.py",
        "test_integration.py",
        "test_ui_enhancements.py",
        "test_task_title_generation.py",
        "test_message_extraction.py",
        "test_group_sorting.py",
        "test_edge_cases.py"
    ]
    
    all_passed = True
    
    for test_file in test_files:
        test_path = test_dir / test_file
        if test_path.exists():
            if not run_test_file(str(test_path)):
                all_passed = False
        else:
            print(f"[WARNING] Test file not found: {test_file}")
    
    print("\n" + "="*60)
    if all_passed:
        print("✅ ALL TESTS PASSED!")
    else:
        print("❌ Some tests failed. Please check the output above.")
    print("="*60)
    
    return 0 if all_passed else 1


if __name__ == '__main__':
    sys.exit(main())