"""Regression tests for UI enhancement features.

1. Test task management
2. Test snooze functionality
3. Test email grouping
v1
"""
import sys
import os
import json
from pathlib import Path
from datetime import datetime, timedelta
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tools.gmail.ui_task_manager import TaskManager, TaskStatus
from tools.gmail.ui_snooze_manager import SnoozeManager
from tools.gmail.models import EmailGroup


def test_task_creation_and_retrieval():
    """Test: if task manager creates and retrieves tasks then not broken."""
    test_file = "test_regression_tasks.json"
    
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = TaskManager(test_file)
    
    # Create task
    task = manager.create_task(
        title="Test Task",
        description="Test Description",
        source_thread_id="test_thread_123",
        deadline=datetime.now() + timedelta(days=1)
    )
    
    # Verify creation
    assert task.id is not None
    assert task.title == "Test Task"
    assert task.status == TaskStatus.PENDING
    
    # Verify retrieval
    retrieved = manager.get_task(task.id)
    assert retrieved is not None
    assert retrieved.title == task.title
    
    # Clean up
    os.remove(test_file)
    
    print("✓ task manager creates and retrieves tasks")


def test_task_status_updates():
    """Test: if task status updates work correctly then not broken."""
    test_file = "test_regression_task_status.json"
    
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = TaskManager(test_file)
    
    # Create and update task
    task = manager.create_task("Status Test", "Test", "thread_123")
    success = manager.update_task_status(task.id, TaskStatus.IN_PROGRESS)
    
    assert success == True
    
    # Verify update
    updated = manager.get_task(task.id)
    assert updated.status == TaskStatus.IN_PROGRESS
    
    # Clean up
    os.remove(test_file)
    
    print("✓ task status updates work correctly")


def test_snooze_queue_operations():
    """Test: if snooze queue adds and removes emails correctly then not broken."""
    test_file = "test_regression_snooze.json"
    
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = SnoozeManager(test_file)
    
    # Snooze email
    thread_id = "test_thread_456"
    until = datetime.now() + timedelta(hours=2)
    manager.snooze_email(thread_id, "msg_456", until, 0)
    
    # Verify snoozed
    assert manager.is_snoozed(thread_id) == True
    assert thread_id in manager.get_snoozed_thread_ids()
    
    # Unsnooze
    success = manager.unsnooze_email(thread_id)
    assert success == True
    assert manager.is_snoozed(thread_id) == False
    
    # Clean up
    os.remove(test_file)
    
    print("✓ snooze queue adds and removes emails correctly")


def test_snooze_expiry():
    """Test: if expired snoozes are automatically removed then not broken."""
    test_file = "test_regression_expiry.json"
    
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = SnoozeManager(test_file)
    
    # Add expired snooze
    expired_time = datetime.now() - timedelta(hours=1)
    manager.snooze_email("expired_thread", "expired_msg", expired_time, 0)
    
    # Check - should be auto-removed
    active = manager.get_snoozed_thread_ids()
    assert "expired_thread" not in active
    assert manager.is_snoozed("expired_thread") == False
    
    # Clean up
    os.remove(test_file)
    
    print("✓ expired snoozes are automatically removed")


def test_email_group_model():
    """Test: if EmailGroup model handles data correctly then not broken."""
    # Create test group
    group = EmailGroup(
        groups={1: "Work", 2: "Personal"},
        assignments={"thread1": 1, "thread2": 2},
        reasoning="Test reasoning"
    )
    
    # Verify structure
    assert len(group.groups) == 2
    assert group.groups[1] == "Work"
    assert group.assignments["thread1"] == 1
    assert group.reasoning == "Test reasoning"
    
    # Verify JSON serialization
    json_data = group.model_dump()
    assert "groups" in json_data
    assert "assignments" in json_data
    assert "reasoning" in json_data
    
    print("✓ EmailGroup model handles data correctly")


def test_active_tasks_filtering():
    """Test: if get_active_tasks filters completed tasks then not broken."""
    test_file = "test_regression_active.json"
    
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = TaskManager(test_file)
    
    # Create tasks with different statuses
    task1 = manager.create_task("Active 1", "Test", "thread1")
    task2 = manager.create_task("Active 2", "Test", "thread2")
    task3 = manager.create_task("Completed", "Test", "thread3")
    
    # Mark one as completed
    manager.update_task_status(task3.id, TaskStatus.COMPLETED)
    
    # Get active tasks
    active = manager.get_active_tasks()
    active_ids = [t.id for t in active]
    
    assert task1.id in active_ids
    assert task2.id in active_ids
    assert task3.id not in active_ids
    assert len(active) == 2
    
    # Clean up
    os.remove(test_file)
    
    print("✓ get_active_tasks filters completed tasks")


def main():
    """Run all UI enhancement regression tests."""
    print("Running UI enhancement regression tests...\n")
    
    test_task_creation_and_retrieval()
    test_task_status_updates()
    test_snooze_queue_operations()
    test_snooze_expiry()
    test_email_group_model()
    test_active_tasks_filtering()
    
    print("\nAll tests passed! ✓")


if __name__ == '__main__':
    main()