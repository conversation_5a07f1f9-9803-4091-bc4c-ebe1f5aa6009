"""Test message extraction functionality.

1. Test recursive MIME parsing
2. Test HTML to text conversion
3. Test nested multipart handling
v1
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.gmail.service import GmailService


def test_extract_content_handles_nested_parts():
    """Test that _extract_content handles nested multipart messages.
    
    1. Create nested structure
    2. Extract content
    3. Verify extraction
    v1
    """
    gmail = GmailService()
    
    # Test nested multipart structure
    test_message = {
        'payload': {
            'mimeType': 'multipart/mixed',
            'parts': [
                {
                    'mimeType': 'multipart/alternative',
                    'parts': [
                        {
                            'mimeType': 'text/plain',
                            'body': {
                                'data': 'VGVzdCBjb250ZW50'  # "Test content" in base64
                            }
                        }
                    ]
                }
            ]
        }
    }
    
    content = gmail._extract_content(test_message)
    if "Test content" not in content:
        print("✗ Nested multipart extraction failed")
        return False
    
    print("✓ Nested multipart extraction works")
    return True


def test_html_fallback():
    """Test HTML to text conversion fallback.
    
    1. Create HTML-only message
    2. Extract content
    3. Verify conversion
    v1
    """
    gmail = GmailService()
    
    # HTML-only message
    test_message = {
        'payload': {
            'mimeType': 'text/html',
            'body': {
                'data': 'PGI+SGVsbG88L2I+IHdvcmxk'  # "<b>Hello</b> world" in base64
            }
        }
    }
    
    content = gmail._extract_content(test_message)
    if "Hello world" not in content:
        print("✗ HTML to text conversion failed")
        return False
    
    print("✓ HTML to text conversion works")
    return True


def test_empty_message_handling():
    """Test empty message handling.
    
    1. Create empty message
    2. Extract content
    3. Verify no crash
    v1
    """
    gmail = GmailService()
    
    # Empty message
    test_message = {
        'payload': {
            'mimeType': 'text/plain'
            # No body
        }
    }
    
    content = gmail._extract_content(test_message)
    if content is None:
        print("✗ Empty message returned None")
        return False
    
    print("✓ Empty message handling works")
    return True


def main():
    """Run all message extraction tests.
    
    1. Test nested parts
    2. Test HTML fallback
    3. Test empty messages
    v1
    """
    print("Testing message extraction functionality...")
    
    tests = [
        test_extract_content_handles_nested_parts,
        test_html_fallback,
        test_empty_message_handling
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    if all_passed:
        print("\nAll message extraction tests passed!")
    else:
        print("\nSome message extraction tests failed!")
    
    return all_passed


if __name__ == "__main__":
    main()