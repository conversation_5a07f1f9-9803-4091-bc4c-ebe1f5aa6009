# Gmail Tools - CLAUDE.md

## Overview

This codebase contains tools for managing Gmail emails and Google Calendar events. The system was originally focused on calendar event filtering and display, but has been extended with comprehensive Gmail management features.

## What Was Already There

### Core Gmail/Calendar Infrastructure
- **Authentication** (`src/gcal/auth.py`, `src/tools/gmail/auth.py`): OAuth2 authentication for Google APIs
- **Gmail Service** (`src/tools/gmail/service.py`): Core Gmail API operations including message listing, thread retrieval, and email sending
- **Calendar Service** (`src/gcal/service.py`): Google Calendar API integration
- **Email Analysis** (`src/tools/gmail/analyze_threads.py`): Thread analysis with Rich formatting, content cleaning, and participant extraction
- **Message Drafting** (`src/tools/gmail/drafts.py`): OpenAI-based email draft generation using GPT-4
- **Context Storage** (`src/tools/gmail/storage/context.py`): JSON-based email context storage
- **User Context** (`src/tools/gmail/storage/alex_context.py`): User preferences and project RACI matrices

### Existing Features
- Email thread analysis and display
- Content cleaning (removing quotes, signatures)
- Participant name detection and highlighting
- Basic reply analysis (`reply_manager.py`)
- Calendar event filtering and display
- **Attachments function** (added by Alex in `service.py`) - MAY NOT WORK PROPERLY

## What I Added

### 1. Interactive Terminal UI (`src/tools/gmail/ui.py`)
- **Rich table display** with pagination (10 threads per page)
- **Numbered row selection**: 1-10 for direct access, 11+ format (11 = thread 1)
- **Column configuration**: Toggle visibility of participants, duration, preview
- **Navigation**: Next/previous page, refresh, column settings
- **Configuration class** (CFG) with customizable display settings
- **Email grouping** (option 3): LLM-based automatic categorization with reasoning
- **Email snoozing** (option 4): Temporary hiding with time-based queue
- **Task creation** (option 5): Convert emails to tasks with deadlines
- **Task list display**: Separate table above email list showing active tasks

### 2. Project Context Store (`src/tools/gmail/storage/project_store.py`)
- **Excel-based storage** using openpyxl for easy data inspection
- **Data models**:
  - ProjectInfo: Core project metadata
  - RACIEntry: Responsibility assignments
  - DocumentContext: Project documentation with LLM summaries
  - PersonContext: Contact information and preferences
- **Full CRUD operations** with auto-save
- **Query functions**: Get project context, find person's projects, search documents

### 3. Project Finding (`src/tools/gmail/find_projects.py`)
- **Async email scanning** for project mentions
- **LLM-based extraction** using OpenAI GPT-4
- **Confidence scoring** for project identification
- **Draft generation** for project updates or new project creation
- **Parallel processing** for performance

### 4. Email Reply Drafting (`src/tools/gmail/draft_replies.py`)
- **Claude-powered drafting** using claude-sonnet-4-20250514
- **Thread analysis**: Extracts key points, questions, action items
- **Project context integration**: Retrieves relevant project information
- **Professional tone**: Maintains Alex Foster's role as R&D Lead at Invisible
- **Batch processing**: Handles multiple threads efficiently

### 5. Hierarchical Context Retrieval (`src/tools/gmail/context_retrieval.py`)
- **Two-tier architecture**:
  - Tier 1: Parallel relevance checking across all sources
  - Tier 2: Intelligent LLM-based ranking using Claude
- **Async parallel processing** for speed
- **Context summarization** with missing information identification
- **Multiple source types**: Projects, documents, emails, people

### 6. Comprehensive Test Suite (`tests2/`)
- **UI tests** (`test_gmail_ui.py`): Thread display, configuration, table creation
- **Storage tests** (`test_project_store.py`): Excel operations, persistence, RACI
- **Integration tests** (`test_integration.py`): Content cleaning, user detection
- **Test runner** (`run_all_tests.py`): Executes all test suites
- **Regression-focused**: Only tests verified working functionality

## Key Design Decisions

### Purposeful Brittleness
- No exception handling in main code paths
- No API mocking
- No fallbacks
- Designed for development transparency

### Storage Choice
- Excel format for project store (vs JSON/SQLite) for easy manual inspection
- Maintains existing JSON storage for email contexts
- Separation of concerns between email and project data

### UI Design
- Rich library for beautiful terminal output
- Numbered shortcuts following vim-like patterns
- Flexible configuration without overwhelming options

### AI Integration
- Claude for complex tasks (reply drafting, context ranking)
- GPT-3.5/4 for simpler tasks (relevance checking, project extraction)
- Hierarchical approach balances cost and quality

## Configuration

### Environment Variables (.env)
```
OPENAI_API_KEY=your-key-here
ANTHROPIC_API_KEY=your-key-here
```

### Default Settings
- UI: 10 threads per page, 7 days lookback, personal inbox focus
- Context Store: project_contexts.xlsx in working directory
- Reply Drafting: 150-word limit, professional tone

## Usage

### Main Commands
```bash
# Interactive UI for browsing emails
python -m src.tools.gmail.ui

# Generate draft replies
python -m src.tools.gmail.draft_replies

# Find projects in emails
python -m src.tools.gmail.find_projects

# Test context retrieval
python -m src.tools.gmail.context_retrieval

# Run all tests
python tests2/run_all_tests.py
```

### UI Navigation
- `1-10`: Open thread by number
- `11, 12, ...`: Open thread 1, 2, ... (extended format)
- `n/p`: Next/previous page
- `c`: Column settings
- `r`: Refresh
- `q`: Quit

## Architecture

### Module Structure
```
src/tools/gmail/
├── ui.py                    # NEW: Interactive terminal UI
├── draft_replies.py         # NEW: Claude-based reply drafting
├── find_projects.py         # NEW: Project extraction from emails
├── context_retrieval.py     # NEW: Hierarchical context system
├── storage/
│   ├── project_store.py     # NEW: Excel-based project storage
│   ├── context.py           # EXISTING: Email context storage
│   └── alex_context.py      # EXISTING: User preferences
├── analyze_threads.py       # EXISTING: Thread analysis
├── service.py              # EXISTING: Gmail API operations
└── auth.py                 # EXISTING: Authentication
```

### Data Flow
1. Gmail API → Service layer → Analysis/Display
2. Emails → Project extraction → Excel storage
3. Thread analysis → Context retrieval → Reply generation
4. All operations support async/parallel processing

## UI Enhancement Features (Implemented)

### Email Grouping (Option 3) - COMPLETED
- **Implementation**: `email_grouper.py` uses GPT-4 to analyze and group emails
- **Usage**: Press '3' in UI to group visible emails
- **Features**:
  - Dynamic group discovery based on email content
  - Shows reasoning for group assignments
  - Adds optional "Group" column to display
  - Groups persist until refresh

### Email Snoozing (Option 4) - COMPLETED
- **Implementation**: `snooze_manager.py` maintains JSON queue
- **Usage**: Press '4x' where x is thread number (e.g., '41' snoozes thread 1)
- **Features**:
  - Snooze for 1 hour, 4 hours, tomorrow 9am, or next week
  - Automatically filters snoozed emails from display
  - Expired snoozes auto-removed on check
  - Emails remain in actual Gmail inbox

### Task Management (Option 5) - COMPLETED
- **Implementation**: `task_manager.py` with Pydantic models
- **Usage**: Press '5x' to create task from thread x
- **Features**:
  - Tasks display in green table above email list
  - **AI-powered task title generation** using Claude Haiku for action-oriented titles
  - **Quick accept**: Press 'd' to use AI-suggested title
  - Set deadlines: today, tomorrow, next week, custom, or none
  - Status tracking: pending, in_progress, completed, cancelled
  - Tasks persist in `gmail_tasks.json`

### New Files Added
- `models.py`: Pydantic models (Task, SnoozedEmail, EmailGroup)
- `task_manager.py`: Task CRUD operations
- `snooze_manager.py`: Snooze queue management
- `email_grouper.py`: LLM-based email grouping
- `tests2/test_ui_enhancements.py`: Regression tests for all features

## Testing Strategy

### Test Organization
- Separate `tests2/` directory for new regression tests
- Each module has corresponding test file
- Tests follow "if X then not broken" pattern
- Only test verified working functionality

### Test Coverage
- UI: Display formatting, configuration, user interaction
- Storage: CRUD operations, persistence, search
- Integration: Content processing, user detection
- All tests must pass before commits

## Future Enhancements

### Potential Improvements
1. Web UI using existing Rich formatting
2. Scheduled email drafting
3. Template system for common replies
4. Advanced project analytics
5. Calendar integration for project timelines

### Technical Debt
1. Consolidate authentication (currently duplicated)
2. Unify storage formats (JSON vs Excel)
3. Implement proper logging
4. Add performance metrics

## Recent Updates (2025-06-26)

### Fixed: Empty Message Content Issue
- **Problem**: Messages showing as "(empty message)" in UI due to nested multipart structures
- **Root Cause**: `_extract_content` in service.py only checked direct parts, missing nested multipart/alternative inside multipart/mixed
- **Solution**: Implemented recursive part traversal in `_extract_content` to handle nested MIME structures
- **Also Added**: Basic HTML-to-text conversion for HTML-only emails

### Fixed: Group Sorting Issue  
- **Problem**: After grouping emails, table wasn't sorting by group first
- **Root Cause**: `fetch_threads_page` wasn't applying any sorting after grouping
- **Solution**: Modified to sort by group ID first, then by original order (recency) within each group

### Added: AI Task Title Generation
- **Feature**: LLM-powered task title generation using Claude Haiku
- **Implementation**: Added `generate_task_title()` method to TaskManager
- **Usage**: When creating tasks (option 5x), AI suggests action-oriented titles
- **UI Enhancement**: Shows both default title (original subject, press Enter) and AI title (press 'd')
- **Benefit**: Converts email subjects into clear, actionable task titles

## Troubleshooting

### Common Issues
1. **Empty messages**: ~~Many emails show as empty due to HTML-only content~~ **FIXED**: Now properly extracts nested multipart content and converts HTML to text
2. **API rate limits**: Implement backoff for large email volumes
3. **Model availability**: Ensure Claude model name is current
4. **Excel file locking**: Close Excel before running project store operations

### Debug Commands
```bash
# Check authentication
python -m src.tools.gmail.auth

# Test project store
python -m src.tools.gmail.storage.project_store

# Verify Rich formatting
python -m src.tools.gmail.analyze_threads
```

---

*Last updated: 2025-06-26*
*Gmail tools extension by Claude*