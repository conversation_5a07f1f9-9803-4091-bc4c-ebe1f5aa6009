[{"id": "1981ebbc2776318e", "threadId": "1981ebbc2776318e", "from": "Zach from Warp <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "Warp - checking in", "date": "Fri, 18 Jul 2025 18:11:17 +0000", "snippet": "Hey there, Thanks for taking <PERSON><PERSON> for a spin. We are periodically checking on how the app is working for folks and wondering how your experience has been. Have you encountered any bugs, issues, or"}, {"id": "1981eb504375a131", "threadId": "1981eb504375a131", "from": "<PERSON> <<EMAIL>>", "to": "<PERSON> <<EMAIL>>", "cc": "", "subject": "dagster+", "date": "Fri, 18 Jul 2025 18:03:54 +0000", "snippet": "Hi <PERSON> - How has onboarding to <PERSON><PERSON><PERSON>+ been going so far? I want to ensure you&#39;re set up for success, so please let me know if you have any questions or if you are good on your own? I do not want"}, {"id": "1981e9a753f893e5", "threadId": "1981e9a753f893e5", "from": "\"<PERSON> via Otter.ai\" <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "Meeting Summary for troubleshooting between <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "date": "Fri, 18 Jul 2025 17:34:55 +0000 (UTC)", "snippet": "<PERSON> has shared notes from troubleshooting between <PERSON><PERSON> and <PERSON><PERSON><PERSON>, Jul 18 . <PERSON><PERSON><PERSON> and <PERSON> discussed issues with a workflow recorder tool. <PERSON><PERSON><PERSON>"}, {"id": "1981e8e6e5fbd8a0", "threadId": "1979da35a800cb88", "from": "<PERSON> <<EMAIL>>", "to": "<PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>", "cc": "louis be<PERSON> <<EMAIL>>", "subject": "Re: Mediar <> Invisible", "date": "Fri, 18 Jul 2025 10:21:45 -0700", "snippet": "Hey <PERSON>, <PERSON><PERSON><PERSON>, ​ I just invited you to a slack channel for fast feedback, <PERSON><PERSON><PERSON>, please join it and help us troubleshoot your issues: provide you with programmatic access to the data, fix UI"}, {"id": "1981e8c7c2912a84", "threadId": "198146578d377220", "from": "<PERSON><PERSON><PERSON> <<EMAIL>>", "to": "\"<EMAIL>\" <<EMAIL>>", "cc": "", "subject": "IT-9929 <PERSON>", "date": "<PERSON><PERSON>, 18 Jul 2025 17:19:39 +0000", "snippet": "—-—-—-— Reply above this line. <PERSON><PERSON><PERSON> commented: Hello <PERSON> Is this still an issue? <PERSON><PERSON>, <PERSON> request · Turn off this request&#39;s notifications This is shared with <PERSON>"}, {"id": "1981e8afcd1c8e4c", "threadId": "1981e8afcd1c8e4c", "from": "Slack <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "<PERSON><PERSON> invited you to work together in Slack", "date": "Fri, 18 Jul 2025 17:18:01 +0000", "snippet": "The team at screenpipe wants to work together You were invited by matt (<EMAIL>) To work with screenpipe (screenpipe.slack.com) In this channel #invisible-email---mediar Get Started This"}, {"id": "1981e60fd882b3d1", "threadId": "1981e60fd882b3d1", "from": "\"<PERSON> via Otter.ai\" <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "<PERSON> shared troubleshooting between <PERSON><PERSON> and <PERSON><PERSON><PERSON> in Otter", "date": "Fri, 18 Jul 2025 16:32:07 +0000 (UTC)", "snippet": "<PERSON> shared a conversation with you Otter.ai logo <PERSON> shared a conversation with you troubleshooting between <PERSON><PERSON> and <PERSON><PERSON><PERSON> Jul 18, 9:30 am Open in Otter"}, {"id": "1981e2ac3b1f133a", "threadId": "1981e2ac3b1f133a", "from": "<PERSON> <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "Deleting data in Rewind 🙅‍♀️", "date": "Fri, 18 Jul 2025 15:32:52 +0000", "snippet": "Ever wish you could delete recorded data in Rewind? If you accidentally recorded audio, you can easily delete it by clicking on the three dots located next to the transcript, and then selecting &quot;"}, {"id": "1981e22f51de5e21", "threadId": "1981e22f51de5e21", "from": "Every <<EMAIL>>", "to": "\"'<PERSON><PERSON>'\" <<EMAIL>>", "cc": "", "subject": "Vibe Check: Grok 4 Aced Its Exams. The Real World Is a Different Story.", "date": "Fri, 18 Jul 2025 15:24:20 +0000", "snippet": "The smartest model isn&#39;t always the most useful one ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌"}, {"id": "1981df26019b33d7", "threadId": "1981df26019b33d7", "from": "Zoom App Marketplace <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "Your app request is on the way!", "date": "Fri, 18 Jul 2025 14:31:19 +0000 (UTC)", "snippet": "Hi <PERSON>, Your account admin will review your request to add Raycast app. In the mean time, you can explore more apps on Zoom App Marketplace. For urgent requests, please contact your account"}, {"id": "1981ddd201c41776", "threadId": "1981ddd201c41776", "from": "<PERSON> <<EMAIL>>", "to": "<PERSON> <<EMAIL>>", "cc": "", "subject": "Accepted: <PERSON>  @ Mon Jul 21, 2025 8pm - 8:30pm (BST) (<PERSON>)", "date": "Fri, 18 Jul 2025 14:08:07 +0000", "snippet": "<PERSON> <PERSON> <PERSON> has accepted this invitation. Join Zoom Meeting inv-tech.zoom.us/j/***********?j... ID: *********** Join by phone (US) ******-444-9171 Join using SIP ***********@zoomcrc"}, {"id": "1981dabf21a7adb6", "threadId": "1981dabf21a7adb6", "from": "Rippling <<EMAIL>>", "to": "<PERSON> <<EMAIL>>", "cc": "", "subject": "Action required: You have pending tasks in Invisible Technologies Inc.", "date": "Fri, 18 Jul 2025 13:14:24 +0000 (UTC)", "snippet": "Hello <PERSON>, You have 2 upcoming tasks for Invisible Technologies Inc., 0 of which are new. Here&#39;s when everything is due. Upcoming tasks • Showing 2 of 2 Task Due date New hire welcome -- Complete"}, {"id": "1981da11eab868ad", "threadId": "1981da11eab868ad", "from": "Royal Mail <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "Your parcel from XES Holdings has been delivered to your Safeplace. How did we do?", "date": "Fri, 18 Jul 2025 13:02:33 +0000", "snippet": "Hi <PERSON> We delivered your parcel from XES Holdings today, Friday, 18 July 2025. We left it in your Safeplace: Enclosed porch How did we do? Tell us about your delivery experience. Keep tabs on"}, {"id": "1981d9c4b31dd826", "threadId": "1981d9c4b31dd826", "from": "Reclaim <<EMAIL>>", "to": "<EMAIL>", "cc": "", "subject": "🎉 Weekly Report at Reclaim: Jul 12 - 18", "date": "Fri, 18 Jul 2025 12:57:18 +0000", "snippet": "Reclaim.ai NEW: Calendar categorization 🗂️ Easily manage all your Calendar Syncs in a refreshed UI, and now categorize your calendars for consistent color coding and classification in Stats and Slack"}, {"id": "1981d9a83708bc0f", "threadId": "1981d9a83708bc0f", "from": "<PERSON> <angel<PERSON>.<EMAIL>>", "to": "<PERSON> <<EMAIL>>", "cc": "Pay Roll <<EMAIL>>, <PERSON> <<EMAIL>>", "subject": "Bonus for Partner Referral", "date": "Fri, 18 Jul 2025 08:54:55 -0400", "snippet": "Hello <PERSON>, I hope you are well. I&#39;m writing to inform you that we have successfully processed the $1000 bonus for each milestone, 90 days and 180 days of employment, totaling $2000 for the partner"}]