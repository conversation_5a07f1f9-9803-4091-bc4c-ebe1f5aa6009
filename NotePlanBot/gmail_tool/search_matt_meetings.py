#!/usr/bin/env python3
"""Search for emails mentioning <PERSON>, <PERSON>, CEO, or Thursday meetings.

Searches both subject lines and email content.
"""
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tools.gmail.service import GmailService
from rich.console import <PERSON>sole
from rich.table import Table

console = Console()


def search_emails(days_back: int = 14):
    """Search for emails with specific keywords.
    
    Args:
        days_back: Number of days to search back (default: 14)
    """
    gmail = GmailService()
    
    # Calculate date range
    after_date = datetime.utcnow() - timedelta(days=days_back)
    date_str = after_date.strftime("%Y/%m/%d")
    
    # Search queries for different terms
    search_terms = [
        f'after:{date_str} ("Matt F" OR "<PERSON>" OR "CEO" OR "Thursday" OR "meeting")',
        f'after:{date_str} subject:("<PERSON>" OR "<PERSON>" OR "CEO" OR "Thursday meeting")',
    ]
    
    all_messages = {}  # Use dict to avoid duplicates
    
    console.print(f"\n[cyan]Searching emails from the last {days_back} days...[/cyan]")
    console.print("=" * 80)
    
    for query in search_terms:
        console.print(f"\n[yellow]Query:[/yellow] {query}")
        
        try:
            # Search using Gmail API
            results = gmail.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=50
            ).execute()
            
            messages = results.get('messages', [])
            
            if messages:
                console.print(f"Found {len(messages)} messages")
                
                # Get details for each message
                for msg in messages:
                    msg_id = msg['id']
                    
                    if msg_id not in all_messages:
                        # Get message details
                        msg_data = gmail.service.users().messages().get(
                            userId='me',
                            id=msg_id,
                            format='metadata',
                            metadataHeaders=['From', 'Subject', 'Date', 'To', 'Cc']
                        ).execute()
                        
                        headers = msg_data['payload']['headers']
                        
                        all_messages[msg_id] = {
                            'id': msg_id,
                            'thread_id': msg_data['threadId'],
                            'subject': next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject'),
                            'from': next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown'),
                            'date': next((h['value'] for h in headers if h['name'] == 'Date'), ''),
                            'to': next((h['value'] for h in headers if h['name'] == 'To'), ''),
                            'cc': next((h['value'] for h in headers if h['name'] == 'Cc'), ''),
                            'labels': gmail.get_label_names(msg_data.get('labelIds', []))
                        }
            else:
                console.print("No messages found for this query")
                
        except Exception as e:
            console.print(f"[red]Error searching: {e}[/red]")
    
    # Display results
    if all_messages:
        console.print(f"\n[green]Total unique messages found: {len(all_messages)}[/green]")
        
        # Create table
        table = Table(title="Search Results", show_lines=True)
        table.add_column("Date", style="cyan", width=20)
        table.add_column("From", style="yellow", width=30)
        table.add_column("Subject", style="white", width=50)
        table.add_column("Labels", style="magenta", width=20)
        
        # Sort by date (newest first)
        def parse_date(date_str):
            if not date_str:
                return datetime.min.replace(tzinfo=None)
            try:
                # Try standard format first
                return datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %z')
            except ValueError:
                try:
                    # Try without timezone
                    return datetime.strptime(date_str.split(' (')[0], '%a, %d %b %Y %H:%M:%S %z')
                except:
                    try:
                        # Try alternative format
                        return datetime.strptime(date_str[:25], '%a, %d %b %Y %H:%M:%S')
                    except:
                        return datetime.min.replace(tzinfo=None)
        
        sorted_messages = sorted(
            all_messages.values(),
            key=lambda x: parse_date(x['date']),
            reverse=True
        )
        
        for msg in sorted_messages:
            # Format date
            try:
                date_obj = datetime.strptime(msg['date'], '%a, %d %b %Y %H:%M:%S %z')
                date_str = date_obj.strftime('%Y-%m-%d %H:%M')
            except:
                date_str = msg['date'][:20] if msg['date'] else 'Unknown'
            
            # Extract sender name
            from_name = msg['from'].split('<')[0].strip()
            
            # Highlight relevant keywords in subject
            subject = msg['subject']
            keywords = ['Matt F', 'Matt Fitzpatrick', 'CEO', 'Thursday', 'meeting']
            for keyword in keywords:
                if keyword.lower() in subject.lower():
                    subject = subject.replace(keyword, f"[bold red]{keyword}[/bold red]")
                    subject = subject.replace(keyword.lower(), f"[bold red]{keyword.lower()}[/bold red]")
                    subject = subject.replace(keyword.upper(), f"[bold red]{keyword.upper()}[/bold red]")
            
            table.add_row(
                date_str,
                from_name,
                subject,
                ', '.join(msg['labels'][:3])  # Show first 3 labels
            )
            
            # Show CC if relevant
            if msg['cc'] and any(keyword in msg['cc'] for keyword in ['Matt F', 'Matt Fitzpatrick']):
                console.print(f"    [dim]CC: {msg['cc']}[/dim]")
        
        console.print(table)
        
        # Check for meeting invites
        console.print("\n[cyan]Checking for calendar invites...[/cyan]")
        meeting_count = 0
        for msg in sorted_messages:
            # Check if it's a calendar invite
            if any(label in msg['labels'] for label in ['📅 Calendar', 'CATEGORY_UPDATES']):
                meeting_count += 1
                console.print(f"\n[yellow]Possible meeting invite:[/yellow]")
                console.print(f"  From: {msg['from']}")
                console.print(f"  Subject: {msg['subject']}")
                console.print(f"  Date: {msg['date']}")
        
        if meeting_count == 0:
            console.print("[dim]No calendar invites found in results[/dim]")
    else:
        console.print("\n[yellow]No messages found matching the search criteria[/yellow]")


if __name__ == '__main__':
    # Search last 14 days by default
    days = 14
    if len(sys.argv) > 1:
        try:
            days = int(sys.argv[1])
        except ValueError:
            console.print(f"[red]Invalid number of days: {sys.argv[1]}[/red]")
            sys.exit(1)
    
    search_emails(days)