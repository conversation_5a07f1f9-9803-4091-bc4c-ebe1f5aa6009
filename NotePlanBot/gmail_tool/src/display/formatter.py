from typing import List
from datetime import datetime, timedelta
from rich.table import Table
from rich.box import ROUNDED
from rich.style import Style
from models.event import Event


def format_time(event: Event) -> str:
    """Format event time for display.
    
    1. Handle all-day events
    2. Format date and time
    3. Add duration
    v2
    """
    if event.is_all_day:
        return "All day"
    
    start = event.start_datetime
    if not start:
        return "No time"
    
    # Format time
    time_str = start.strftime("%I:%M %p")
    if event.duration_minutes:
        time_str += f" ({event.duration_minutes}m)"
    
    return time_str


def format_attendees(event: Event) -> str:
    """Format attendee information.
    
    1. Show count
    2. Show response status
    3. Handle no attendees
    v2
    """
    if not event.attendees:
        return "No attendees"
    
    status_counts = {
        'accepted': 0,
        'declined': 0,
        'tentative': 0,
        'needsAction': 0
    }
    
    for attendee in event.attendees:
        status = attendee.get('responseStatus', 'needsAction')
        status_counts[status] += 1
    
    parts = []
    if status_counts['accepted']:
        parts.append(f"{status_counts['accepted']}✓")
    if status_counts['declined']:
        parts.append(f"{status_counts['declined']}✗")
    if status_counts['tentative']:
        parts.append(f"{status_counts['tentative']}?")
    if status_counts['needsAction']:
        parts.append(f"{status_counts['needsAction']}•")
    
    return f"{event.attendee_count} ({' '.join(parts)})"


def format_status_line(event: Event) -> str:
    """Format event status information.
    
    1. Check event status
    2. Check transparency
    3. Check recurrence
    v1
    """
    status_line = []
    if event.status != 'confirmed':
        status_line.append(event.status.upper())
    if event.transparency == 'transparent':
        status_line.append('FREE')
    if event.is_recurring:
        status_line.append('RECURRING')
    
    return ' | '.join(status_line) if status_line else ''


def is_ignorable_event(event: Event) -> bool:
    """Check if event should be displayed in grey.
    
    1. Check filter matches
    2. Check transparency
    v1
    """
    ignorable_filters = {'sleep', 'ooo', 'free', 'optional', 'routine'}
    if hasattr(event, 'filter_matches'):
        if any(f in event.filter_matches for f in ignorable_filters):
            return True
    return event.transparency == 'transparent'


def get_event_style(event: Event) -> Style:
    """Get the style for an event row.
    
    1. Check evening plans
    2. Check focus time
    3. Check ignorable status
    4. Return appropriate style
    v4
    """
    # Get all filter matches in lowercase for case-insensitive comparison
    matches = [f.lower() for f in event.filter_matches]
    
    # Check for focus time first (it's a filter match)
    if "focus_time" in matches:
        return Style(color="orange1", bold=True)
    # Then evening plans (it's an auto tag)
    elif "Evening Plans" in event.filter_matches:  # This one is case-sensitive since we control it
        return Style(color="bright_green", bold=True)
    # Then ignorable events
    elif is_ignorable_event(event):
        return Style(color="grey50", dim=True)
    # Default style
    return Style(color="green")


def create_event_table(events: List[Event], week_start: datetime = None) -> Table:
    """Create a rich table for events display.
    
    1. Setup table with columns
    2. Add event rows with formatting
    3. Apply styling
    v6
    """
    # Create title based on week
    title = "Upcoming Calendar Events"
    if week_start:
        week_end = week_start + timedelta(days=6)
        title = f"Calendar Events: {week_start.strftime('%d-%b-%Y')} to {week_end.strftime('%d-%b-%Y')}"
    
    table = Table(
        title=title,
        box=ROUNDED,
        expand=True,
        show_header=True,
        header_style="bold magenta",
        width=240  # 2x wider
    )
    
    # Add columns with adjusted widths
    table.add_column("Time", style="cyan", width=25)
    table.add_column("Title", width=70)  # Style will be dynamic per row
    table.add_column("Attendees", style="yellow", width=35)
    table.add_column("Location", style="blue", width=40)
    table.add_column("Status", style="magenta", width=35)
    table.add_column("Tags", style="red", width=35)
    
    # Track current day for dividers
    current_day = None
    
    # Add rows
    for event in events:
        if not event.start_datetime:
            continue
            
        event_day = event.start_datetime.date()
        
        # Add day divider if needed
        if current_day != event_day:
            if current_day is not None:
                # Weekend divider (Saturday/Sunday)
                if event_day.weekday() >= 5:
                    table.add_row(
                        "═" * 25, "═" * 70, "═" * 35, "═" * 40, "═" * 35, "═" * 35,
                        style=Style(color="yellow", bold=True)
                    )
                else:
                    # Weekday divider
                    table.add_row(
                        "─" * 25, "─" * 70, "─" * 35, "─" * 40, "─" * 35, "─" * 35,
                        style=Style(color="grey37")
                    )
                
            # Add date header
            table.add_row(
                event_day.strftime("%d-%b-%Y"),
                "", "", "", "", "",
                style=Style(color="cyan", bold=True)
            )
            current_day = event_day
        
        # Format event data
        status = format_status_line(event)
        location = event.location or (
            "🎥 Meet" if event.hangout_link else 
            "🎥 Conference" if event.conference_data else ""
        )
        tags = ", ".join(event.filter_matches) if hasattr(event, 'filter_matches') else ""
        
        # Get event style
        event_style = get_event_style(event)
        
        # Add event row with appropriate style
        table.add_row(
            format_time(event),
            event.title,
            format_attendees(event),
            location,
            status,
            tags,
            style=event_style
        )
    
    return table