"""Main entry point for calendar operations.

1. Handle events
2. Apply filters
3. Show reports
v2
"""
from typing import List, Optional
from rich.console import Console
from . import service
from display import display_events
from models.event import Event
from filters.manager import create_default_filters


console = Console()


def analyze_calendar(
    weeks_ahead: int = 3,
    show_filters: bool = True
) -> List[Event]:
    """Analyze upcoming calendar events.
    
    1. Get events
    2. Apply filters
    3. Generate report
    v1
    """
    console.print("\n[cyan]Analyzing Calendar Events[/cyan]")
    console.print("=" * 50)
    
    # Get events
    console.print("\nFetching upcoming events...")
    raw_events = service.get_upcoming_events(weeks=weeks_ahead)
    
    if not raw_events:
        console.print("[yellow]No upcoming events found.[/yellow]")
        return []
    
    # Process events
    console.print(f"\nProcessing {len(raw_events)} events...")
    events = [Event.from_gcal_event(event) for event in raw_events]
    
    # Apply filters if requested
    if show_filters:
        filter_manager = create_default_filters()
        for event in events:
            event.filter_matches = filter_manager.get_filter_matches(event)
    
    # Display results
    display_events(events)
    
    return events


def main():
    """Main entry point.
    
    1. Run analysis
    2. Show report
    3. Handle user input
    v1
    """
    try:
        events = analyze_calendar(weeks_ahead=3)
        
        if events:
            console.print("\n[cyan]Actions:[/cyan]")
            console.print("1. View event details")
            console.print("2. Filter events")
            console.print("3. Exit")
            
            choice = input("\nEnter choice (1-3): ")
            if choice == '1':
                console.print("\n[yellow]Event details not implemented yet[/yellow]")
            elif choice == '2':
                console.print("\n[yellow]Event filtering not implemented yet[/yellow]")
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")


if __name__ == '__main__':
    main() 