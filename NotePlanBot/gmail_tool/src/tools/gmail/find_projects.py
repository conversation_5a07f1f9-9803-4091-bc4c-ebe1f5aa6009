"""Find projects in emails and draft context files.

1. Analyze emails for project mentions
2. Extract project information
3. Draft context updates
v1
"""
import asyncio
import os
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from openai import AsyncOpenAI
from dotenv import load_dotenv
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from .service import GmailService
from .storage.project_store import ProjectContextStore, ProjectInfo, RACIEntry, DocumentContext
from .ui_viewthreads import clean_content


load_dotenv()
console = Console()


@dataclass
class ProjectMention:
    """Project mention found in email.
    
    1. Project details
    2. Context from email
    3. Confidence score
    v1
    """
    project_name: str
    thread_id: str
    message_id: str
    sender: str
    date: str
    context: str
    confidence: float
    suggested_role: Optional[str] = None
    suggested_priority: Optional[int] = None


class ProjectFinder:
    """Find and extract project information from emails.
    
    1. Scan emails for projects
    2. Extract context
    3. Draft updates
    v1
    """
    
    def __init__(self):
        """Initialize finder.
        
        1. Set up services
        2. Load existing projects
        v1
        """
        self.gmail = GmailService()
        self.store = ProjectContextStore()
        self.client = AsyncOpenAI()
        
    async def analyze_email_for_projects(self, message: Dict, thread_messages: List[Dict]) -> List[ProjectMention]:
        """Analyze email for project mentions.
        
        1. Extract content
        2. Use LLM to find projects
        3. Return mentions
        v1
        """
        # Get clean content
        content = clean_content(message['content'])
        if not content or len(content) < 50:
            return []
        
        # Build thread context
        thread_context = []
        for msg in thread_messages[-5:]:  # Last 5 messages
            sender = msg['from'].split('<')[0].strip()
            msg_content = clean_content(msg['content'])[:200]
            thread_context.append(f"{sender}: {msg_content}")
        
        prompt = f"""Analyze this email for project mentions. Extract any projects discussed.

Email from: {message['from']}
Subject: {message['subject']}
Date: {message['date']}

Content:
{content}

Thread Context:
{chr(10).join(thread_context)}

Existing Projects: {', '.join(self.store.projects.keys()) if self.store.projects else 'None'}

For each project mentioned:
1. Project name (use existing name if matches, otherwise suggest new name)
2. Confidence (0-1) that this is a real project
3. Sender's likely RACI role (R/A/C/I)
4. Suggested priority (1-5)
5. Key context about the project from this email

Format as:
PROJECT: <name>
CONFIDENCE: <0-1>
ROLE: <R/A/C/I>
PRIORITY: <1-5>
CONTEXT: <brief context>
---

Only include projects with confidence > 0.5."""

        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=500
            )
            
            # Parse response
            mentions = []
            content = response.choices[0].message.content
            
            for section in content.split('---'):
                if 'PROJECT:' not in section:
                    continue
                
                lines = section.strip().split('\n')
                project_data = {}
                
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        project_data[key.strip()] = value.strip()
                
                if 'PROJECT' in project_data and 'CONFIDENCE' in project_data:
                    mention = ProjectMention(
                        project_name=project_data['PROJECT'],
                        thread_id=message['thread_id'],
                        message_id=message['id'],
                        sender=message['from'],
                        date=message['date'],
                        context=project_data.get('CONTEXT', ''),
                        confidence=float(project_data['CONFIDENCE']),
                        suggested_role=project_data.get('ROLE'),
                        suggested_priority=int(project_data.get('PRIORITY', 3))
                    )
                    mentions.append(mention)
            
            return mentions
            
        except Exception as e:
            console.print(f"[red]Error analyzing email: {e}[/red]")
            return []
    
    async def scan_recent_emails(self, days_back: int = 7, max_emails: int = 50) -> List[ProjectMention]:
        """Scan recent emails for projects.
        
        1. Get recent emails
        2. Analyze each
        3. Return all mentions
        v1
        """
        mentions = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Scanning emails...", total=None)
            
            # Get recent messages
            messages = self.gmail.list_unread_messages(
                max_results=max_emails,
                days_back=days_back
            )
            
            if not messages:
                return mentions
            
            # Group by thread for context
            thread_groups = {}
            for msg in messages:
                thread_id = msg['thread_id']
                if thread_id not in thread_groups:
                    thread_groups[thread_id] = []
                thread_groups[thread_id].append(msg)
            
            # Analyze each thread
            tasks = []
            for thread_id, thread_messages in thread_groups.items():
                # Get full thread context
                full_thread = self.gmail.get_thread_messages(thread_id)
                
                # Analyze each unread message in thread
                for msg in thread_messages:
                    tasks.append(self.analyze_email_for_projects(msg, full_thread))
            
            # Run all analyses in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, list):
                    mentions.extend(result)
        
        return mentions
    
    def draft_project_updates(self, mentions: List[ProjectMention]) -> Dict[str, Dict]:
        """Draft updates for project context.
        
        1. Group by project
        2. Generate updates
        3. Return drafts
        v1
        """
        # Group mentions by project
        project_mentions = {}
        for mention in mentions:
            if mention.project_name not in project_mentions:
                project_mentions[mention.project_name] = []
            project_mentions[mention.project_name].append(mention)
        
        drafts = {}
        
        for project_name, project_mentions_list in project_mentions.items():
            # Check if project exists
            existing = self.store.projects.get(project_name)
            
            # Aggregate information
            priorities = [m.suggested_priority for m in project_mentions_list if m.suggested_priority]
            avg_priority = sum(priorities) // len(priorities) if priorities else 3
            
            # Extract unique people
            people = {}
            for mention in project_mentions_list:
                email = mention.sender.split('<')[-1].strip('>') if '<' in mention.sender else mention.sender
                name = mention.sender.split('<')[0].strip()
                if email and mention.suggested_role:
                    people[email] = (name, mention.suggested_role)
            
            # Create context summary
            contexts = [m.context for m in project_mentions_list if m.context]
            summary = " | ".join(contexts[:3])  # Top 3 contexts
            
            if existing:
                # Draft update
                draft = {
                    'action': 'update',
                    'project': existing,
                    'new_people': people,
                    'new_context': summary,
                    'mentions_count': len(project_mentions_list)
                }
            else:
                # Draft new project
                draft = {
                    'action': 'create',
                    'project': ProjectInfo(
                        name=project_name,
                        description=summary[:200],
                        priority=avg_priority,
                        status='active',
                        start_date=datetime.now().strftime('%Y-%m-%d'),
                        end_date=None,
                        key_stakeholders=list(people.keys()),
                        primary_contact=list(people.keys())[0] if people else '',
                        tags=[]
                    ),
                    'people': people,
                    'mentions_count': len(project_mentions_list)
                }
            
            drafts[project_name] = draft
        
        return drafts
    
    def display_drafts(self, drafts: Dict[str, Dict]) -> None:
        """Display draft updates.
        
        1. Show each draft
        2. Format nicely
        v1
        """
        if not drafts:
            console.print("[yellow]No project updates found.[/yellow]")
            return
        
        console.print(f"\n[cyan]Found {len(drafts)} project(s) to update:[/cyan]\n")
        
        for project_name, draft in drafts.items():
            action = draft['action']
            
            if action == 'create':
                console.print(f"[green]NEW PROJECT:[/green] {project_name}")
                project = draft['project']
                console.print(f"  Description: {project.description}")
                console.print(f"  Priority: {project.priority}/5")
                console.print(f"  People: {', '.join(draft['people'].keys())}")
            else:
                console.print(f"[yellow]UPDATE PROJECT:[/yellow] {project_name}")
                console.print(f"  New people: {', '.join(draft['new_people'].keys())}")
                console.print(f"  New context: {draft['new_context'][:100]}...")
            
            console.print(f"  Based on: {draft['mentions_count']} email mentions\n")


async def main():
    """Main entry point.
    
    1. Create finder
    2. Scan emails
    3. Display results
    v1
    """
    finder = ProjectFinder()
    
    console.print("[cyan]Scanning recent emails for project information...[/cyan]\n")
    
    # Scan emails
    mentions = await finder.scan_recent_emails(days_back=7, max_emails=30)
    
    if not mentions:
        console.print("[yellow]No project mentions found in recent emails.[/yellow]")
        return
    
    console.print(f"\n[green]Found {len(mentions)} project mentions[/green]\n")
    
    # Draft updates
    drafts = finder.draft_project_updates(mentions)
    finder.display_drafts(drafts)
    
    # TODO: Add interactive approval and saving


if __name__ == '__main__':
    asyncio.run(main())