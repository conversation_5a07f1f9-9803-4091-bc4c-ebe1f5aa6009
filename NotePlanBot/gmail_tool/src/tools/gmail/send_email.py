"""Gmail API tool for sending and reading emails.

1. Authenticate using existing creds
2. Create and send email
3. Read and process emails
v2
"""
import os
import base64
from email.mime.text import MIMEText
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
import pickle
from datetime import datetime


SCOPES = [
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.readonly'
]
TOKEN_PATH = 'gmail_token.pickle'
CREDS_PATH = 'creds.json'


def get_gmail_service():
    """Get authenticated Gmail service.
    
    1. Check for existing token
    2. If no valid token, create new one
    3. Build and return service
    v1
    """
    creds = None
    
    # 1. Check for existing token
    if os.path.exists(TOKEN_PATH):
        with open(TOKEN_PATH, 'rb') as token:
            creds = pickle.load(token)
    
    # 2. If no valid token, create new one
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CREDS_PATH, SCOPES)
            creds = flow.run_local_server(port=0)
        
        # Save credentials for future use
        with open(TOKEN_PATH, 'wb') as token:
            pickle.dump(creds, token)
    
    # 3. Build and return service
    return build('gmail', 'v1', credentials=creds)


def send_test_email():
    """Send a test email using Gmail API.
    
    1. Create service
    2. Create message
    3. Send and handle response
    v1
    """
    try:
        service = get_gmail_service()
        
        # Create message
        message = MIMEText("This is a test email from the Gmail API integration!")
        message['to'] = "<EMAIL>"
        message['subject'] = "Test Email from Gmail API"
        
        # Encode the message
        raw = base64.urlsafe_b64encode(message.as_bytes())
        raw = raw.decode()
        body = {'raw': raw}
        
        # Send message
        sent_message = service.users().messages().send(
            userId='me',
            body=body
        ).execute()
        
        print(f"Message Id: {sent_message['id']}")
        print("Email sent successfully!")
        return True
        
    except Exception as e:
        print(f"An error occurred: {e}")
        return False


def check_unread_emails():
    """Check last 10 unread emails.
    
    1. Get Gmail service
    2. Search for unread messages
    3. Get and display message details
    v1
    """
    try:
        service = get_gmail_service()
        
        # Search for unread messages
        results = service.users().messages().list(
            userId='me',
            labelIds=['UNREAD'],
            maxResults=10
        ).execute()
        
        messages = results.get('messages', [])
        
        if not messages:
            print("No unread messages found.")
            return
        
        print("\nLast 10 Unread Emails:")
        print("-" * 50)
        
        for msg in messages:
            # Get message details
            message = service.users().messages().get(
                userId='me',
                id=msg['id'],
                format='metadata',
                metadataHeaders=['From', 'Subject', 'Date']
            ).execute()
            
            headers = message['payload']['headers']
            subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
            sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown')
            date_str = next((h['value'] for h in headers if h['name'] == 'Date'), '')
            
            # Format the output
            print(f"\nMessage ID: {msg['id']}")
            print(f"From: {sender}")
            print(f"Subject: {subject}")
            print(f"Date: {date_str}")
            print("-" * 50)
            
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == '__main__':
    # First check unread emails
    print("Checking unread emails...")
    check_unread_emails()
    
    # Ask if user wants to send a test email
    response = input("\nWould you like to send a test email? (y/n): ")
    if response.lower() == 'y':
        send_test_email() 