"""View Gmail threads by label with customizable filters.

1. Get threads by label
2. Format and display
3. Support multiple views
v7
"""
from datetime import datetime
from typing import Dict, List, Optional, Set
import re
from rich.console import Console
from rich.text import Text
from talon import quotations
from src.tools.gmail.service import gmail_service
from src.tools.gmail.ui_viewthreads import (
    clean_content,
    format_date,
    format_content,
    find_names,
    is_user_name,
    get_thread_stats,
    USER_NAMES
)
from src.tools.gmail.message_types import MessageClassifier, MessageType


# Initialize rich console
console = Console()


def get_threads_by_label(
    label: str,
    max_results: int = 5,
    days_back: int = 7,
    min_messages: int = 1,
    exclude_self_only: bool = True
) -> List[Dict]:
    """Get threads with specific label.
    
    1. Fetch messages with label
    2. Group into threads
    3. Sort by date
    v2
    
    Args:
        label: Gmail label ID to filter by
        max_results: Maximum number of threads to return
        days_back: Only get messages from last N days
        min_messages: Minimum messages per thread
        exclude_self_only: Skip threads that only contain messages from you
    """
    # Get messages with label
    messages = gmail_service.list_unread_messages(
        max_results=max_results * 2,  # Get extra to account for filtering
        days_back=days_back,
        label_filter=[label]
    )
    
    if not messages:
        return []
    
    # Group by thread
    threads = {}
    for msg in messages:
        thread_id = msg['thread_id']
        if thread_id not in threads:
            threads[thread_id] = {
                'messages': None,
                'subject': msg['subject'],
                'latest_date': msg['date'],
                'labels': msg.get('labels', [])
            }
    
    # Convert to list and sort by date
    thread_list = [
        {'id': tid, 'data': tdata}
        for tid, tdata in threads.items()
    ]
    
    thread_list.sort(
        key=lambda x: x['data']['latest_date'],
        reverse=True
    )
    
    # Load content for top threads
    loaded_threads = []
    for thread in thread_list[:max_results * 2]:  # Load extra to account for filtering
        # Load thread content
        thread['data']['messages'] = gmail_service.get_thread_messages(thread['id'])
        messages = thread['data']['messages']
        
        # Skip if not enough messages
        if len(messages) < min_messages:
            continue
            
        # Skip if all messages are from self
        if exclude_self_only:
            senders = {msg['from'].split('<')[0].strip(' "\'') for msg in messages}
            user_variants = {
                'Alex Foster',
                '<EMAIL>',
                'Alex',
                'Alexander Foster'
            }
            if all(any(variant in sender for variant in user_variants) for sender in senders):
                console.print(f"[dim]Skipping self-only thread: {thread['data']['subject']}[/dim]")
                continue
        
        loaded_threads.append(thread)
        if len(loaded_threads) >= max_results:
            break
    
    return loaded_threads


def format_message_content(msg: Dict, participants: Set[str], is_from_user: bool) -> str:
    """Format message content based on type.
    
    1. Classify message
    2. Format appropriately
    3. Return formatted text
    v1
    """
    # Classify message
    classified = MessageClassifier.classify(
        msg.get('content', ''),
        msg.get('subject', '')
    )
    
    if classified.type == MessageType.CALENDAR_EVENT:
        return classified.summary
    
    if classified.type in (MessageType.GOOGLE_DOC_COMMENT, MessageType.GOOGLE_SHEET_COMMENT):
        if classified.details and 'matches' in classified.details:
            # Format document title and comments
            lines = [f"Activity in: {classified.title}"]
            for match in classified.details['matches']:
                if len(match) >= 2:  # Username and comment
                    lines.append(f"  • {match[0].strip()}: {match[1].strip()}")
            return "\n".join(lines)
        return classified.summary
    
    if classified.type == MessageType.SLACK_NOTIFICATION:
        return classified.summary
    
    # Regular email - use existing cleaning
    content = clean_content(msg.get('content', ''))
    if content and content != "(empty message)":
        return format_content(
            content,
            participants,
            is_from_user
        )
    
    return "(no content)"


def group_threads_by_type(threads: List[Dict]) -> Dict[MessageType, List[Dict]]:
    """Group threads by primary message type.
    
    1. Classify each thread
    2. Group by type
    3. Sort within groups
    v1
    """
    groups = {}
    for thread in threads:
        # Use first message to determine type
        first_msg = thread['data']['messages'][0] if thread['data']['messages'] else None
        if first_msg:
            msg_type = MessageClassifier.classify(
                first_msg.get('content', ''),
                thread['data']['subject']
            ).type
        else:
            msg_type = MessageType.REGULAR_EMAIL
        
        if msg_type not in groups:
            groups[msg_type] = []
        groups[msg_type].append(thread)
    
    # Sort threads within each group
    for msg_type in groups:
        groups[msg_type].sort(
            key=lambda t: t['data']['latest_date'],
            reverse=True
        )
    
    return groups


def display_thread(thread: Dict, index: int = 0) -> None:
    """Display a single thread.
    
    1. Show header info
    2. Show messages
    3. Format nicely
    v8
    """
    console.print(f"\n[yellow]{'=' * 80}[/yellow]")
    console.print(f"[green]Thread {index + 1}:[/green]")
    console.print(f"[blue]Subject:[/blue] {thread['data']['subject']}")
    
    # Show labels if present
    if thread['data']['labels']:
        console.print(f"[blue]Labels:[/blue] {', '.join(thread['data']['labels'])}")
    
    messages = thread['data']['messages']
    stats = get_thread_stats(messages)
    
    if not stats:
        console.print("[yellow]No message data available[/yellow]")
        return
    
    # Show thread stats
    console.print(
        f"[blue]Stats:[/blue] "
        f"{stats['message_count']} messages over {stats['duration_days']:.1f} days "
        f"from {stats['participants']} participants"
    )
    
    # Format participants on one line
    parts = []
    for name, count in stats['participant_list']:
        style = "bold magenta" if is_user_name(name) else "bold blue"
        parts.append(f"[{style}]{name}[/] ({count})")
    console.print(f"[blue]Participants:[/blue] " + ", ".join(parts))
    
    # Show messages
    console.print("\n[blue]Messages:[/blue]")
    for msg in messages:
        date = format_date(msg['date'])
        from_name = msg['from'].split('<')[0].strip(' "\'')
        if ' via ' in from_name:
            from_name = from_name.split(' via ')[0]
        
        # Format the message
        is_from_user = is_user_name(from_name)
        name_style = "bold magenta" if is_from_user else "bold blue"
        
        # Add labels if present
        labels = msg.get('labels', [])
        label_str = f" [[dim]{', '.join(labels)}[/]]" if labels else ""
        
        # Print the message header
        console.print(
            f"[dim]{date}[/] [{name_style}]{from_name}:[/]{label_str}"
        )
        
        # Format and print content
        content = format_message_content(
            msg,
            {name for name, _ in stats['participant_list']},
            is_from_user
        )
        
        # Print each line with proper indentation
        if content:
            if isinstance(content, Text):
                # Create a new text object with indentation
                indented = Text("  ")
                indented.append(content)
                console.print(indented)
            else:
                for line in str(content).split('\n'):
                    if line.strip():
                        console.print(f"  {line.rstrip()}")
        else:
            console.print("  [dim](no content)[/]")
        
        # Add spacing between messages
        console.print()
    
    console.print(f"[yellow]{'=' * 80}[/yellow]")


def view_important_and_inbox(
    threads_per_label: int = 5,
    days_back: int = 7,
    min_messages: int = 1,
    exclude_self_only: bool = True,
    show_inbox: bool = True
):
    """View important and inbox threads.
    
    1. Show important threads
    2. Show inbox threads (optional)
    3. Format nicely
    v5
    """
    if threads_per_label > 0:
        console.print(f"\n[cyan]Important Threads[/cyan]")
        important_threads = get_threads_by_label(
            'IMPORTANT',
            max_results=threads_per_label,
            days_back=days_back,
            min_messages=min_messages,
            exclude_self_only=exclude_self_only
        )
        
        if important_threads:
            # Group threads by type
            grouped = group_threads_by_type(important_threads)
            
            # Display each group
            for msg_type in MessageType:
                if msg_type in grouped:
                    threads = grouped[msg_type]
                    if threads:
                        console.print(f"\n[magenta]{msg_type.name} Messages:[/magenta]")
                        for i, thread in enumerate(threads):
                            display_thread(thread, i)
        else:
            console.print("[yellow]No important threads found[/yellow]")
    
    if show_inbox and threads_per_label > 0:
        console.print(f"\n[cyan]Inbox Threads[/cyan]")
        inbox_threads = get_threads_by_label(
            'INBOX',
            max_results=threads_per_label,
            days_back=days_back,
            min_messages=min_messages,
            exclude_self_only=exclude_self_only
        )
        
        if inbox_threads:
            # Group threads by type
            grouped = group_threads_by_type(inbox_threads)
            
            # Display each group
            for msg_type in MessageType:
                if msg_type in grouped:
                    threads = grouped[msg_type]
                    if threads:
                        console.print(f"\n[magenta]{msg_type.name} Messages:[/magenta]")
                        for i, thread in enumerate(threads):
                            display_thread(thread, i)
        else:
            console.print("[yellow]No inbox threads found[/yellow]")


if __name__ == '__main__':
    view_important_and_inbox(
        threads_per_label=50,  # Show up to 50 threads
        days_back=7,
        min_messages=1,
        exclude_self_only=True,
        show_inbox=False  # Only show important threads
    ) 