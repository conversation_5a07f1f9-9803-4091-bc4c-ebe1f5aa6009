"""Manage email replies based on context and rules.

1. Analyze messages
2. Generate replies
3. Track responses
v2
"""
from datetime import datetime
from typing import Dict, List, Optional
from tools.gmail.message_types import MessageType, RecipientType, MessageMetadata
from tools.gmail.storage.alex_context import alex_context
from tools.gmail.storage.context import context_manager
from src.tools.gmail.service import gmail_service


def analyze_messages(messages: List[Dict], days_back: int = 7) -> List[Dict]:
    """Analyze messages and prepare reply suggestions.
    
    1. Group by type
    2. Apply context
    3. Generate drafts
    v2
    """
    results = []
    cutoff = datetime.now().timestamp() - (days_back * 24 * 60 * 60)
    
    # Group messages by type
    for msg in messages:
        # Get full message details
        full_msg = gmail_service.get_message(msg['id'])
        if not full_msg:
            continue
        
        # Extract timestamp from headers
        headers = {h['name'].lower(): h['value'] for h in full_msg.get('payload', {}).get('headers', [])}
        date_str = headers.get('date', '')
        try:
            msg_time = datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %z')
        except ValueError:
            # Skip messages with invalid dates
            continue
        
        # Skip old messages
        if msg_time.timestamp() < cutoff:
            continue
        
        # Create metadata
        metadata = MessageMetadata(
            message_id=msg['id'],
            thread_id=msg.get('threadId', ''),
            timestamp=msg_time,
            recipient_type=determine_recipient_type(headers, alex_context.email),
            sender=headers.get('from', ''),
            project_name=detect_project(headers)
        )
        
        # Get thread context
        thread = context_manager.get_thread_context(metadata.thread_id)
        
        # Analyze based on type
        msg_type = detect_message_type(headers)
        if msg_type == MessageType.DOC_COMMENT:
            analysis = analyze_doc_comment(full_msg, metadata, thread)
        elif msg_type == MessageType.CALENDAR:
            analysis = analyze_calendar_event(full_msg, metadata, thread)
        else:
            analysis = analyze_email(full_msg, metadata, thread)
        
        results.append(analysis)
    
    return results


def determine_recipient_type(headers: Dict[str, str], user_email: str) -> RecipientType:
    """Determine how the user was addressed.
    
    1. Check TO field
    2. Check CC field
    3. Default to NONE
    v2
    """
    to_field = headers.get('to', '').lower()
    cc_field = headers.get('cc', '').lower()
    user_email = user_email.lower()
    
    if user_email in to_field:
        return RecipientType.TO
    if user_email in cc_field:
        return RecipientType.CC
    return RecipientType.NONE


def detect_project(headers: Dict[str, str]) -> Optional[str]:
    """Detect which project a message belongs to.
    
    1. Check subject
    2. Check content
    3. Check sender
    v2
    """
    subject = headers.get('subject', '').lower()
    from_field = headers.get('from', '').lower()
    content = f"{subject} {from_field}"
    
    for project_name in alex_context.projects:
        if project_name.lower() in content:
            return project_name
    
    return None


def detect_message_type(headers: Dict[str, str]) -> MessageType:
    """Detect the type of message.
    
    1. Check headers
    2. Check content
    3. Default to email
    v2
    """
    subject = headers.get('subject', '').lower()
    
    if any(x in subject for x in ['accepted:', 'declined:', 'tentative:', 'updated:']):
        return MessageType.CALENDAR
    
    if any(x in subject for x in ['new activity in', 'replied to a comment']):
        return MessageType.DOC_COMMENT
    
    return MessageType.EMAIL


def analyze_doc_comment(message: Dict, metadata: MessageMetadata, thread: Dict) -> Dict:
    """Analyze a doc comment.
    
    1. Extract comment data
    2. Check context
    3. Generate draft
    v1
    """
    # TODO: Implement doc comment analysis
    return {
        'type': MessageType.DOC_COMMENT,
        'metadata': metadata,
        'needs_reply': False,
        'draft': None,
        'reason': 'Doc comment analysis not implemented'
    }


def analyze_calendar_event(message: Dict, metadata: MessageMetadata, thread: Dict) -> Dict:
    """Analyze a calendar event.
    
    1. Extract event data
    2. Check context
    3. Generate draft
    v1
    """
    # Calendar events usually don't need replies
    return {
        'type': MessageType.CALENDAR,
        'metadata': metadata,
        'needs_reply': False,
        'draft': None,
        'reason': 'Calendar events do not need replies'
    }


def analyze_email(message: Dict, metadata: MessageMetadata, thread: Dict) -> Dict:
    """Analyze a regular email.
    
    1. Extract email data
    2. Check context
    3. Generate draft
    v1
    """
    # TODO: Implement email analysis
    return {
        'type': MessageType.EMAIL,
        'metadata': metadata,
        'needs_reply': True,
        'draft': 'Placeholder draft',
        'reason': 'Email analysis not implemented'
    }


def format_analysis_report(analyses: List[Dict]) -> str:
    """Format analysis results for display.
    
    1. Group by type
    2. Sort by priority
    3. Format nicely
    v1
    """
    if not analyses:
        return "No messages to analyze"
    
    # Group by type
    by_type = {}
    for analysis in analyses:
        msg_type = analysis['type']
        if msg_type not in by_type:
            by_type[msg_type] = []
        by_type[msg_type].append(analysis)
    
    # Format report
    lines = []
    for msg_type, items in by_type.items():
        lines.append(f"\n{msg_type.value.upper()} MESSAGES:")
        
        for item in sorted(items, key=lambda x: x['metadata'].timestamp, reverse=True):
            meta = item['metadata']
            lines.append(
                f"\n[{meta.days_ago}d ago] "
                f"{'TO' if meta.recipient_type == RecipientType.TO else 'CC' if meta.recipient_type == RecipientType.CC else '  '} "
                f"From: {meta.sender}"
            )
            if item.get('needs_reply'):
                lines.append(f"  → Reply needed: {item.get('reason', 'No reason given')}")
                if item.get('draft'):
                    lines.append(f"  Draft: {item['draft']}")
    
    return "\n".join(lines)


def test_reply_manager():
    """Test the reply manager.
    
    1. Get messages
    2. Analyze them
    3. Show report
    v2
    """
    # Get test messages
    print("\nFetching recent messages...")
    messages = gmail_service.list_unread_messages(max_results=10)
    
    # Analyze messages
    print(f"\nAnalyzing {len(messages)} messages...")
    analyses = analyze_messages(messages)
    
    # Show report
    print("\nREPLY ANALYSIS REPORT")
    print("=" * 50)
    print(format_analysis_report(analyses))


if __name__ == '__main__':
    test_reply_manager() 