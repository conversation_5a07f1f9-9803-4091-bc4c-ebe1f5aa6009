"""Test the email reply system.

1. Process unread emails
2. Show drafts
3. Test reply sending
v1
"""
from .handlers.replies import process_and_reply


def test_reply_system():
    """Test the email reply system.
    
    1. Get unread emails
    2. Generate drafts
    3. Show results
    v1
    """
    print("\nProcessing unread emails...")
    results = process_and_reply(max_emails=5, auto_send=False)
    
    if not results:
        print("No unread emails found.")
        return
    
    print(f"\nProcessed {len(results)} emails:")
    for i, result in enumerate(results, 1):
        msg = result['message']
        draft = result['draft']
        context = result['context']
        
        print(f"\n{'-' * 60}")
        print(f"Email {i}:")
        print(f"From: {msg['from']}")
        print(f"Subject: {msg['subject']}")
        print(f"Date: {msg['date']}")
        print(f"\nImportance: {context.importance}/5")
        print(f"Tags: {', '.join(context.tags)}")
        print(f"\nSummary: {context.summary}")
        print(f"\nDraft Reply:\n{draft}")
        print(f"{'-' * 60}")
        
        if i < len(results):
            input("\nPress Enter for next email...")


if __name__ == '__main__':
    test_reply_system() 