"""Store Alex's context and preferences for email handling.

1. Role information
2. Project responsibilities
3. Communication preferences
v2
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from enum import Enum


class RACI(Enum):
    """RACI responsibility matrix roles.
    
    1. Responsible: Does the work
    2. Accountable: Delegates and reviews
    3. Consulted: Provides input
    4. Informed: Kept updated
    v2
    """
    RESPONSIBLE = "R"
    ACCOUNTABLE = "A"
    CONSULTED = "C"
    INFORMED = "I"


@dataclass
class DocumentContext:
    """Context from project documentation.
    
    1. Store document path
    2. Track summaries
    3. Handle versioning
    v1
    """
    filepath: str
    llm_summary: str
    last_updated: str  # ISO format date
    doc_type: str  # 'design', 'spec', 'notes', etc.
    version: Optional[str] = None


@dataclass
class ProjectContext:
    """Project-specific context.
    
    1. Store RACI roles
    2. Track key stakeholders
    3. Define communication preferences
    v2
    """
    name: str
    raci_matrix: Dict[RACI, List[str]]  # {RACI.RESPONSIBLE: ['<EMAIL>', '<EMAIL>']}
    key_stakeholders: List[str]
    priority: int  # 1-5, 5 being highest
    auto_reply_enabled: bool = False
    doc_contexts: List[DocumentContext] = field(default_factory=list)
    
    def __post_init__(self):
        """Ensure RACI matrix has all roles."""
        if not self.raci_matrix:
            self.raci_matrix = {role: [] for role in RACI}
        else:
            # Fill in any missing roles with empty lists
            for role in RACI:
                if role not in self.raci_matrix:
                    self.raci_matrix[role] = []
    
    def get_role_members(self, role: RACI) -> List[str]:
        """Get all members with a specific RACI role."""
        return self.raci_matrix.get(role, [])
    
    def get_all_roles_for_member(self, email: str) -> List[RACI]:
        """Get all RACI roles for a team member."""
        return [role for role, members in self.raci_matrix.items() if email in members]
    
    # TODO: Add more fields as needed:
    # - Meeting preferences
    # - Preferred communication channels
    # - Escalation paths
    # - Working hours/timezone


@dataclass
class AlexContext:
    """Alex's work context and preferences.
    
    1. Store personal info
    2. Track project roles
    3. Define global preferences
    v2
    """
    email: str = "<EMAIL>"
    name: str = "Alex Foster"
    role: str = "Technical Lead"  # TODO: Update with actual role
    timezone: str = "GMT"
    
    # Project contexts
    projects: Dict[str, ProjectContext] = None
    
    # TODO: Add more fields as needed:
    # - Team structure
    # - Direct reports
    # - Regular meetings
    # - OOO calendar
    # - Response time SLAs
    
    def __post_init__(self):
        """Initialize with placeholder project data."""
        if self.projects is None:
            self.projects = {
                "Safety": ProjectContext(
                    name="Safety",
                    raci_matrix={
                        RACI.ACCOUNTABLE: [self.email],
                        RACI.RESPONSIBLE: ["<EMAIL>"],
                        RACI.CONSULTED: ["<EMAIL>", "<EMAIL>"],
                        RACI.INFORMED: ["<EMAIL>"]
                    },
                    key_stakeholders=["<EMAIL>"],
                    priority=5,
                    doc_contexts=[
                        DocumentContext(
                            filepath="docs/safety/design.md",
                            llm_summary="Safety system design focusing on LLM guardrails and monitoring",
                            last_updated="2024-01-15",
                            doc_type="design"
                        ),
                        DocumentContext(
                            filepath="docs/safety/spec.md",
                            llm_summary="Technical specifications for safety implementations",
                            last_updated="2024-01-20",
                            doc_type="spec"
                        )
                    ]
                ),
                "xAI": ProjectContext(
                    name="xAI",
                    raci_matrix={
                        RACI.CONSULTED: [self.email, "<EMAIL>"],
                        RACI.RESPONSIBLE: ["<EMAIL>"],
                        RACI.ACCOUNTABLE: ["<EMAIL>"],
                        RACI.INFORMED: ["<EMAIL>"]
                    },
                    key_stakeholders=["<EMAIL>"],
                    priority=4,
                    doc_contexts=[
                        DocumentContext(
                            filepath="docs/xai/architecture.md",
                            llm_summary="System architecture for explainable AI components",
                            last_updated="2024-01-10",
                            doc_type="design"
                        )
                    ]
                ),
                # TODO: Add more projects
            }
    
    def get_project_context(self, project_name: str) -> Optional[ProjectContext]:
        """Get context for a specific project."""
        return self.projects.get(project_name)
    
    def should_auto_reply(self, project_name: str) -> bool:
        """Check if auto-reply is enabled for project."""
        project = self.get_project_context(project_name)
        return project and project.auto_reply_enabled
    
    def get_all_project_roles(self) -> Dict[str, List[RACI]]:
        """Get all RACI roles across all projects."""
        roles = {}
        for project in self.projects.values():
            roles[project.name] = project.get_all_roles_for_member(self.email)
        return roles


# Global instance
alex_context = AlexContext() 