"""Email context storage and management.

1. Store email context and insights
2. Manage context relevance
3. <PERSON><PERSON> pruning and updates
v1
"""
import json
import os
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict


@dataclass
class EmailContext:
    """Store context for an email conversation.
    
    1. Basic metadata
    2. AI-generated insights
    3. Relevance tracking
    v1
    """
    message_id: str
    thread_id: str
    summary: str
    importance: int  # 1-5
    tags: List[str]
    references: List[str]  # Related context IDs
    last_accessed: str  # ISO format datetime
    insights: Dict  # AI-generated insights
    metadata: Dict  # Original email metadata


class ContextManager:
    """Manage email conversation context.
    
    1. Store and retrieve context
    2. Update relevance scores
    3. Prune old/irrelevant data
    v1
    """
    
    def __init__(self, storage_dir: str = "src/tools/gmail/storage/data"):
        """Initialize context manager.
        
        1. Set up storage
        2. Load existing data
        v1
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
    
    def store_context(self, context: EmailContext) -> bool:
        """Store email context.
        
        1. Format context data
        2. Write to storage
        3. Update indices
        v1
        """
        try:
            # Create filename from message ID
            filename = os.path.join(
                self.storage_dir,
                f"{context.message_id}.json"
            )
            
            # Update last accessed
            context.last_accessed = datetime.utcnow().isoformat()
            
            # Write context to file
            with open(filename, 'w') as f:
                json.dump(asdict(context), f, indent=2)
            
            return True
            
        except Exception as e:
            print(f"Error storing context: {e}")
            return False
    
    def get_context(self, message_id: str) -> Optional[EmailContext]:
        """Retrieve email context.
        
        1. Load from storage
        2. Update access time
        3. Return context
        v1
        """
        try:
            filename = os.path.join(
                self.storage_dir,
                f"{message_id}.json"
            )
            
            if not os.path.exists(filename):
                return None
            
            with open(filename, 'r') as f:
                data = json.load(f)
            
            # Update last accessed
            context = EmailContext(**data)
            context.last_accessed = datetime.utcnow().isoformat()
            self.store_context(context)
            
            return context
            
        except Exception as e:
            print(f"Error retrieving context: {e}")
            return None
    
    def get_thread_context(self, thread_id: str) -> List[EmailContext]:
        """Get all context for a thread.
        
        1. Find all related contexts
        2. Sort by relevance
        3. Return list
        v1
        """
        contexts = []
        
        try:
            # Scan all files in storage
            for filename in os.listdir(self.storage_dir):
                if not filename.endswith('.json'):
                    continue
                
                filepath = os.path.join(self.storage_dir, filename)
                with open(filepath, 'r') as f:
                    data = json.load(f)
                    
                if data['thread_id'] == thread_id:
                    contexts.append(EmailContext(**data))
            
            # Sort by importance and last accessed
            contexts.sort(
                key=lambda x: (x.importance, x.last_accessed),
                reverse=True
            )
            
            return contexts
            
        except Exception as e:
            print(f"Error retrieving thread context: {e}")
            return contexts
    
    def prune_old_contexts(self, days_threshold: int = 30) -> int:
        """Remove old context files.
        
        1. Find old contexts
        2. Remove if unimportant
        3. Return count removed
        v1
        """
        removed = 0
        threshold = datetime.utcnow().timestamp() - (days_threshold * 86400)
        
        try:
            for filename in os.listdir(self.storage_dir):
                if not filename.endswith('.json'):
                    continue
                
                filepath = os.path.join(self.storage_dir, filename)
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                # Convert ISO format to timestamp
                last_accessed = datetime.fromisoformat(
                    data['last_accessed']
                ).timestamp()
                
                # Remove if old and unimportant
                if last_accessed < threshold and data['importance'] <= 2:
                    os.remove(filepath)
                    removed += 1
            
            return removed
            
        except Exception as e:
            print(f"Error pruning contexts: {e}")
            return removed


# Global instance for easy access
context_manager = ContextManager() 