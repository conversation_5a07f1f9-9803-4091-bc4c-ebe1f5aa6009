"""Gmail API service operations.

1. Core email operations
2. Message formatting
3. Label management
v5
"""
import base64
import html
import os
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Optional
from .gmail_auth import gmail_auth


# Gmail system label IDs:
# - INBOX: Regular inbox
# - CATEGORY_PERSONAL: Personal category
# - CATEGORY_SOCIAL: Social networks, dating
# - CATEGORY_PROMOTIONS: Marketing, deals
# - CATEGORY_UPDATES: Bills, receipts, confirmations
# - CATEGORY_FORUMS: Groups, mailing lists
# - IMPORTANT: Gmail's important marker
# - STARRED: Starred items
# - DRAFT: Email drafts
# - SENT: Sent emails
# - SPAM: Spam/junk folder
# - TRASH: Deleted items
# Can be a list of multiple labels, e.g., ['CATEGORY_PERSONAL', 'INBOX']
LABEL_FILTER = ['CATEGORY_PERSONAL', 'INBOX']  # Show personal inbox items


class GmailService:
    """Handle Gmail API operations.
    
    1. Core email operations
    2. Message management
    3. Label handling
    v5
    """
    
    SYSTEM_LABELS = {
        'INBOX': '📥 Inbox',
        'UNREAD': '🔵 Unread',
        'IMPORTANT': '⭐ Important',
        'CATEGORY_PERSONAL': '👤 Primary',
        'CATEGORY_SOCIAL': '👥 Social',
        'CATEGORY_PROMOTIONS': '🏷️ Promotions',
        'CATEGORY_UPDATES': '🔔 Updates',
        'CATEGORY_FORUMS': '💬 Forums',
        'DRAFT': '📝 Draft',
        'SENT': '➡️ Sent',
        'SPAM': '⚠️ Spam',
        'TRASH': '🗑️ Trash'
    }
    
    def __init__(self):
        """Initialize Gmail service.
        
        1. Get authenticated service
        2. Set up base configuration
        v3
        """
        self.service = gmail_auth.get_service()
        self.labels = {}
        self._cache_labels()
        # Enable batch mode if available
        try:
            self.batch = self.service.new_batch_http_request()
        except AttributeError:
            self.batch = None
    
    def _cache_labels(self):
        """Cache available labels.
        
        1. Fetch all labels
        2. Map IDs to names
        v1
        """
        try:
            results = self.service.users().labels().list(userId='me').execute()
            self.labels = {
                label['id']: label['name']
                for label in results.get('labels', [])
            }
        except Exception as e:
            print(f"Error caching labels: {e}")
            self.labels = {}
    
    def get_label_names(self, label_ids: List[str]) -> List[str]:
        """Convert label IDs to readable names.
        
        1. Check system labels
        2. Check custom labels
        3. Return readable names
        v2
        """
        names = []
        for lid in label_ids:
            if lid in self.SYSTEM_LABELS:
                names.append(self.SYSTEM_LABELS[lid])
            elif lid in self.labels:
                names.append(f"🏷️ {self.labels[lid]}")  # Custom labels with tag emoji
        return names
    
    def list_unread_messages(
        self,
        max_results: int = 10,
        days_back: int = 7,
        include_labels: bool = True,
        label_filter: Optional[List[str]] = None
    ) -> List[Dict]:
        """Get unread messages.
        
        1. Search for unread messages
        2. Get message metadata
        3. Format response
        v5
        
        Args:
            max_results: Maximum number of messages to return
            days_back: Only get messages from last N days
            include_labels: Whether to include label info in response
            label_filter: List of label IDs to filter by. If None, uses LABEL_FILTER global var.
                        Set to empty list [] to disable filtering.
        """
        try:
            # Calculate date range
            after_date = datetime.utcnow() - timedelta(days=days_back)
            query = f'is:unread after:{after_date.strftime("%Y/%m/%d")}'
            
            # Add label filter
            filter_labels = label_filter if label_filter is not None else LABEL_FILTER
            if filter_labels:
                query += ' (' + ' OR '.join(f'label:{label}' for label in filter_labels) + ')'
            
            # First, get only message IDs and threads
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            if not messages:
                return []
            
            # Get metadata for all messages in batches
            message_data = {}
            
            def callback(request_id, response, exception):
                if exception is None:
                    message_data[request_id] = response
            
            # Process in batches of 50 (Gmail API limit)
            batch_size = 50
            for i in range(0, len(messages), batch_size):
                batch = self.service.new_batch_http_request(callback=callback)
                
                for msg in messages[i:i + batch_size]:
                    batch.add(
                        self.service.users().messages().get(
                            userId='me',
                            id=msg['id'],
                            format='metadata',
                            metadataHeaders=['From', 'Subject', 'Date', 'To', 'Message-ID']
                        ),
                        request_id=msg['id']
                    )
                
                batch.execute()
            
            # Format the metadata
            formatted_messages = []
            for msg_id, msg_data in message_data.items():
                thread_id = msg_data['threadId']
                headers = msg_data['payload']['headers']
                
                formatted_messages.append({
                    'id': msg_id,
                    'thread_id': thread_id,
                    'subject': next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject'),
                    'from': next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown'),
                    'date': next((h['value'] for h in headers if h['name'] == 'Date'), ''),
                    'to': next((h['value'] for h in headers if h['name'] == 'To'), ''),
                    'message_id': next((h['value'] for h in headers if h['name'] == 'Message-ID'), ''),
                    'labels': self.get_label_names(msg_data.get('labelIds', [])) if include_labels else [],
                    'thread_messages': None  # Will be loaded on demand
                })
            
            return formatted_messages
            
        except Exception as e:
            print(f"Error listing messages: {e}")
            return []
    
    def get_thread_messages(self, thread_id: str, max_length: int = 300) -> List[Dict]:
        """Get all messages in a thread.
        
        1. Fetch thread
        2. Extract messages
        3. Format content
        v2
        """
        try:
            thread = self.service.users().threads().get(
                userId='me',
                id=thread_id
            ).execute()
            
            messages = []
            for msg in thread['messages']:
                headers = msg['payload']['headers']
                content = self._extract_content(msg)
                
                # Truncate content if needed
                if len(content) > max_length:
                    content = content[:max_length] + "..."
                
                messages.append({
                    'from': next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown'),
                    'date': next((h['value'] for h in headers if h['name'] == 'Date'), ''),
                    'content': content,
                    'labels': self.get_label_names(msg.get('labelIds', []))
                })
            
            return messages
            
        except Exception as e:
            print(f"Error getting thread messages: {e}")
            return []
    
    def _extract_content(self, message: Dict) -> str:
        """Extract readable content from message.
        
        1. Find text parts recursively
        2. Decode content
        3. Clean and format
        v3
        """
        def _find_text_parts(payload: Dict) -> List[Dict]:
            """Recursively find all text parts in message payload."""
            parts = []
            
            # Check if this part has data
            if 'body' in payload and 'data' in payload['body']:
                parts.append({
                    'mimeType': payload.get('mimeType', ''),
                    'data': payload['body']['data']
                })
            
            # Recurse into nested parts
            if 'parts' in payload:
                for part in payload['parts']:
                    parts.extend(_find_text_parts(part))
            
            return parts
        
        text = ""
        
        try:
            # Get all text parts recursively
            all_parts = _find_text_parts(message['payload'])
            
            # Prefer text/plain over text/html
            text_plain_parts = [p for p in all_parts if p['mimeType'] == 'text/plain']
            text_html_parts = [p for p in all_parts if p['mimeType'] == 'text/html']
            
            if text_plain_parts:
                # Use first text/plain part
                text = base64.urlsafe_b64decode(
                    text_plain_parts[0]['data'].encode('UTF-8')
                ).decode('utf-8')
            elif text_html_parts:
                # Fall back to HTML if no plain text
                html_text = base64.urlsafe_b64decode(
                    text_html_parts[0]['data'].encode('UTF-8')
                ).decode('utf-8')
                # Simple HTML to text conversion
                import re
                text = re.sub('<[^<]+?>', '', html_text)
                text = html.unescape(text)
            
            # Clean up the text
            text = text.strip()
            
            return text
        except Exception as e:
            print(f"Error extracting content: {e}")
            return ""
    
    def send_message(
        self,
        to: str,
        subject: str,
        body: str,
        reply_to: Optional[Dict] = None
    ) -> Optional[str]:
        """Send an email message.
        
        1. Create message object
        2. Add headers and content
        3. Send and return ID
        v1
        """
        try:
            message = MIMEText(body)
            message['to'] = to
            message['subject'] = subject
            
            if reply_to:
                message['In-Reply-To'] = reply_to.get('message_id', '')
                message['References'] = reply_to.get('message_id', '')
                
                # If original subject doesn't start with Re:, add it
                if not subject.lower().startswith('re:'):
                    message['subject'] = f"Re: {subject}"
            
            raw = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            sent_message = self.service.users().messages().send(
                userId='me',
                body={'raw': raw}
            ).execute()
            
            return sent_message['id']
            
        except Exception as e:
            print(f"Error sending message: {e}")
            return None
    
    def send_message_with_attachment(
        self,
        to: str,
        subject: str,
        body: str,
        attachment_paths: List[str],
        cc: Optional[str] = None,
        reply_to: Optional[Dict] = None
    ) -> Optional[str]:
        """Send an email message with attachments.
        
        1. Create multipart message
        2. Add text body
        3. Add attachments
        4. Send and return ID
        v1
        """
        try:
            # Create the message container
            message = MIMEMultipart()
            message['to'] = to
            message['subject'] = subject
            
            if cc:
                message['cc'] = cc
            
            if reply_to:
                message['In-Reply-To'] = reply_to.get('message_id', '')
                message['References'] = reply_to.get('message_id', '')
                
                # If original subject doesn't start with Re:, add it
                if not subject.lower().startswith('re:'):
                    message['subject'] = f"Re: {subject}"
            
            # Attach the body
            msg_body = MIMEText(body, 'plain')
            message.attach(msg_body)
            
            # Process attachments
            for file_path in attachment_paths:
                if not os.path.exists(file_path):
                    print(f"Warning: Attachment file not found: {file_path}")
                    continue
                
                # Determine MIME type
                filename = os.path.basename(file_path)
                
                # Create attachment
                with open(file_path, 'rb') as f:
                    # For PDF files, use application/pdf
                    if file_path.lower().endswith('.pdf'):
                        attachment = MIMEBase('application', 'pdf')
                    else:
                        attachment = MIMEBase('application', 'octet-stream')
                    
                    attachment.set_payload(f.read())
                
                # Encode the attachment
                encoders.encode_base64(attachment)
                
                # Add header
                attachment.add_header(
                    'Content-Disposition',
                    f'attachment; filename="{filename}"'
                )
                
                # Attach to message
                message.attach(attachment)
            
            # Convert message to raw format
            raw = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            # Send the message
            sent_message = self.service.users().messages().send(
                userId='me',
                body={'raw': raw}
            ).execute()
            
            return sent_message['id']
            
        except Exception as e:
            print(f"Error sending message with attachment: {e}")
            return None
    
    def mark_as_read(self, message_id: str) -> bool:
        """Mark a message as read.
        
        1. Remove UNREAD label
        2. Confirm modification
        v1
        """
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            return True
            
        except Exception as e:
            print(f"Error marking message as read: {e}")
            return False
    
    def get_message(self, message_id: str) -> Optional[Dict]:
        """Get full message details.
        
        1. Fetch message
        2. Extract headers
        3. Get content
        v1
        """
        try:
            return self.service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()
        except Exception as e:
            print(f"Error getting message {message_id}: {e}")
            return None


# Global instance for easy access
gmail_service = GmailService() 