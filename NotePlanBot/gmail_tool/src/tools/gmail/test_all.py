"""Comprehensive test suite for Gmail tools.

1. Test authentication
2. Test email operations
3. Test drafting and context
v2
"""
import os
from datetime import datetime
from typing import Dict, List
import colorama
from dotenv import load_dotenv
from .service import gmail_service
from .gmail_send_email import draft_assistant
from .storage.context import context_manager
from .handlers.replies import process_and_reply


# Initialize colorama
colorama.init()
# Load environment variables
load_dotenv()


def format_date(date_str: str) -> str:
    """Format date string for display.
    
    1. Parse date string
    2. Format for display
    v1
    """
    try:
        # Parse various date formats
        for fmt in [
            "%a, %d %b %Y %H:%M:%S %z",
            "%a, %d %b %Y %H:%M:%S %Z",
            "%d %b %Y %H:%M:%S %z",
        ]:
            try:
                dt = datetime.strptime(date_str.split('(')[0].strip(), fmt)
                return dt.strftime("%Y-%m-%d %H:%M")
            except ValueError:
                continue
        return date_str
    except Exception:
        return date_str


def format_thread_preview(thread_messages: List[Dict], max_chars: int = 300) -> str:
    """Format thread messages for preview.
    
    1. Combine messages
    2. Format display
    3. Truncate if needed
    v1
    """
    preview = []
    for msg in thread_messages:
        date = format_date(msg['date'])
        from_name = msg['from'].split('<')[0].strip(' "\'')
        content = msg['content'].replace('\n', ' ').strip()
        preview.append(f"{colorama.Style.DIM}{date} {from_name}:{colorama.Style.RESET_ALL} {content}")
    
    # Join with newlines and truncate
    result = '\n'.join(preview)
    if len(result) > max_chars:
        result = result[:max_chars] + "..."
    
    return result


def test_auth():
    """Test Gmail authentication.
    
    1. Check credentials
    2. Test service creation
    v1
    """
    print(f"\n{colorama.Fore.CYAN}Testing Gmail Authentication...{colorama.Style.RESET_ALL}")
    try:
        _ = gmail_service.service
        print(f"{colorama.Fore.GREEN}✓ Gmail authentication successful{colorama.Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{colorama.Fore.RED}✗ Gmail authentication failed: {e}{colorama.Style.RESET_ALL}")
        return False


def test_assistant():
    """Test OpenAI assistant setup.
    
    1. Check API key
    2. Test assistant creation
    v1
    """
    print(f"\n{colorama.Fore.CYAN}Testing OpenAI Assistant...{colorama.Style.RESET_ALL}")
    try:
        if not draft_assistant.assistant_id:
            print(f"{colorama.Fore.RED}✗ Assistant not initialized{colorama.Style.RESET_ALL}")
            return False
        print(f"{colorama.Fore.GREEN}✓ Assistant initialized (ID: {draft_assistant.assistant_id}){colorama.Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{colorama.Fore.RED}✗ Assistant setup failed: {e}{colorama.Style.RESET_ALL}")
        return False


def test_storage():
    """Test context storage.
    
    1. Check storage directory
    2. Test file operations
    v1
    """
    print(f"\n{colorama.Fore.CYAN}Testing Context Storage...{colorama.Style.RESET_ALL}")
    try:
        storage_dir = context_manager.storage_dir
        if not os.path.exists(storage_dir):
            print(f"{colorama.Fore.RED}✗ Storage directory not found: {storage_dir}{colorama.Style.RESET_ALL}")
            return False
        print(f"{colorama.Fore.GREEN}✓ Storage directory ready: {storage_dir}{colorama.Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{colorama.Fore.RED}✗ Storage test failed: {e}{colorama.Style.RESET_ALL}")
        return False


def test_unread_emails():
    """Test unread email processing.
    
    1. Fetch unread emails
    2. Test draft generation
    3. Check context storage
    v2
    """
    print(f"\n{colorama.Fore.CYAN}Testing Unread Email Processing...{colorama.Style.RESET_ALL}")
    try:
        results = process_and_reply(max_emails=3, auto_send=False)
        
        if not results:
            print(f"{colorama.Fore.GREEN}✓ No unread emails to process{colorama.Style.RESET_ALL}")
            return True
        
        print(f"\n{colorama.Fore.WHITE}Processed {len(results)} emails:{colorama.Style.RESET_ALL}")
        for i, result in enumerate(results, 1):
            msg = result['message']
            draft = result['draft']
            context = result['context']
            
            print(f"\n{colorama.Fore.YELLOW}{'=' * 60}{colorama.Style.RESET_ALL}")
            print(f"{colorama.Fore.GREEN}Email {i}:{colorama.Style.RESET_ALL}")
            print(f"{colorama.Fore.BLUE}From:{colorama.Style.RESET_ALL} {msg['from']}")
            print(f"{colorama.Fore.BLUE}Subject:{colorama.Style.RESET_ALL} {msg['subject']} ({msg.get('thread_count', 1)})")
            print(f"{colorama.Fore.BLUE}Date:{colorama.Style.RESET_ALL} {format_date(msg['date'])}")
            
            # Show thread preview
            if 'thread_messages' in msg:
                print(f"\n{colorama.Fore.BLUE}Thread Preview:{colorama.Style.RESET_ALL}")
                print(format_thread_preview(msg['thread_messages']))
            
            print(f"\n{colorama.Fore.BLUE}Importance:{colorama.Style.RESET_ALL} {context.importance}/5")
            print(f"{colorama.Fore.BLUE}Tags:{colorama.Style.RESET_ALL} {', '.join(context.tags)}")
            print(f"{colorama.Fore.BLUE}Draft Length:{colorama.Style.RESET_ALL} {len(draft)} chars")
            
            # Verify context storage
            stored_context = context_manager.get_context(msg['id'])
            if stored_context:
                print(f"{colorama.Fore.GREEN}✓ Context stored successfully{colorama.Style.RESET_ALL}")
            else:
                print(f"{colorama.Fore.RED}✗ Context storage failed{colorama.Style.RESET_ALL}")
            
            print(f"{colorama.Fore.YELLOW}{'=' * 60}{colorama.Style.RESET_ALL}")
        
        return True
        
    except Exception as e:
        print(f"{colorama.Fore.RED}✗ Email processing failed: {e}{colorama.Style.RESET_ALL}")
        return False


def run_all_tests():
    """Run all tests in sequence.
    
    1. Run each test
    2. Report results
    v2
    """
    tests = [
        ("Authentication", test_auth),
        ("Assistant Setup", test_assistant),
        ("Storage System", test_storage),
        ("Email Processing", test_unread_emails)
    ]
    
    results = []
    
    print(f"\n{colorama.Fore.CYAN}=== Gmail Tools Test Suite ==={colorama.Style.RESET_ALL}")
    
    for name, test_func in tests:
        print(f"\n{colorama.Fore.YELLOW}--- Testing {name} ---{colorama.Style.RESET_ALL}")
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"{colorama.Fore.RED}✗ Test failed with error: {e}{colorama.Style.RESET_ALL}")
            results.append((name, False))
    
    print(f"\n{colorama.Fore.CYAN}=== Test Results ==={colorama.Style.RESET_ALL}")
    for name, success in results:
        status = f"{colorama.Fore.GREEN}✓ PASS" if success else f"{colorama.Fore.RED}✗ FAIL"
        print(f"{status} - {name}{colorama.Style.RESET_ALL}")


if __name__ == '__main__':
    run_all_tests() 