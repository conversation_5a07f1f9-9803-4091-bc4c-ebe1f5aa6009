"""Message type definitions and parsing.

1. Define message schemas
2. Handle parsing
3. Track metadata
v3
"""
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional


class MessageType(Enum):
    """Types of messages we handle.
    
    1. Doc comments
    2. Calendar events
    3. Regular emails
    v1
    """
    DOC_COMMENT = "doc_comment"
    CALENDAR = "calendar"
    EMAIL = "email"


class RecipientType(Enum):
    """How <PERSON> was addressed in the message.
    
    1. Direct recipient (TO field)
    2. Carbon copied (CC field)
    3. Not directly addressed
    v2
    """
    TO = "to"  # <PERSON> is in the TO field
    CC = "cc"  # Alex is CC'd
    NONE = "none"  # <PERSON> is not directly addressed


@dataclass
class MessageMetadata:
    """Common metadata for all message types.
    
    1. Track timing
    2. Track addressing
    3. Store thread info
    v2
    """
    message_id: str
    thread_id: str
    timestamp: datetime
    recipient_type: RecipientType  # How <PERSON> was addressed
    sender: str
    project_name: Optional[str] = None
    
    @property
    def days_ago(self) -> int:
        """Calculate days since message.
        
        1. Convert to UTC
        2. Calculate difference
        3. Return days
        v2
        """
        now = datetime.now(self.timestamp.tzinfo)
        return (now - self.timestamp).days
    
    @property
    def is_to_alex(self) -> bool:
        """Check if <PERSON> is in the TO field."""
        return self.recipient_type == RecipientType.TO
    
    @property
    def is_cc_to_alex(self) -> bool:
        """Check if Alex is CC'd."""
        return self.recipient_type == RecipientType.CC


@dataclass
class DocComment:
    """Google Doc comment message.
    
    1. Store comment data
    2. Track document info
    3. Handle formatting
    v1
    """
    metadata: MessageMetadata
    doc_type: str  # document, spreadsheet, etc.
    title: str
    comment_count: int
    comments: List[Dict[str, str]]  # [{user, text}]
    links: Optional[Dict[str, Dict[str, str]]] = None  # {id: {type, url}}
    is_action_item: bool = False


@dataclass
class CalendarEvent:
    """Calendar event message.
    
    1. Store event data
    2. Track timing
    3. Handle recurrence
    v1
    """
    metadata: MessageMetadata
    title: str
    time: str
    timezone: Optional[str] = None
    recurrence: Optional[str] = None
    location: Optional[str] = None
    day: Optional[str] = None
    when: Optional[str] = None


@dataclass
class Email:
    """Regular email message.
    
    1. Store email content
    2. Track recipients
    3. Handle attachments
    v1
    """
    metadata: MessageMetadata
    subject: str
    body: str
    to: List[str]
    cc: List[str] = None
    attachments: List[Dict] = None  # [{filename, type, size}]


def determine_recipient_type(message_data: Dict, user_email: str) -> RecipientType:
    """Determine how the user was addressed.
    
    1. Check TO field
    2. Check CC field
    3. Default to NONE
    v1
    """
    if 'to' in message_data and user_email in message_data['to']:
        return RecipientType.TO
    if 'cc' in message_data and user_email in message_data['cc']:
        return RecipientType.CC
    return RecipientType.NONE 