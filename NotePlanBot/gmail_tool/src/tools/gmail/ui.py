"""Interactive terminal UI for Gmail threads.

1. Display threads in rich table
2. Handle user input
3. Navigate pages
v1
"""
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple, Set
from datetime import datetime, timedelta
import asyncio
from .common import get_console
from rich.table import Table
from rich.prompt import Prompt
from rich.text import Text
from .services import get_gmail_service
from .ui_viewthreads import get_thread_stats, clean_content, format_date, is_user_name
from .ui_task_manager import TaskManager, TaskStatus
from .ui_snooze_manager import SnoozeManager
from .ui_email_grouper import EmailGrouper
from .models import EmailGroup


@dataclass
class CFG:
    """Configuration for Gmail UI.
    
    1. Display settings
    2. Page settings
    3. Column configuration
    v1
    """
    threads_per_page: int = 10
    max_content_preview: int = 50
    
    # Column visibility settings
    show_thread_id: bool = False
    show_labels: bool = True
    show_participants: bool = True
    show_duration: bool = True
    show_last_message: bool = True
    show_group: bool = False
    
    # Column widths
    col_width_subject: int = 40
    col_width_participants: int = 20
    col_width_content: int = 50
    
    # Search settings
    days_back: int = 7
    include_categories: List[str] = None
    
    def __post_init__(self):
        if self.include_categories is None:
            self.include_categories = ['CATEGORY_PERSONAL', 'INBOX']


# Global configuration
cfg = CFG()
console = get_console()
gmail_service = get_gmail_service()
task_manager = TaskManager()
snooze_manager = SnoozeManager()
email_grouper = EmailGrouper()
current_email_group: Optional[EmailGroup] = None


def get_thread_display_data(thread_messages: List[Dict], thread_subject: str) -> Dict:
    """Extract display data from thread.
    
    1. Get thread statistics
    2. Format for display
    3. Return structured data
    v1
    """
    stats = get_thread_stats(thread_messages)
    if not stats:
        return None
    
    # Get participant summary
    participants = []
    for name, count in stats['participant_list'][:3]:  # Top 3 participants
        # Clean name
        name = name.split(' (Google')[0]
        name = name.split(' via ')[0]
        if len(name) > 15:
            name = name[:12] + "..."
        style = "green" if is_user_name(name) else "yellow"
        participants.append(f"[{style}]{name}[/{style}] ({count})")
    
    if len(stats['participant_list']) > 3:
        participants.append(f"+{len(stats['participant_list']) - 3} more")
    
    # Get last message preview
    last_msg = thread_messages[-1] if thread_messages else None
    last_preview = ""
    if last_msg:
        sender = last_msg['from'].split('<')[0].strip()
        sender = sender.split(' (Google')[0]
        sender = sender.split(' via ')[0]
        content = clean_content(last_msg['content'])
        if len(content) > cfg.max_content_preview:
            content = content[:cfg.max_content_preview] + "..."
        last_preview = f"[dim]{sender}:[/dim] {content}"
    
    return {
        'subject': thread_subject,
        'msg_count': stats['message_count'],
        'participants': participants,
        'duration': f"{stats['duration_days']:.1f}d",
        'last_ago': f"{stats['days_since_last']:.1f}d ago",
        'last_preview': last_preview,
        'thread_id': thread_messages[0].get('thread_id') if thread_messages else None
    }


def create_threads_table(threads: List[Dict], start_idx: int = 0) -> Table:
    """Create rich table with thread data.
    
    1. Set up columns
    2. Add thread rows
    3. Return formatted table
    v1
    """
    table = Table(show_header=True, header_style="bold cyan", border_style="bright_blue", show_lines=True)
    
    # Add columns based on config
    table.add_column("#", style="dim", width=3)
    table.add_column("Subject", width=cfg.col_width_subject)
    table.add_column("Msgs", justify="right", width=4)
    
    if cfg.show_participants:
        table.add_column("Participants", width=cfg.col_width_participants)
    
    if cfg.show_duration:
        table.add_column("Span", justify="right", width=6)
    
    table.add_column("Last", justify="right", width=8)
    
    if cfg.show_group and current_email_group:
        table.add_column("Group", width=15)
    
    if cfg.show_last_message:
        table.add_column("Preview", width=cfg.col_width_content)
    
    # Add rows
    for i, thread_data in enumerate(threads):
        if not thread_data:
            continue
            
        row = [
            str(start_idx + i + 1),
            Text(thread_data['subject'], overflow="ellipsis"),
            str(thread_data['msg_count'])
        ]
        
        if cfg.show_participants:
            # Join participants with comma instead of newline for better table layout
            participants_str = ", ".join(thread_data['participants'])
            row.append(Text(participants_str, overflow="ellipsis"))
        
        if cfg.show_duration:
            row.append(thread_data['duration'])
        
        row.append(thread_data['last_ago'])
        
        if cfg.show_group and current_email_group:
            thread_id = thread_data.get('thread_id', '')
            group_id = current_email_group.assignments.get(thread_id, 0)
            group_name = current_email_group.groups.get(group_id, "Unknown")
            row.append(Text(group_name, style="yellow"))
        
        if cfg.show_last_message:
            row.append(Text(thread_data['last_preview'], overflow="ellipsis"))
        
        table.add_row(*row)
    
    return table


def create_tasks_table() -> Optional[Table]:
    """Create table showing active tasks.
    
    1. Get active tasks
    2. Format table
    3. Return if any
    v1
    """
    tasks = task_manager.get_active_tasks()
    if not tasks:
        return None
    
    table = Table(show_header=True, header_style="bold green", border_style="green", show_lines=True)
    table.add_column("ID", width=8)
    table.add_column("Title", width=30)
    table.add_column("Deadline", width=15)
    table.add_column("Status", width=12)
    table.add_column("Source", width=15)
    
    for task in tasks:
        deadline_str = task.deadline.strftime("%Y-%m-%d") if task.deadline else "No deadline"
        status_color = {
            TaskStatus.PENDING: "yellow",
            TaskStatus.IN_PROGRESS: "blue",
            TaskStatus.COMPLETED: "green",
            TaskStatus.CANCELLED: "red"
        }.get(task.status, "white")
        
        table.add_row(
            task.id,
            Text(task.title, overflow="ellipsis"),
            deadline_str,
            Text(task.status.value, style=status_color),
            Text(task.source_thread_id[:8] + "...", overflow="ellipsis")
        )
    
    return table


def fetch_threads_page(page: int = 0) -> Tuple[List[Dict], int]:
    """Fetch a page of threads.
    
    1. Get messages from Gmail
    2. Group by thread
    3. Sort by group if active
    4. Return thread data
    v2
    """
    offset = page * cfg.threads_per_page
    
    # Fetch more than needed to ensure we get enough threads
    messages = gmail_service.list_unread_messages(
        max_results=cfg.threads_per_page * 3,
        days_back=cfg.days_back,
        label_filter=cfg.include_categories
    )
    
    if not messages:
        return [], 0
    
    # Get snoozed threads to filter out
    snoozed_threads = snooze_manager.get_snoozed_thread_ids()
    
    # Group messages by thread, excluding snoozed
    threads_map = {}
    thread_order = []
    
    for msg in messages:
        thread_id = msg['thread_id']
        if thread_id not in snoozed_threads and thread_id not in threads_map:
            threads_map[thread_id] = msg
            thread_order.append(thread_id)
    
    # Get all threads (not just page) for proper sorting
    all_threads = []
    for thread_id in thread_order:
        msg = threads_map[thread_id]
        thread_messages = gmail_service.get_thread_messages(thread_id)
        
        if thread_messages:
            display_data = get_thread_display_data(thread_messages, msg['subject'])
            if display_data:
                display_data['thread_id'] = thread_id
                display_data['message_id'] = msg['id']
                display_data['original_order'] = thread_order.index(thread_id)
                all_threads.append(display_data)
    
    # Sort by group if grouping is active
    if cfg.show_group and current_email_group:
        # Sort by group ID first, then by recency (original order)
        all_threads.sort(key=lambda t: (
            current_email_group.assignments.get(t['thread_id'], 999),
            t['original_order']
        ))
    
    # Get threads for this page
    page_threads = all_threads[offset:offset + cfg.threads_per_page]
    
    return page_threads, len(all_threads)


def show_thread_detail(thread_id: str) -> None:
    """Display full thread conversation.
    
    1. Get all messages
    2. Format nicely
    3. Show in console
    v1
    """
    thread_messages = gmail_service.get_thread_messages(thread_id, max_length=1000)
    if not thread_messages:
        console.print("[red]Thread not found[/red]")
        return
    
    # Get thread subject from first message
    first_msg_headers = gmail_service.get_message(thread_messages[0].get('id', ''))
    subject = "No Subject"
    if first_msg_headers:
        headers = first_msg_headers.get('payload', {}).get('headers', [])
        subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
    
    console.print(f"\n[bold cyan]Thread: {subject}[/bold cyan]")
    console.print("[dim]" + "=" * 80 + "[/dim]\n")
    
    # Get all names for highlighting
    names = set()
    for msg in thread_messages:
        names.add(msg['from'].split('<')[0].strip())
    
    # Display each message
    for i, msg in enumerate(thread_messages):
        sender = msg['from'].split('<')[0].strip()
        style = "bold green" if is_user_name(sender) else "bold yellow"
        
        console.print(f"[{style}]{sender}[/{style}] • [blue]{format_date(msg['date'])}[/blue]")
        
        content = clean_content(msg['content'])
        if content:
            console.print(content)
        console.print()
    
    console.print("[dim]" + "=" * 80 + "[/dim]")
    input("\nPress Enter to return to list...")


def show_column_settings() -> None:
    """Display column configuration menu.
    
    1. Show current settings
    2. Allow toggling
    3. Update config
    v1
    """
    console.print("\n[bold cyan]Column Settings[/bold cyan]")
    console.print("[dim]Toggle visibility of columns[/dim]\n")
    
    settings = [
        ("1", "Thread ID", cfg.show_thread_id),
        ("2", "Labels", cfg.show_labels),
        ("3", "Participants", cfg.show_participants),
        ("4", "Duration", cfg.show_duration),
        ("5", "Last Message Preview", cfg.show_last_message),
        ("6", "Group", cfg.show_group),
    ]
    
    for key, name, enabled in settings:
        status = "[green]✓[/green]" if enabled else "[red]✗[/red]"
        console.print(f"{key}. {status} {name}")
    
    console.print("\n0. Back to main menu")
    
    choice = Prompt.ask("\nToggle setting", choices=["0", "1", "2", "3", "4", "5", "6"])
    
    if choice == "1":
        cfg.show_thread_id = not cfg.show_thread_id
    elif choice == "2":
        cfg.show_labels = not cfg.show_labels
    elif choice == "3":
        cfg.show_participants = not cfg.show_participants
    elif choice == "4":
        cfg.show_duration = not cfg.show_duration
    elif choice == "5":
        cfg.show_last_message = not cfg.show_last_message
    elif choice == "6":
        cfg.show_group = not cfg.show_group


async def group_emails(threads: List[Dict]) -> None:
    """Group emails using LLM.
    
    1. Call grouper
    2. Show reasoning
    3. Update display
    v1
    """
    global current_email_group
    
    if not threads:
        console.print("[yellow]No emails to group.[/yellow]")
        return
    
    with console.status("[cyan]Analyzing emails for grouping...[/cyan]"):
        current_email_group = await email_grouper.group_emails(threads)
    
    console.print("\n[bold cyan]Email Grouping Results[/bold cyan]")
    console.print(f"\n[yellow]Reasoning:[/yellow] {current_email_group.reasoning}\n")
    
    console.print("[green]Groups created:[/green]")
    for group_id, group_name in current_email_group.groups.items():
        count = sum(1 for t_id in current_email_group.assignments.values() if t_id == group_id)
        console.print(f"  {group_id}. {group_name} ({count} emails)")
    
    cfg.show_group = True
    console.print("\n[green]Group column enabled. Refresh to see groups.[/green]")
    input("\nPress Enter to continue...")


def snooze_email(thread_id: str, position: int) -> None:
    """Snooze an email thread.
    
    1. Get snooze duration
    2. Add to queue
    3. Refresh display
    v1
    """
    console.print("\n[bold cyan]Snooze Email[/bold cyan]")
    console.print("How long to snooze?")
    console.print("1. Until tomorrow (9am)")
    console.print("2. 2 days")
    console.print("3. 3 days")
    console.print("4. Until next week")
    console.print("5. 1 hour")
    console.print("6. 4 hours")
    console.print("0. Cancel")
    
    choice = Prompt.ask("Choice", choices=["0", "1", "2", "3", "4", "5", "6"])
    
    if choice == "0":
        return
    
    now = datetime.now()
    if choice == "1":
        tomorrow = now + timedelta(days=1)
        until = tomorrow.replace(hour=9, minute=0, second=0, microsecond=0)
    elif choice == "2":
        until = now + timedelta(days=2)
    elif choice == "3":
        until = now + timedelta(days=3)
    elif choice == "4":
        until = now + timedelta(days=7)
    elif choice == "5":
        until = now + timedelta(hours=1)
    else:  # choice == "6"
        until = now + timedelta(hours=4)
    
    # Get message ID for the thread
    messages = gmail_service.list_unread_messages(max_results=1)
    message_id = ""
    for msg in messages:
        if msg['thread_id'] == thread_id:
            message_id = msg['id']
            break
    
    snooze_manager.snooze_email(thread_id, message_id, until, position)
    console.print(f"\n[green]Email snoozed until {until.strftime('%Y-%m-%d %H:%M')}[/green]")
    input("\nPress Enter to continue...")


def _d_to_use_llm_title(suggested_title: str, default_title: str) -> str:
    """Helper to handle 'd' shortcut for using LLM title.
    
    1. Show instruction
    2. Get input
    3. Return title
    v2
    """
    console.print("[dim]Or type your own custom title[/dim]")
    user_input = Prompt.ask("Task title")
    
    # Handle 'd' shortcut
    if user_input.strip().lower() == 'd':
        return suggested_title
    
    # Handle empty input (user pressed enter) - use default
    if not user_input.strip():
        return default_title
    
    return user_input


def create_task_from_email(thread_id: str, subject: str) -> None:
    """Create task from email thread.
    
    1. Generate AI title suggestion
    2. Get task details
    3. Create task
    4. Show confirmation
    v3
    """
    console.print(f"\n[bold cyan]Create Task from Email[/bold cyan]")
    console.print(f"Thread: {subject}\n")
    
    # Get email content for better title generation
    thread_messages = gmail_service.get_thread_messages(thread_id, max_length=300)
    email_content = ""
    if thread_messages:
        # Get latest message content
        email_content = thread_messages[-1].get('content', '')
    
    # Generate AI title suggestion
    from .async_utils import llm_operation
    try:
        suggested_title = task_manager.generate_task_title(subject, email_content)
    except Exception:
        suggested_title = subject[:50]
    
    # Show default title option first
    default_title = subject[:50]
    console.print(f"[dim]Default Title (press Enter):[/dim] {default_title}")
    console.print(f"[yellow]AI Suggested Title (press 'd'):[/yellow] {suggested_title}")
    
    title = _d_to_use_llm_title(suggested_title, default_title)
    description = Prompt.ask("Description", default=f"Follow up on: {subject}")
    
    console.print("\nSet deadline?")
    console.print("1. Today")
    console.print("2. Tomorrow")
    console.print("3. Next week")
    console.print("4. Custom date")
    console.print("5. No deadline")
    
    deadline_choice = Prompt.ask("Choice", choices=["1", "2", "3", "4", "5"])
    
    deadline = None
    if deadline_choice == "1":
        deadline = datetime.now().replace(hour=17, minute=0, second=0, microsecond=0)
    elif deadline_choice == "2":
        deadline = datetime.now() + timedelta(days=1)
        deadline = deadline.replace(hour=17, minute=0, second=0, microsecond=0)
    elif deadline_choice == "3":
        deadline = datetime.now() + timedelta(days=7)
    elif deadline_choice == "4":
        date_str = Prompt.ask("Enter date (YYYY-MM-DD)")
        try:
            deadline = datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            console.print("[red]Invalid date format.[/red]")
    
    task = task_manager.create_task(title, description, thread_id, deadline)
    console.print(f"\n[green]Task created: {task.id} - {task.title}[/green]")
    input("\nPress Enter to continue...")


def main_loop() -> None:
    """Main interactive loop.
    
    1. Display threads
    2. Handle navigation
    3. Process commands
    v1
    """
    current_page = 0
    threads_cache = {}
    
    while True:
        console.clear()
        
        # Show tasks table if any
        tasks_table = create_tasks_table()
        if tasks_table:
            console.print("[bold green]Active Tasks[/bold green]")
            console.print(tasks_table)
            console.print()
        
        console.print("[bold cyan]Gmail Thread Viewer[/bold cyan]")
        console.print(f"[dim]Page {current_page + 1} • {cfg.days_back} days • {cfg.threads_per_page} per page[/dim]\n")
        
        # Get threads for current page
        if current_page not in threads_cache:
            threads, total_threads = fetch_threads_page(current_page)
            threads_cache[current_page] = (threads, total_threads)
        else:
            threads, total_threads = threads_cache[current_page]
        
        if not threads:
            console.print("[yellow]No unread messages found.[/yellow]")
        else:
            # Display threads table
            table = create_threads_table(threads, current_page * cfg.threads_per_page)
            console.print(table)
        
        # Show navigation options
        console.print("\n[bold]Navigation:[/bold]")
        console.print("• [cyan]1-10[/cyan]: Open thread (11+ for thread 1, 12+ for thread 2, etc)")
        console.print("• [cyan]3[/cyan]: Group emails • [cyan]4x[/cyan]: Snooze thread x • [cyan]5x[/cyan]: Create task from thread x")
        console.print("• [cyan]n[/cyan]: Next page • [cyan]p[/cyan]: Previous page")
        console.print("• [cyan]r[/cyan]: Refresh • [cyan]c[/cyan]: Column settings")
        console.print("• [cyan]q[/cyan]: Quit")
        
        # Calculate total pages
        total_pages = (total_threads + cfg.threads_per_page - 1) // cfg.threads_per_page
        if total_pages > 1:
            console.print(f"\n[dim]Page {current_page + 1} of {total_pages}[/dim]")
        
        # Get user input
        choice = Prompt.ask("\nChoice").lower().strip()
        
        if choice == 'q':
            break
        elif choice == 'n' and current_page < total_pages - 1:
            current_page += 1
        elif choice == 'p' and current_page > 0:
            current_page -= 1
        elif choice == 'r':
            threads_cache.clear()
        elif choice == 'c':
            show_column_settings()
            threads_cache.clear()  # Clear cache to refresh display
        elif choice == '3':
            # Group emails
            asyncio.run(group_emails(threads))
            threads_cache.clear()
        elif choice.startswith('4') and len(choice) > 1:
            # Snooze email (4x format)
            try:
                thread_idx = int(choice[1:]) - 1
                if 0 <= thread_idx < len(threads):
                    thread = threads[thread_idx]
                    snooze_email(thread['thread_id'], thread_idx)
                    threads_cache.clear()
            except ValueError:
                console.print("[red]Invalid thread number.[/red]")
        elif choice.startswith('5') and len(choice) > 1:
            # Create task (5x format)
            try:
                thread_idx = int(choice[1:]) - 1
                if 0 <= thread_idx < len(threads):
                    thread = threads[thread_idx]
                    create_task_from_email(thread['thread_id'], thread['subject'])
            except ValueError:
                console.print("[red]Invalid thread number.[/red]")
        elif choice.isdigit():
            thread_num = int(choice)
            if thread_num > 10:
                # Handle 11+ format (11 = thread 1, 12 = thread 2, etc)
                actual_idx = (thread_num // 10) - 1
            else:
                actual_idx = thread_num - 1
            
            if 0 <= actual_idx < len(threads):
                thread = threads[actual_idx]
                if thread and 'thread_id' in thread:
                    show_thread_detail(thread['thread_id'])
                    threads_cache.clear()  # Refresh after viewing


def main():
    """Entry point for Gmail UI.
    
    1. Initialize
    2. Run main loop
    3. Handle errors
    v1
    """
    try:
        main_loop()
    except KeyboardInterrupt:
        console.print("\n[yellow]Exiting...[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")


if __name__ == '__main__':
    main()