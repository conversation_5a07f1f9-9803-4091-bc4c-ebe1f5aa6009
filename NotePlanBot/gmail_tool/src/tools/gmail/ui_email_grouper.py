"""Email grouping using LLM.

1. Analyze visible emails
2. Create logical groups
3. Return JSON with reasoning
v1
"""
import json
import asyncio
from typing import List, Dict, Tuple
from openai import AsyncOpenAI
from dotenv import load_dotenv
from .models import EmailGroup


load_dotenv()


class EmailGrouper:
    """Group emails using LLM analysis.
    
    1. Analyze thread data
    2. Generate groups
    3. Assign emails
    v1
    """
    
    def __init__(self):
        """Initialize grouper.
        
        1. Set up OpenAI client
        v1
        """
        self.client = AsyncOpenAI()
    
    async def group_emails(self, threads: List[Dict]) -> EmailGroup:
        """Group emails intelligently.
        
        1. Build context
        2. Ask LLM
        3. Parse response
        v1
        """
        # Build email summaries
        email_summaries = []
        for i, thread in enumerate(threads):
            summary = {
                "index": i,
                "thread_id": thread.get("thread_id", f"thread_{i}"),
                "subject": thread.get("subject", "No Subject"),
                "participants": thread.get("participants", []),
                "preview": thread.get("last_preview", ""),
                "message_count": thread.get("msg_count", 0)
            }
            email_summaries.append(summary)
        
        prompt = f"""Analyze these email threads and group them into logical categories.

Email Threads:
{json.dumps(email_summaries, indent=2)}

Create 3-5 logical groups based on the content, participants, and nature of the emails.
Do NOT use pre-defined categories. Discover groups based on what you see.

Output JSON with this exact structure:
{{
  "groups": {{
    "1": "Group Name 1",
    "2": "Group Name 2",
    ...
  }},
  "assignments": {{
    "thread_id_1": 1,
    "thread_id_2": 2,
    ...
  }},
  "reasoning": "Brief explanation of why you created these groups and how you assigned emails"
}}

Make group names short (2-3 words) and descriptive."""

        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3
            )
            
            result = json.loads(response.choices[0].message.content)
            
            # Ensure all thread IDs are assigned
            for summary in email_summaries:
                thread_id = summary["thread_id"]
                if thread_id not in result["assignments"]:
                    # Assign to first group as fallback
                    result["assignments"][thread_id] = 1
            
            return EmailGroup(**result)
            
        except Exception as e:
            # Fallback grouping
            groups = {
                1: "Uncategorized"
            }
            assignments = {
                thread.get("thread_id", f"thread_{i}"): 1
                for i, thread in enumerate(threads)
            }
            
            return EmailGroup(
                groups=groups,
                assignments=assignments,
                reasoning=f"Failed to group emails: {str(e)}"
            )