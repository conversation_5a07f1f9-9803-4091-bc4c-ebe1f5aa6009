"""UI configuration dataclass.

1. Display settings
2. Column visibility
3. Page settings
v1
"""
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class CFG:
    """Configuration for Gmail UI.
    
    1. Display settings
    2. Page settings
    3. Column configuration
    v1
    """
    threads_per_page: int = 10
    max_content_preview: int = 50
    
    # Column visibility settings
    show_thread_id: bool = False
    show_labels: bool = True
    show_participants: bool = True
    show_duration: bool = True
    show_last_message: bool = True
    show_group: bool = False
    
    # Column widths
    col_width_subject: int = 40
    col_width_participants: int = 20
    col_width_content: int = 50
    
    # Search settings
    days_back: int = 7
    include_categories: List[str] = None
    
    def __post_init__(self):
        if self.include_categories is None:
            self.include_categories = ['CATEGORY_PERSONAL', 'INBOX']