"""Analyze Gmail threads with detailed formatting.

1. Get unread threads
2. Show thread statistics
3. Display formatted content
v8
"""
from datetime import datetime
from typing import Dict, List, Set, Optional
import re
from rich.console import Console
from rich.text import Text
from .service import GmailService
from .storage.alex_context import alex_context


# Initialize rich console
console = Console()
gmail_service = GmailService()

# Known user names (add your variations)
USER_NAMES = {'alex', 'alexander', 'foster', 'alex foster', alex_context.email}


def remove_quotes(content: str) -> str:
    """Remove email quotations.
    
    1. Remove quoted lines
    2. Remove signatures
    3. Clean up
    v1
    """
    lines = content.split('\n')
    cleaned_lines = []
    in_quote = False
    
    for line in lines:
        # Skip empty lines at start
        if not cleaned_lines and not line.strip():
            continue
        
        # Check for quote markers
        if line.strip().startswith('>'):
            in_quote = True
            continue
        
        # Check for forwarded message markers
        if any(marker in line for marker in [
            'From:',
            'Sent:',
            'To:',
            'Subject:',
            'On ',
            'wrote:',
            '---------- Forwarded message ----------'
        ]):
            in_quote = True
            continue
        
        # Skip lines in quoted section
        if in_quote:
            # Reset quote flag if we hit a non-indented, non-empty line
            if line and not line.startswith(' ') and not line.startswith('>'):
                in_quote = False
            else:
                continue
        
        # Add non-quoted lines
        cleaned_lines.append(line)
    
    # Join lines and clean up
    text = '\n'.join(cleaned_lines).strip()
    
    # Remove signatures
    signature_markers = [
        '\n-- \n',
        '\nRegards,',
        '\nBest regards,',
        '\nBest,',
        '\nCheers,',
        '\nThanks,',
        '\nThank you,',
        '\nBest wishes,',
        '\nSincerely,'
    ]
    
    for marker in signature_markers:
        if marker in text:
            text = text.split(marker)[0]
    
    return text.strip()


def test_content_cleaning():
    """Test content cleaning functionality.
    
    1. Test empty content
    2. Test calendar invites
    3. Test Google Docs notifications
    4. Test regular emails
    v1
    """
    test_cases = [
        # Empty content
        ("", "(empty message)"),
        
        # Calendar invite
        (
            "Accepted: Meeting @ Weekly from 2pm to 2:30pm\nWhen: Wednesday\nWhere: Google Meet",
            "Accepted: Meeting @ Weekly from 2pm to 2:30pm\nWhen: Wednesday\nWhere: Google Meet"
        ),
        
        # Google Docs notification
        (
            "New activity in document\nProject Notes\n(2 comments\nUser1: Comment text\nUser2: Reply text",
            "New activity in document: Project Notes\n  • User1: Comment text\n  • User2: Reply text"
        ),
        
        # Regular email with signature
        (
            "Hello,\nThis is the message.\n\nBest regards,\nJohn\nTitle | Company\nPhone | Email",
            "Hello,\nThis is the message."
        ),
        
        # Email with quotes
        (
            "Yes, that works.\n\nOn Mon, Dec 9, User wrote:\n> Can we meet tomorrow?\n> Thanks",
            "Yes, that works."
        )
    ]
    
    console.print("\n[cyan]Running Content Cleaning Tests[/cyan]")
    for i, (input_text, expected) in enumerate(test_cases, 1):
        result = clean_content(input_text)
        success = result.strip() == expected.strip()
        console.print(f"\n[yellow]Test {i}:[/yellow] {'✓' if success else '✗'}")
        if not success:
            console.print("[red]Expected:[/red]")
            console.print(expected)
            console.print("[red]Got:[/red]")
            console.print(result)


def is_user_name(name: str) -> bool:
    """Check if name belongs to user.
    
    1. Clean name
    2. Check variants
    v2
    """
    name_lower = name.lower()
    return any(un in name_lower for un in USER_NAMES)


def find_names(text: str, known_names: Set[str]) -> List[tuple]:
    """Find name occurrences in text.
    
    1. Apply regex patterns
    2. Match known names
    3. Return positions
    v3
    """
    matches = []
    
    # Common words to exclude (case-insensitive)
    exclude_words = {
        'new', 'first', 'also', 'yes', 'see', 'nice', 'awesome',
        'thanks', 'hi', 'hello', 'hey', 'dear', 'draft', 'this',
        'when', 'what', 'why', 'how', 'who', 'where',
        'the', 'and', 'but', 'or', 'if', 'then', 'else',
        'just', 'very', 'quite', 'rather', 'only', 'such',
        'please', 'thank', 'thanks', 'best', 'regards',
        'forward', 'reply', 'sent', 'received', 'attachment',
        'file', 'document', 'meeting', 'call', 'update',
        'google', 'sheets', 'gmail', 'email', 'mail'
    }
    
    # First check known names (exact matches only)
    for name in sorted(known_names, key=len, reverse=True):
        # Clean up name
        name = name.split(' (Google')[0]  # Remove "(Google Sheets)" etc.
        name = name.split(' via ')[0]     # Remove "via LinkedIn" etc.
        
        # Skip single word matches against exclusion list
        if ' ' not in name and name.lower() in exclude_words:
            continue
            
        # Create pattern that requires word boundaries
        pattern = r'\b' + re.escape(name) + r'\b'
        for match in re.finditer(pattern, text, re.IGNORECASE):
            # Don't match if part of a larger name
            if not any(start <= match.start() < end for start, end, _ in matches):
                matches.append((match.start(), match.end(), name))
    
    return sorted(matches)


def format_content(content: str, names: Set[str], is_from_user: bool = False) -> Text:
    """Format content with Rich Text.
    
    1. Find names
    2. Apply styles
    3. Return Rich Text
    v3
    """
    text = Text(content)
    
    # Find all name occurrences
    matches = find_names(content, names)
    
    # Apply styles in reverse to preserve positions
    for start, end, name in reversed(matches):
        style = "bold magenta" if is_user_name(name) else "bold blue"
        text.stylize(style, start, end)
    
    return text


def clean_content(content: str) -> str:
    """Clean email content.
    
    1. Handle empty content
    2. Detect message type
    3. Clean appropriately
    v6
    """
    if not content:
        return "(empty message)"
    
    # Detect message type
    is_calendar = bool(re.search(r'(Accepted|Declined|Tentative|Updated):.+?@.+?(am|pm)', content, re.I))
    is_gdocs = bool(re.search(r'New activity in.+(document|spreadsheet)', content, re.I))
    
    # Handle calendar invites
    if is_calendar:
        # Extract key calendar information
        lines = content.split('\n')
        calendar_info = []
        for line in lines:
            if any(key in line for key in [':', '@', 'When:', 'Where:']):
                calendar_info.append(line.strip())
        return '\n'.join(calendar_info)
    
    # Handle Google Docs notifications
    if is_gdocs:
        lines = content.split('\n')
        doc_info = []
        
        # Get document name
        for line in lines:
            if 'in document' in line.lower() or 'in spreadsheet' in line.lower():
                continue
            if line.strip() and not line.startswith(('(', '.')):
                doc_info.append(f"New activity in document: {line.strip()}")
                break
        
        # Get comments
        comments = []
        for i, line in enumerate(lines):
            if ':' in line and not any(x in line.lower() for x in ['http:', 'https:', 'in document:', 'in spreadsheet:']):
                comments.append(f"  • {line.strip()}")
        
        if comments:
            doc_info.extend(comments)
        
        return '\n'.join(doc_info)
    
    # Regular email cleaning
    # Remove signatures and quoted text
    text = remove_quotes(content)
    
    # Clean up formatting
    text = text.replace('\r\n', '\n').strip()
    text = '\n'.join(line.strip() for line in text.split('\n') if line.strip())
    
    # Remove HTML artifacts
    text = re.sub(r'<!DOCTYPE[^>]*>', '', text, flags=re.I)
    text = re.sub(r'<[^>]+>', ' ', text)
    text = re.sub(r'https?://\S+', '[link]', text)
    
    # Remove only exact matches of noise patterns
    noise = [
        '[image: Your profile picture]',
        '---------- Forwarded message ---------',
        '----------',
        '---------',
    ]
    for n in noise:
        text = text.replace(n, '')
    
    # Clean up multiple spaces and lines
    text = re.sub(r'\s{3,}', ' ', text)
    text = text.strip()
    
    # If content is too short after cleaning, return original with minimal cleaning
    if len(text) < 10 and content:
        text = content.replace('\r\n', '\n').strip()
        text = re.sub(r'\s{3,}', ' ', text)
        return text
    
    return text if text else "(empty message)"


def format_date(date_str: str, now: Optional[datetime] = None) -> str:
    """Format date string for display.
    
    1. Parse date string
    2. Add days ago
    3. Format nicely
    v3
    """
    try:
        # Parse various date formats
        for fmt in [
            "%a, %d %b %Y %H:%M:%S %z",
            "%a, %d %b %Y %H:%M:%S %Z",
            "%d %b %Y %H:%M:%S %z",
        ]:
            try:
                dt = datetime.strptime(date_str.split('(')[0].strip(), fmt)
                if now is None:
                    now = datetime.now(dt.tzinfo)
                days_ago = (now - dt).total_seconds() / 86400
                days_str = f"({int(days_ago)}d ago)"
                return f"{days_str}{dt.strftime('%a-%d-%b-%y %H:%M')}"
            except ValueError:
                continue
        return date_str
    except Exception:
        return date_str


def get_thread_stats(messages: List[Dict]) -> Dict:
    """Get statistics for a thread.
    
    1. Count messages
    2. Calculate time span
    3. Get participant info
    v4
    """
    if not messages:
        return {}
    
    # Get dates
    dates = []
    for msg in messages:
        try:
            dt = datetime.strptime(
                msg['date'].split('(')[0].strip(),
                "%a, %d %b %Y %H:%M:%S %z"
            )
            dates.append(dt)
        except ValueError:
            continue
    
    if not dates:
        return {}
    
    # Get participants and message counts
    participants = {}
    for msg in messages:
        name = msg['from'].split('<')[0].strip(' "\'')
        # Clean up 'via' names
        if ' via ' in name:
            name = name.split(' via ')[0]
        # Clean up Google Sheets names
        if ' (Google' in name:
            name = name.split(' (Google')[0]
        participants[name] = participants.get(name, 0) + 1
    
    # Sort participants by message count
    sorted_participants = sorted(
        participants.items(),
        key=lambda x: x[1],
        reverse=True
    )
    
    # Calculate durations
    now = datetime.now(max(dates).tzinfo)
    thread_start = min(dates)
    thread_end = max(dates)
    duration = (thread_end - thread_start).total_seconds() / 86400  # days
    days_since = (now - thread_end).total_seconds() / 86400  # days
    
    return {
        'message_count': len(messages),
        'participants': len(participants),
        'duration_days': round(duration, 1),
        'days_since_last': round(days_since, 1),
        'participant_list': sorted_participants,
        'start_date': thread_start,
        'end_date': thread_end
    }


def analyze_threads(max_messages: int = 10, days_back: int = 7) -> None:
    """Analyze Gmail threads with rich formatting.
    
    1. Get unread messages
    2. Format content
    3. Show statistics
    v1
    """
    console.print("\n[cyan]Analyzing Gmail Threads[/cyan]")
    console.print("=" * 50)
    
    # Get messages
    console.print("\nFetching recent messages...")
    messages = gmail_service.list_unread_messages(
        max_results=max_messages,
        days_back=days_back
    )
    
    if not messages:
        console.print("[yellow]No unread messages found.[/yellow]")
        return
    
    # Get all participant names
    names = set()
    for msg in messages:
        names.add(msg['from'].split('<')[0].strip())
        if 'to' in msg:
            names.update(to.split('<')[0].strip() for to in msg['to'].split(','))
    
    # Show threads
    for msg in messages:
        thread_messages = gmail_service.get_thread_messages(msg['thread_id'])
        if not thread_messages:
            continue
        
        # Get thread stats
        stats = get_thread_stats(thread_messages)
        if not stats:
            continue
        
        # Show thread header
        console.print(f"\n[magenta]{'=' * 80}[/magenta]")
        console.print(f"[cyan]Thread:[/cyan] {msg['subject']}")
        console.print(
            f"[yellow]{stats['message_count']} messages[/yellow] • "
            f"[yellow]{stats['participants']} participants[/yellow] • "
            f"Span: [yellow]{stats['duration_days']:.1f} days[/yellow] • "
            f"Last: [yellow]{stats['days_since_last']:.1f} days ago[/yellow]"
        )
        
        # Show participants
        console.print("\n[cyan]Participants:[/cyan]")
        for name, count in stats['participant_list']:
            style = "green" if is_user_name(name) else "yellow"
            console.print(f"  [{style}]{name}[/{style}]: {count} messages")
        
        # Show messages
        console.print("\n[cyan]Messages:[/cyan]")
        for thread_msg in thread_messages:
            # Format sender
            sender = thread_msg['from'].split('<')[0].strip()
            style = "green" if is_user_name(sender) else "yellow"
            
            # Format date
            date = format_date(thread_msg['date'])
            
            # Clean and format content
            content = clean_content(thread_msg['content'])
            formatted_content = format_content(content, names, is_user_name(sender))
            
            # Show message
            console.print(f"\n[{style}]{sender}[/{style}] • [blue]{date}[/blue]")
            console.print(formatted_content)


def main():
    """Main entry point.
    
    1. Run analysis
    2. Show report
    3. Handle input
    v1
    """
    try:
        analyze_threads(max_messages=10, days_back=7)
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled.[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")


if __name__ == '__main__':
    main()
 