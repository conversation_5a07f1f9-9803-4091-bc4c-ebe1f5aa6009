"""Email draft generation using OpenAI Assistant.

1. Generate email replies
2. Extract context insights
3. Manage templates
v2
"""
import os
import re
from typing import Dict, Optional, Tuple, List
from dotenv import load_dotenv
from openai import OpenAI
from .storage.context import EmailContext, context_manager


# Load environment variables
load_dotenv()


class DraftAssistant:
    """Handle email draft generation with OpenAI.
    
    1. Manage OpenAI assistant
    2. Generate replies
    3. Extract insights
    v2
    """
    
    def __init__(self):
        """Initialize draft assistant.
        
        1. Set up OpenAI client
        2. Initialize assistant
        v1
        """
        if not os.getenv("OPENAI_API_KEY"):
            raise ValueError("OPENAI_API_KEY must be set in environment")
        
        self.client = OpenAI()
        self.assistant_id = None
        self._setup_assistant()
    
    def _setup_assistant(self):
        """Set up or get existing assistant.
        
        1. Check for existing assistant
        2. Create new if needed
        v2
        """
        ASSISTANT_NAME = "Email Reply Assistant"
        ASSISTANT_INSTRUCTIONS = """You are an expert email assistant that helps draft replies and extract insights.

For each email, you will:
1. Analyze the content and context
2. Draft appropriate replies
3. Extract key insights and information
4. Suggest importance rating (1-5)
5. Generate relevant tags

Your replies should be:
1. Professional and contextually appropriate
2. Clear and concise
3. Maintain conversation context
4. Include relevant details from history

When extracting insights, focus on:
1. Key information for future reference
2. Action items and deadlines
3. Important relationships and connections
4. Recurring themes or patterns

Format your response EXACTLY as follows:
[DRAFT]
Your drafted reply here...
[/DRAFT]

[SUMMARY]
Brief summary here...
[/SUMMARY]

[INSIGHTS]
Key insights here...
[/INSIGHTS]

[IMPORTANCE]
Just the number 1-5, nothing else
[/IMPORTANCE]

[TAGS]
tag1, tag2, tag3
[/TAGS]
"""
        
        # Try to find existing assistant
        assistants = self.client.beta.assistants.list(
            order="desc",
            limit=100
        )
        
        for assistant in assistants.data:
            if assistant.name == ASSISTANT_NAME:
                self.assistant_id = assistant.id
                return
        
        # Create new assistant
        assistant = self.client.beta.assistants.create(
            name=ASSISTANT_NAME,
            instructions=ASSISTANT_INSTRUCTIONS,
            model="gpt-4-1106-preview"
        )
        self.assistant_id = assistant.id
    
    def analyze_and_draft(
        self,
        message_details: Dict,
        thread_context: Optional[List[EmailContext]] = None
    ) -> Tuple[str, EmailContext]:
        """Analyze email and generate draft reply.
        
        1. Process email and context
        2. Generate draft
        3. Extract insights
        v2
        """
        # Create thread with context
        thread = self.client.beta.threads.create()
        
        # Add context from thread history
        if thread_context:
            context_msg = "Previous conversation context:\n"
            for ctx in thread_context:
                context_msg += f"\nDate: {ctx.metadata.get('date')}\n"
                context_msg += f"Subject: {ctx.metadata.get('subject')}\n"
                context_msg += f"Summary: {ctx.summary}\n"
                context_msg += f"Insights: {ctx.insights}\n"
                context_msg += "-" * 40 + "\n"
            
            self.client.beta.threads.messages.create(
                thread_id=thread.id,
                role="user",
                content=context_msg
            )
        
        # Add current message
        self.client.beta.threads.messages.create(
            thread_id=thread.id,
            role="user",
            content=f"""Please analyze this email and draft a reply:

From: {message_details['from']}
Subject: {message_details['subject']}
Date: {message_details['date']}

Please provide your response in the specified format with [DRAFT], [SUMMARY], etc. sections.
Remember for [IMPORTANCE] to only provide a number 1-5, nothing else.
"""
        )
        
        # Run the assistant
        run = self.client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=self.assistant_id
        )
        
        # Wait for completion
        while run.status in ["queued", "in_progress"]:
            run = self.client.beta.threads.runs.retrieve(
                thread_id=thread.id,
                run_id=run.id
            )
        
        # Get the response
        messages = self.client.beta.threads.messages.list(
            thread_id=thread.id
        )
        
        # Parse assistant's response using sections
        response = messages.data[0].content[0].text.value
        sections = self._parse_sections(response)
        
        # Extract importance rating (ensure it's just a number)
        importance_str = sections.get('IMPORTANCE', '3')
        importance = self._parse_importance(importance_str)
        
        # Create context
        context = EmailContext(
            message_id=message_details['id'],
            thread_id=message_details['thread_id'],
            summary=sections.get('SUMMARY', ''),
            importance=importance,
            tags=sections.get('TAGS', '').split(', '),
            references=[],
            last_accessed=message_details['date'],
            insights={"key_points": sections.get('INSIGHTS', '')},
            metadata=message_details
        )
        
        # Store context
        context_manager.store_context(context)
        
        return sections.get('DRAFT', ''), context
    
    def _parse_sections(self, response: str) -> Dict[str, str]:
        """Parse sections from assistant response.
        
        1. Find section markers
        2. Extract content
        3. Return dict
        v2
        """
        sections = {}
        current_section = None
        current_content = []
        
        for line in response.split('\n'):
            if line.startswith('[') and line.endswith(']'):
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                    current_content = []
                current_section = line.strip('[]')
            elif current_section and line.strip() and not line.startswith('[/'):
                current_content.append(line)
        
        # Add last section
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()
        
        return sections
    
    def _parse_importance(self, importance_str: str) -> int:
        """Parse importance rating from string.
        
        1. Extract first number
        2. Validate range
        3. Return default if invalid
        v1
        """
        # Try to find first number in string
        match = re.search(r'\d+', importance_str)
        if match:
            value = int(match.group())
            if 1 <= value <= 5:
                return value
        
        # Default to medium importance
        return 3


# Global instance for easy access
draft_assistant = DraftAssistant() 