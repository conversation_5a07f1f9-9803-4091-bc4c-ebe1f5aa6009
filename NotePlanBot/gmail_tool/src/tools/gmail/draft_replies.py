"""Draft email replies using <PERSON> Sonnet.

1. Analyze email threads
2. Generate contextual replies
3. Include project context
v1
"""
import os
import asyncio
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from anthropic import AsyncAnthropic
from dotenv import load_dotenv
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from .service import GmailService
from .storage.project_store import ProjectContextStore
from .storage.context import EmailContext, context_manager
from .ui_viewthreads import clean_content, get_thread_stats, is_user_name


load_dotenv()
console = Console()


class ClaudeDraftAssistant:
    """Draft email replies using Claude.
    
    1. Analyze threads
    2. Retrieve context
    3. Generate drafts
    v1
    """
    
    def __init__(self):
        """Initialize assistant.
        
        1. Set up clients
        2. Load context
        v1
        """
        self.gmail = GmailService()
        self.project_store = ProjectContextStore()
        self.context_manager = context_manager
        self.client = AsyncAnthropic(
            api_key=os.getenv("ANTHROPIC_API_KEY")
        )
        
        # User context
        self.user_email = "<EMAIL>"
        self.user_name = "<PERSON>"
        self.user_role = "R&D Lead at Invisible"
    
    async def retrieve_project_context(self, thread_messages: List[Dict]) -> str:
        """Retrieve relevant project context.
        
        1. Extract project mentions
        2. Find relevant docs
        3. Return context
        v1
        """
        # Extract unique senders and content
        people = set()
        content_pieces = []
        
        for msg in thread_messages:
            sender_email = msg['from'].split('<')[-1].strip('>') if '<' in msg['from'] else msg['from']
            people.add(sender_email)
            content_pieces.append(clean_content(msg['content'])[:200])
        
        # Find related projects
        related_projects = []
        for email in people:
            projects = self.project_store.get_person_projects(email)
            related_projects.extend(projects)
        
        related_projects = list(set(related_projects))
        
        # Build context
        context_parts = []
        
        if related_projects:
            context_parts.append(f"Related Projects: {', '.join(related_projects)}")
            
            # Get project details
            for project_name in related_projects[:2]:  # Top 2 projects
                project_ctx = self.project_store.get_project_context(project_name)
                if project_ctx:
                    project = project_ctx['project']
                    context_parts.append(f"\n{project.name}: {project.description}")
                    
                    # Add RACI info
                    raci_info = []
                    for entry in project_ctx['raci'][:3]:
                        raci_info.append(f"{entry.person_name} ({entry.role})")
                    if raci_info:
                        context_parts.append(f"Team: {', '.join(raci_info)}")
        
        # Search for relevant documents
        search_query = " ".join(content_pieces[:3])
        relevant_docs = self.project_store.search_documents(search_query)[:2]
        
        if relevant_docs:
            context_parts.append("\nRelevant Documentation:")
            for doc in relevant_docs:
                context_parts.append(f"- {doc.doc_type}: {doc.summary[:100]}...")
        
        return "\n".join(context_parts) if context_parts else "No specific project context found."
    
    async def analyze_thread(self, thread_messages: List[Dict]) -> Dict:
        """Analyze thread for reply context.
        
        1. Extract key info
        2. Determine reply needed
        3. Get context
        v1
        """
        if not thread_messages:
            return None
        
        # Get thread stats
        stats = get_thread_stats(thread_messages)
        
        # Check if we sent the last message
        last_msg = thread_messages[-1]
        last_sender = last_msg['from'].split('<')[0].strip()
        we_sent_last = is_user_name(last_sender)
        
        # Extract key points from recent messages
        recent_messages = thread_messages[-3:]
        key_points = []
        
        for msg in recent_messages:
            sender = msg['from'].split('<')[0].strip()
            content = clean_content(msg['content'])
            if content and not is_user_name(sender):
                # Look for questions
                if '?' in content:
                    questions = [line.strip() for line in content.split('\n') if '?' in line]
                    key_points.extend(questions[:2])
                
                # Look for action items
                action_keywords = ['can you', 'could you', 'please', 'need', 'want', 'would you']
                for keyword in action_keywords:
                    if keyword in content.lower():
                        sentences = content.split('.')
                        for sent in sentences:
                            if keyword in sent.lower():
                                key_points.append(sent.strip())
                                break
        
        # Get project context
        project_context = await self.retrieve_project_context(thread_messages)
        
        return {
            'thread_id': thread_messages[0].get('thread_id'),
            'subject': thread_messages[0].get('subject', 'No Subject'),
            'participants': stats['participant_list'],
            'message_count': stats['message_count'],
            'we_sent_last': we_sent_last,
            'last_sender': last_sender,
            'last_message_ago': stats['days_since_last'],
            'key_points': key_points[:3],
            'project_context': project_context,
            'thread_messages': thread_messages
        }
    
    async def generate_draft(self, thread_analysis: Dict) -> str:
        """Generate draft reply using Claude.
        
        1. Build prompt
        2. Call Claude
        3. Return draft
        v1
        """
        # Build thread summary
        thread_summary = []
        for msg in thread_analysis['thread_messages'][-5:]:  # Last 5 messages
            sender = msg['from'].split('<')[0].strip()
            date = msg['date'].split(',')[0] if ',' in msg['date'] else msg['date']
            content = clean_content(msg['content'])[:300]
            thread_summary.append(f"{sender} ({date}):\n{content}\n")
        
        prompt = f"""You are drafting an email reply as {self.user_name}, {self.user_role}.

Thread Subject: {thread_analysis['subject']}
Participants: {', '.join([f"{name} ({count} msgs)" for name, count in thread_analysis['participants'][:3]])}
Last Message From: {thread_analysis['last_sender']} ({thread_analysis['last_message_ago']:.1f} days ago)

Key Points to Address:
{chr(10).join(f"- {point}" for point in thread_analysis['key_points']) if thread_analysis['key_points'] else "- No specific questions or action items identified"}

Project Context:
{thread_analysis['project_context']}

Recent Thread History:
{chr(10).join(thread_summary)}

Draft a professional, concise reply that:
1. Addresses any questions or action items
2. Maintains the appropriate tone for the conversation
3. Is helpful and moves the conversation forward
4. Reflects Alex's role as R&D Lead at Invisible
5. Uses the project context appropriately if relevant

Keep the reply focused and under 150 words unless more detail is specifically needed.
Do not include subject line or email headers, just the body text."""

        try:
            response = await self.client.messages.create(
                model="claude-sonnet-4-20250514",
                max_tokens=500,
                temperature=0.7,
                messages=[{"role": "user", "content": prompt}]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            console.print(f"[red]Error generating draft: {e}[/red]")
            return None
    
    async def draft_replies_for_threads(self, max_threads: int = 5) -> List[Tuple[Dict, str]]:
        """Draft replies for recent threads.
        
        1. Get recent threads
        2. Analyze each
        3. Generate drafts
        v1
        """
        # Get recent unread messages
        messages = self.gmail.list_unread_messages(
            max_results=max_threads * 3,
            days_back=7
        )
        
        if not messages:
            return []
        
        # Group by thread
        seen_threads = set()
        threads_to_process = []
        
        for msg in messages:
            thread_id = msg['thread_id']
            if thread_id not in seen_threads:
                seen_threads.add(thread_id)
                threads_to_process.append(thread_id)
                if len(threads_to_process) >= max_threads:
                    break
        
        # Process threads
        drafts = []
        
        with console.status("[cyan]Analyzing threads and generating drafts...[/cyan]") as status:
            for i, thread_id in enumerate(threads_to_process):
                status.update(f"[cyan]Processing thread {i+1}/{len(threads_to_process)}...[/cyan]")
                
                # Get full thread
                thread_messages = self.gmail.get_thread_messages(thread_id)
                if not thread_messages:
                    continue
                
                # Analyze thread
                analysis = await self.analyze_thread(thread_messages)
                if not analysis or analysis['we_sent_last']:
                    continue
                
                # Generate draft
                draft = await self.generate_draft(analysis)
                if draft:
                    drafts.append((analysis, draft))
        
        return drafts
    
    def display_drafts(self, drafts: List[Tuple[Dict, str]]) -> None:
        """Display draft replies.
        
        1. Show thread info
        2. Display draft
        3. Allow editing
        v1
        """
        if not drafts:
            console.print("[yellow]No threads need replies.[/yellow]")
            return
        
        console.print(f"\n[cyan]Generated {len(drafts)} draft replies:[/cyan]\n")
        
        for i, (analysis, draft) in enumerate(drafts):
            console.print(f"[bold]{i+1}. {analysis['subject']}[/bold]")
            console.print(f"   From: {analysis['last_sender']}")
            console.print(f"   {analysis['message_count']} messages, last {analysis['last_message_ago']:.1f} days ago")
            
            if analysis['key_points']:
                console.print("   [yellow]Key points:[/yellow]")
                for point in analysis['key_points']:
                    console.print(f"   - {point[:60]}...")
            
            console.print("\n   [green]Draft Reply:[/green]")
            console.print(f"   {draft}\n")
            console.print("   " + "-" * 60 + "\n")


async def main():
    """Main entry point.
    
    1. Create assistant
    2. Generate drafts
    3. Display results
    v1
    """
    # Check for API key
    if not os.getenv("ANTHROPIC_API_KEY"):
        console.print("[red]Error: ANTHROPIC_API_KEY not set in .env file[/red]")
        console.print("Get your API key from: https://console.anthropic.com/api-keys")
        return
    
    assistant = ClaudeDraftAssistant()
    
    console.print("[cyan]Analyzing recent email threads...[/cyan]\n")
    
    # Generate drafts
    drafts = await assistant.draft_replies_for_threads(max_threads=5)
    
    # Display results
    assistant.display_drafts(drafts)
    
    # TODO: Add interactive selection and sending


if __name__ == '__main__':
    asyncio.run(main())