"""Test the OpenAI message drafter functionality.

1. Test initialization
2. Test basic message drafting
v2
"""
import os
from message_drafter import create_default_drafter


def test_drafter():
    """Test basic message drafter functionality.
    
    1. Create drafter
    2. Test meeting message
    3. Test evening plan
    v2
    """
    # 1. Create drafter (use dry run if no API key)
    use_dry_run = not bool(os.getenv("OPENAI_API_KEY"))
    if use_dry_run:
        print("\nRunning in dry run mode (no OpenAI API key found)")
    drafter = create_default_drafter(dry_run=use_dry_run)
    
    # 2. Test meeting message
    event = {"summary": "Coffee with <PERSON>", "start": "2pm", "duration": "1h"}
    meeting_msg = drafter.draft_meeting_message(event)
    print("\nMeeting message test:")
    print(meeting_msg)
    
    # 3. Test evening plan
    availability = {"start": "6pm", "end": "10pm", "preferences": "casual dinner"}
    evening_msg = drafter.draft_evening_plan(availability)
    print("\nEvening plan test:")
    print(evening_msg)


if __name__ == "__main__":
    test_drafter() 