from typing import List
from models.event import Event
from .base import EventFilter


class TitlePatternFilter(EventFilter):
    """Filter events based on title patterns.
    
    1. Check for exact matches
    2. Check for contains matches
    3. Handle case sensitivity
    v2
    """
    def __init__(self, name: str, patterns: List[str], description: str = "", 
                 case_sensitive: bool = False, priority: int = 0):
        super().__init__(name=name, description=description, priority=priority)
        self.patterns = patterns
        self.case_sensitive = case_sensitive
    
    def matches(self, event: Event) -> bool:
        title = event.title
        if not self.case_sensitive:
            title = title.lower()
            patterns = [p.lower() for p in self.patterns]
        else:
            patterns = self.patterns
            
        return any(p in title for p in patterns) 