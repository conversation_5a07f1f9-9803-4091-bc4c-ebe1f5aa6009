from typing import Optional
from models.event import Event
from .base import EventFilter


class MetadataFilter(EventFilter):
    """Filter events based on metadata properties.
    
    1. Check transparency
    2. Check attendee count
    3. Check creator/organizer
    v2
    """
    def __init__(self, name: str, description: str = "", priority: int = 0,
                 transparency: Optional[str] = None,
                 min_attendees: Optional[int] = None,
                 max_attendees: Optional[int] = None,
                 creator_email: Optional[str] = None,
                 min_duration: Optional[int] = None,
                 max_duration: Optional[int] = None):
        super().__init__(name=name, description=description, priority=priority)
        self.transparency = transparency
        self.min_attendees = min_attendees
        self.max_attendees = max_attendees
        self.creator_email = creator_email
        self.min_duration = min_duration
        self.max_duration = max_duration
    
    def matches(self, event: Event) -> bool:
        if self.transparency and event.transparency == self.transparency:
            return True
        
        if self.min_attendees is not None and event.attendee_count < self.min_attendees:
            return False
        if self.max_attendees is not None and event.attendee_count > self.max_attendees:
            return False
        
        if self.creator_email and event.creator_email == self.creator_email:
            return True
        
        if (self.min_duration is not None and event.duration_minutes and 
                event.duration_minutes < self.min_duration):
            return False
        if (self.max_duration is not None and event.duration_minutes and 
                event.duration_minutes > self.max_duration):
            return False
        
        return False


class RecurringFilter(EventFilter):
    """Filter recurring events.
    
    1. Check recurrence rules
    2. Check series status
    v2
    """
    def matches(self, event: Event) -> bool:
        return event.is_recurring 