from dataclasses import dataclass
from models.event import Event


@dataclass
class EventFilter:
    """Base class for event filters.
    
    1. Define filter attributes
    2. Implement matching logic
    3. Track metadata
    v1
    """
    name: str
    description: str
    priority: int = 0  # Higher priority filters are checked first
    
    def matches(self, event: Event) -> bool:
        """Base match method - should be overridden."""
        return False 