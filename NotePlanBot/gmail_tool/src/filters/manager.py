from typing import List, Dict
from models.event import Event
from .base import <PERSON><PERSON>ilter
from .title import TitlePatternFilter
from .metadata import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RecurringFilter


class FilterManager:
    """Manage and apply event filters.
    
    1. Store filters
    2. Apply filters to events
    3. Track filter stats
    v1
    """
    def __init__(self):
        self.filters: List[EventFilter] = []
        self.stats: Dict[str, int] = {}  # Track how many events each filter catches
    
    def add_filter(self, filter_: EventFilter):
        """Add a filter and sort by priority."""
        self.filters.append(filter_)
        self.filters.sort(key=lambda x: x.priority, reverse=True)
    
    def get_filter_matches(self, event: Event) -> List[str]:
        """Get names of all filters that match an event."""
        matches = []
        for filter_ in self.filters:
            if filter_.matches(event):
                matches.append(filter_.name)
                self.stats[filter_.name] = self.stats.get(filter_.name, 0) + 1
        return matches


def create_default_filters() -> FilterManager:
    """Create default set of filters.
    
    1. Create basic filters
    2. Add metadata filters
    3. Return manager
    v2
    """
    manager = FilterManager()
    
    # Sleep patterns
    manager.add_filter(TitlePatternFilter(
        name="sleep",
        patterns=["😴", "sleep", "asleep"],
        description="Sleep-related events",
        priority=100
    ))
    
    # Focus/Reserved time
    manager.add_filter(TitlePatternFilter(
        name="focus_time",
        patterns=["focus", "reserved", "block", "focused work", "focused", "focus block", "reserved for focused"],
        description="Focus and reserved time blocks",
        priority=90
    ))
    
    # Out of office
    manager.add_filter(TitlePatternFilter(
        name="ooo",
        patterns=["out of office", "ooo", "off"],
        description="Out of office time",
        priority=80
    ))
    
    # Free time
    manager.add_filter(MetadataFilter(
        name="free",
        description="Events marked as free/transparent",
        transparency="transparent",
        priority=70
    ))
    
    # Solo events
    manager.add_filter(MetadataFilter(
        name="solo",
        description="Events with no other attendees",
        max_attendees=1,
        priority=60
    ))
    
    # Daily recurring
    manager.add_filter(RecurringFilter(
        name="recurring",
        description="Recurring calendar events",
        priority=50
    ))
    
    # Morning routine
    manager.add_filter(TitlePatternFilter(
        name="routine",
        patterns=["📧", "morning", "catch up", "laundry", "pack"],
        description="Regular routine tasks",
        priority=40
    ))
    
    # Optional events
    manager.add_filter(TitlePatternFilter(
        name="optional",
        patterns=["optional", "if needed", "?"],
        description="Optional or tentative events",
        priority=30
    ))
    
    # Short meetings
    manager.add_filter(MetadataFilter(
        name="quick_sync",
        description="Short sync meetings",
        max_duration=30,
        priority=20
    ))
    
    # Long meetings
    manager.add_filter(MetadataFilter(
        name="long_meeting",
        description="Long meetings",
        min_duration=90,
        priority=10
    ))
    
    return manager 