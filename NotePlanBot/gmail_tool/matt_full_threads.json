{"19819f2981f148b2": {"id": "19819f2981f148b2", "historyId": "2539657", "messages": [{"id": "19819f2981f148b2", "threadId": "19819f2981f148b2", "labelIds": ["Label_19", "UNREAD", "Label_40", "IMPORTANT", "CATEGORY_PERSONAL", "INBOX"], "snippet": "Team, as we start to build this out lets include <PERSON> and <PERSON>, they have some really cool capabilities we can add in here <PERSON> Chief Executive Officer • Invisible Technologies Matt@", "payload": {"partId": "", "mimeType": "multipart/related", "filename": "", "headers": [{"name": "Delivered-To", "value": "<EMAIL>"}, {"name": "Received", "value": "by 2002:a05:6200:4498:b0:5ad:7480:a0e4 with SMTP id w24csp894301qnz;        Thu, 17 Jul 2025 12:53:06 -0700 (PDT)"}, {"name": "X-Forwarded-Encrypted", "value": "i=2; AJvYcCUUEn0Z6mWwvYZAy+C0y0QR52XdBC0Qd03UQ8/7UyU/THNx7hDlWUocvxh8qHXOi324XYsK5c7IaI5WHA==@invisible.email"}, {"name": "X-Received", "value": "by 2002:a05:690c:2602:b0:714:586:86fc with SMTP id 00721157ae682-71836ca2c4dmr116073567b3.16.1752781986144;        <PERSON><PERSON>, 17 Jul 2025 12:53:06 -0700 (PDT)"}, {"name": "ARC-Seal", "value": "i=1; a=rsa-sha256; t=1752781986; cv=none;        d=google.com; s=arc-20240605;        b=PXvP/Tlf/XNBtRX3shcUDJGkB8AxlHLGLkfSwzMsadIzhylaKR9h9u80CaSoHAUBrP         pV572uzzqh0sxYwZYM3yaBXqFVzoNnI/dD50FkiLj39q2S5bxBQnf8oTOyY0yrp+ooeD         /h4nSkQcblaHU+sBYrC4+V6L9/9odscpZ5oQMD2AC+9LYsUFd5BYV479+mrXI43uipC1         /bhOlVda2er057WnZ8oLIlbVlEmAB7Xs7YxH4+28iQokEu/HdeBr9PtL9nM3dpizRgYZ         UdEYdRTtfQuGfvPd/GjDv1Ez2QBXrSmv+fs3ioV8WxEokD2AHaPdmDohymRQFbR+jekQ         ER0g=="}, {"name": "ARC-Message-Signature", "value": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;        h=cc:to:subject:message-id:date:thread-index:mime-version:in-reply-to         :references:from:dkim-signature;        bh=8iIBFRAjyjaWP4g/r+PcGdvQZAHx7qedvZmL1cJNygU=;        fh=hN9QDx7t/id/wcmg+GtqkJ0hHsCn9UQxH+25STQyoa0=;        b=L9eLyMKVJPtCIgqa2VDNRrc6ANuVwkAp6Nb3dJ2nB1TJhNvZzb3oGrICwa9rzmQtXv         aEL0HnbY9ib1W43q0csWclGe7lz5of4icV+7lpTCkyqk2UNlC75ixuv/sTXpYhgHnrD3         jPS8DqeuBuISSY4Dlhop5JT367tCyWBaYq70O+kmcgDJvZQ2AcSYZifRWZdQEMCvWRSt         TP6QRntO6xC7P4UdxZMXQG6ubymnS+kzyJuclpKgVX9V0AbqJLKgecfsqJyjDd1oopYJ         rHx2eRkxM3Dyar/L45DvIJ1FI939vSPyd3hDUpcaXZrvZdShbq4dKS1F2U7ImlV0VDC1         QSZA==;        dara=google.com"}, {"name": "ARC-Authentication-Results", "value": "i=1; mx.google.com;       dkim=pass header.i=@invisible.email header.s=google header.b=fQfeptrP;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=invisible.email;       dara=neutral header.i=@invisible.email"}, {"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Received", "value": "from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])        by mx.google.com with SMTPS id 00721157ae682-717c5a7c95esor103806977b3.0.2025.***********.05        for <<EMAIL>>        (Google Transport Security);        Thu, 17 Jul 2025 12:53:06 -0700 (PDT)"}, {"name": "Received-SPF", "value": "pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;"}, {"name": "Authentication-Results", "value": "mx.google.com;       dkim=pass header.i=@invisible.email header.s=google header.b=fQfeptrP;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=invisible.email;       dara=neutral header.i=@invisible.email"}, {"name": "DKIM-Signature", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=invisible.email; s=google; t=1752781985; x=1753386785; darn=invisible.email;        h=cc:to:subject:message-id:date:thread-index:mime-version:in-reply-to         :references:from:from:to:cc:subject:date:message-id:reply-to;        bh=8iIBFRAjyjaWP4g/r+PcGdvQZAHx7qedvZmL1cJNygU=;        b=fQfeptrPnudIn8GHBV8UThUlktXzZ8k+6Gsoy0iojG4bmqIFJJ7zfsj0Pc6dJtptDK         2ril9bUdAHqEIJ7tP8NQ5Tv9uL9w9E7eaZzXlxdcJkJuHB6LBwssmid9nVCVk8gdqsnK         0Ub1sNUP8CnlwkgFHyUTbBTUOIIN4tuSa88ew="}, {"name": "X-Google-DKIM-Signature", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20230601; t=1752781985; x=1753386785;        h=cc:to:subject:message-id:date:thread-index:mime-version:in-reply-to         :references:from:x-gm-message-state:from:to:cc:subject:date         :message-id:reply-to;        bh=8iIBFRAjyjaWP4g/r+PcGdvQZAHx7qedvZmL1cJNygU=;        b=rhvBIgpwf+ZzIpu8dkMYLj74zZsH1Fz5aXTHar5aOYwPAFswd9BgJEujDvojbGIRzc         MVkHyQQSE1Ted67lC1TEm2VyyfZbVzkbVjplxA5mNrucxjGO4UWaQJvHQgZw90S9Tqbj         v+a//Jk1G56cvvqI7zx8aSPbGVN1eF+7/afBi108+szjh+SD+d482ilkbY0rzo+7KWQi         qcdC1xB7kKs0VptZqJCnImKe1U9WeX/DII9ZhQ/so6iOMBF4dGJko8OZCRU8vDRC20qW         +0cB5U+0sYHoB1pjYzQmNJQ59W/VEzUIbT8NHYGFeYDY+7HuScMkG4nuepq1r7udIMkG         z+5g=="}, {"name": "X-Forwarded-Encrypted", "value": "i=1; AJvYcCW5dC9IteKzGRJjptpZVoW2cLDgqOWcwtDHv49iBt3zULcipXVlPvAqLhck7T7whqYKPkRXQthL4zIhpA==@invisible.email"}, {"name": "X-Gm-Message-State", "value": "AOJu0YxM6+AvkKICXtRPFMapHRm4eHx3A8h3nw5UFxOI5Jyr9H5eaN41 KqIYSr/J6tyuBOAgGYyeLrdt6oBaPzUqahHYfge1wBemDMvQMgsFBLa0yi10Hee5T778yB7DnCO 2e34GxkAphIVrWx9433bQM3Kw+hDHC59SBvAZpVKxQI0L"}, {"name": "X-Gm-Gg", "value": "ASbGncvcahAnT1SxVYOIJz8eAwYDOdTqUvjHTgWwef3f/31cIK65ZpSmbmQGeZG3TVm s9+/SHTXeXf4YTMzxV1p9M/ChmKHCWjHFRN0mSg8b1/8FZRjYBZHZ3TdKBajZrYKkvd5OAHv2Tk M94vOTXfS5nMtYE/octdRZmx0FCDTDPrTLhOmmlRFP4+9/XWKlhOvj23yRru0cnwLprsw0YMlbB tAoOGY="}, {"name": "X-Google-Smtp-Source", "value": "AGHT+IHLVPUbcaIox/zKdDOdWhD9Qf65hTCl6SXIdL5MrsOAoZkGIEcp/SkopJiGpWw664v9974fF2SHnMM1ZFa83+w="}, {"name": "X-Received", "value": "by 2002:a05:690c:22c6:b0:70e:142d:9c4e with SMTP id 00721157ae682-71837470738mr106219877b3.26.1752781985193; <PERSON><PERSON>, 17 Jul 2025 12:53:05 -0700 (PDT)"}, {"name": "From", "value": "<PERSON> <<EMAIL>>"}, {"name": "References", "value": "<<EMAIL>> <<EMAIL>>"}, {"name": "In-Reply-To", "value": "<<EMAIL>>"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "X-Mailer", "value": "Microsoft Outlook 16.0"}, {"name": "Thread-Index", "value": "AQIOaqVHc+l+zcPltIQCTg01kxTvsAHLboT7s8N3tLA="}, {"name": "Date", "value": "Thu, 17 Jul 2025 15:53:03 -0400"}, {"name": "X-Gm-Features", "value": "Ac12FXz8rgA3g3n_pdAnPyL3i-0thQJ1xQqohd66ZPzwPAL0SVFbL3gwQlzKPt4"}, {"name": "Message-ID", "value": "<<EMAIL>>"}, {"name": "Subject", "value": "RE: Invisible/HMC connect"}, {"name": "To", "value": "<PERSON> <<EMAIL>>"}, {"name": "Cc", "value": "<PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <ale<PERSON><PERSON>@invisible.email>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>"}, {"name": "Content-Type", "value": "multipart/related; boundary=\"000000000000998f1d063a255b9a\""}], "body": {"size": 0}, "parts": [{"partId": "0", "mimeType": "multipart/alternative", "filename": "", "headers": [{"name": "Content-Type", "value": "multipart/alternative; boundary=\"000000000000998f1c063a255b99\""}], "body": {"size": 0}, "parts": [{"partId": "0.0", "mimeType": "text/plain", "filename": "", "headers": [{"name": "Content-Type", "value": "text/plain; charset=\"UTF-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "body": {"size": 4703, "data": "VGVhbSwgYXMgd2Ugc3RhcnQgdG8gYnVpbGQgdGhpcyBvdXQgbGV0cyBpbmNsdWRlIEFsZXggYW5kIFdpbGwsIHRoZXkgaGF2ZQ0Kc29tZSByZWFsbHkgY29vbCBjYXBhYmlsaXRpZXMgd2UgY2FuIGFkZCBpbiBoZXJlDQoNCg0KDQoNCg0KPGh0dHBzOi8vd3d3LmludmlzaWJsZS5jby8-DQoNCipNYXR0IEZpdHpwYXRyaWNrKg0KDQpDaGllZiBFeGVjdXRpdmUgT2ZmaWNlciDigKIgSW52aXNpYmxlIFRlY2hub2xvZ2llcw0KDQpNYXR0QEludmlzaWJsZS5jbw0KDQpbaW1hZ2U6IFJhbmtlZCAjMiBmYXN0ZXN0IGdyb3dpbmcgQUkgY29tcGFueSBpbiBBbWVyaWNhIGluIHRoZSAyMDI0IEluYy4NCjUwMDBdIDxodHRwczovL3d3dy5pbnZpc2libGUuY28vPg0KDQpbaW1hZ2U6IExpbmtlZEluXQ0KPGh0dHBzOi8vd3d3LmxpbmtlZGluLmNvbS9jb21wYW55L2ludmlzaWJsZS10ZWNobm9sb2dpZXMtaW5jLS8-W2ltYWdlOiBYXQ0KPGh0dHBzOi8veC5jb20vaW52dGVjaGluYz5baW1hZ2U6IFlvdVR1YmVdDQo8aHR0cHM6Ly93d3cueW91dHViZS5jb20vQGludmlzaWJsZXRlY2hub2xvZ2llcz4NCg0KDQoNCg0KDQoqRnJvbToqIENhcmwgU2FuZGJlcmcgPGNhcmwuc2FuZGJlcmdAaW52aXNpYmxlLmVtYWlsPg0KKlNlbnQ6KiBXZWRuZXNkYXksIEp1bHkgMTYsIDIwMjUgMTA6MTUgUE0NCipUbzoqIE1hdHRoZXcgRml0enBhdHJpY2sgPG1hdHRAaW52aXNpYmxlLmVtYWlsPg0KKkNjOiogbmFkZWF1bUBobWMuaGFydmFyZC5lZHU7IHNtdXJueXl5QGhtYy5oYXJ2YXJkLmVkdTsNCm1hamV3c2tpZEBobWMuaGFydmFyZC5lZHU7IEFsZXhpdXMgV3JvbmthIDxhbGV4aXVzQGludmlzaWJsZS5lbWFpbD47DQpBbGVrc2VpIFNoa3VyaW4gPGFsZWtzZWlAaW52aXNpYmxlLmVtYWlsPjsgVXNoYSBSYW8NCjx1c2hhLnJhb0BpbnZpc2libGUuZW1haWw-OyBSaWNoYXJkIFp1cm9mZiA8cmljaGFyZC56dXJvZmZAaW52aXNpYmxlLmVtYWlsPjsNClh1ZXFpIENoYW5nIDx4dWVxaS5jaGFuZ0BpbnZpc2libGUuZW1haWw-DQoqU3ViamVjdDoqIFJlOiBJbnZpc2libGUvSE1DIGNvbm5lY3QNCg0KDQoNCk1pa2UtLUp1c3QgY2F1Z2h0IHVwIHdpdGggTWF0dCByZWdhcmRpbmcgdGhlIHVwZGF0ZWQgdGltZWxpbmUgb24geW91ciBzaWRlLg0KV2UncmUgbG9va2luZyBmb3J3YXJkIHRvIHRoZSBuZXh0IHN0ZXBzLg0KDQoNCg0KQ2FybA0KDQoNCg0KT24gV2VkLCBKdWwgMTYsIDIwMjUgYXQgOTo1N-KAr1BNIE1hdHRoZXcgRml0enBhdHJpY2sgPG1hdHRAaW52aXNpYmxlLmVtYWlsPg0Kd3JvdGU6DQoNCk1pa2UsIGFsbCwNCg0KDQoNCldlIHJlYWxseSBlbmpveWVkIHRoZSBjb252ZXJzYXRpb24gbGFzdCB3ZWVrLCB0aGFuayB5b3UgZm9yIHRha2luZyB0aGUgdGltZQ0KYW5kIEkgZGVmaW5pdGVseSB0aGluayBvdXIgcGxhdGZvcm1zIGNvdWxkIGJlIGhlbHBmdWwgZm9yIHNvbWUgb2YgdGhlIGRhdGENCmNoYWxsZW5nZXMgeW91IGFyZSBncmFwcGxpbmcgd2l0aA0KDQoNCg0KSSB3YW50ZWQgdG8gZm9sbG93IHVwIG9uIHRoZSBuZXh0IHN0ZXBzIHdlIGRpc2N1c3NlZCwgdG8gc3RhcnQgdG8gdGhlDQpwcm9jZXNzIG9uIGFuIGluaXRpYWwgZGVwbG95bWVudCBvZiBvdXIgZGF0YSBwbGF0Zm9ybSB0byBkZW1vbnN0cmF0ZQ0KZXh0cmFjdGluZyBleHBvc3VyZSBhbmQgcG9zaXRpb25hbCBkYXRhIGZyb20gbWFuYWdlciBsZXR0ZXJzLiBJIHRoaW5rIHdlDQpkaXNjdXNzZWQgMyBzcGVjaWZpYyBhY3Rpb25zDQoNCiAgIDEuIFdlIGNhbiBleGVjdXRlIGFuIE5EQSBpZiB5b3Ugc2VuZCBvdmVyDQogICAyLiBXZSBjYW4gc3RhcnQgdGhlIHByb2Nlc3Mgb2YgcGVybWlzc2lvbmluZyBvciBzZXR0aW5nIHVwIGFuIGVudmlyb25tZW50DQogICAzLiBZb3UgY2FuIGdpdmUgdXMgYSBzZXQgb2YgcmVsZXZhbnQgZG9jdW1lbnRzIHJlY2VpdmVkLCByZWRhY3RlZCBhcw0KICAgbmVlZGVkLCBhcyB3ZWxsIGFzIHRoZSB0YXJnZXQgc3RhdGUgb3V0cHV0cyB5b3Ugd2VyZSBzZWVraW5nIHRvIGRlbGl2ZXINCg0KDQoNCkxldCB1cyBrbm93IHRoZSBiZXN0IHdheSB0byBnZXQgc3RhcnRlZCBvbiB0aGlzPyBBIHN1YnNldCBvZiB0aGlzIGdyb3VwIGNhbg0KYWxzbyBqdW1wIG9uIHRoaXMgcGhvbmUgdG9tb3Jyb3cgYXMgbmVlZGVkIOKAkyBDYXJsIGNhbiBxdWFydGVyYmFjayBmcm9tIG91cg0Kc2lkZQ0KDQoNCg0KTWF0dA0KDQoNCg0KDQoNCjxodHRwczovL3d3dy5pbnZpc2libGUuY28vPg0KDQoqTWF0dCBGaXR6cGF0cmljayoNCg0KQ2hpZWYgRXhlY3V0aXZlIE9mZmljZXIg4oCiIEludmlzaWJsZSBUZWNobm9sb2dpZXMNCg0KTWF0dEBJbnZpc2libGUuY28NCg0KW2ltYWdlOiBSYW5rZWQgIzIgZmFzdGVzdCBncm93aW5nIEFJIGNvbXBhbnkgaW4gQW1lcmljYSBpbiB0aGUgMjAyNCBJbmMuDQo1MDAwXSA8aHR0cHM6Ly93d3cuaW52aXNpYmxlLmNvLz4NCg0KW2ltYWdlOiBMaW5rZWRJbl0NCjxodHRwczovL3d3dy5saW5rZWRpbi5jb20vY29tcGFueS9pbnZpc2libGUtdGVjaG5vbG9naWVzLWluYy0vPltpbWFnZTogWF0NCjxodHRwczovL3guY29tL2ludnRlY2hpbmM-W2ltYWdlOiBZb3VUdWJlXQ0KPGh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL0BpbnZpc2libGV0ZWNobm9sb2dpZXM-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"}}, {"partId": "0.1", "mimeType": "text/html", "filename": "", "headers": [{"name": "Content-Type", "value": "text/html; charset=\"UTF-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "body": {"size": 15422, "data": "PGh0bWw-PGhlYWQ-PG1ldGEgaHR0cC1lcXVpdj0iQ29udGVudC1UeXBlIiBjb250ZW50PSJ0ZXh0L2h0bWw7IGNoYXJzZXQ9dXRmLTgiPjxtZXRhIG5hbWU9IkdlbmVyYXRvciIgY29udGVudD0iTWljcm9zb2Z0IFdvcmQgMTUgKGZpbHRlcmVkIG1lZGl1bSkiPjxzdHlsZT48IS0tDQovKiBGb250IERlZmluaXRpb25zICovDQpAZm9udC1mYWNlDQoJe2ZvbnQtZmFtaWx5OkhlbHZldGljYTsNCglwYW5vc2UtMToyIDExIDYgNCAyIDIgMiAyIDIgNDt9DQpAZm9udC1mYWNlDQoJe2ZvbnQtZmFtaWx5OiJDYW1icmlhIE1hdGgiOw0KCXBhbm9zZS0xOjIgNCA1IDMgNSA0IDYgMyAyIDQ7fQ0KQGZvbnQtZmFjZQ0KCXtmb250LWZhbWlseTpDYWxpYnJpOw0KCXBhbm9zZS0xOjIgMTUgNSAyIDIgMiA0IDMgMiA0O30NCkBmb250LWZhY2UNCgl7Zm9udC1mYW1pbHk6QXB0b3M7fQ0KLyogU3R5bGUgRGVmaW5pdGlvbnMgKi8NCnAuTXNvTm9ybWFsLCBsaS5Nc29Ob3JtYWwsIGRpdi5Nc29Ob3JtYWwNCgl7bWFyZ2luOjBpbjsNCglmb250LXNpemU6MTIuMHB0Ow0KCWZvbnQtZmFtaWx5OiJBcHRvcyIsc2Fucy1zZXJpZjt9DQphOmxpbmssIHNwYW4uTXNvSHlwZXJsaW5rDQoJe21zby1zdHlsZS1wcmlvcml0eTo5OTsNCgljb2xvcjpibHVlOw0KCXRleHQtZGVjb3JhdGlvbjp1bmRlcmxpbmU7fQ0Kc3Bhbi5FbWFpbFN0eWxlMjANCgl7bXNvLXN0eWxlLXR5cGU6cGVyc29uYWwtcmVwbHk7DQoJZm9udC1mYW1pbHk6IkFwdG9zIixzYW5zLXNlcmlmOw0KCWNvbG9yOndpbmRvd3RleHQ7fQ0KLk1zb0NocERlZmF1bHQNCgl7bXNvLXN0eWxlLXR5cGU6ZXhwb3J0LW9ubHk7fQ0KQHBhZ2UgV29yZFNlY3Rpb24xDQoJe3NpemU6OC41aW4gMTEuMGluOw0KCW1hcmdpbjoxLjBpbiAxLjBpbiAxLjBpbiAxLjBpbjt9DQpkaXYuV29yZFNlY3Rpb24xDQoJe3BhZ2U6V29yZFNlY3Rpb24xO30NCi8qIExpc3QgRGVmaW5pdGlvbnMgKi8NCkBsaXN0IGwwDQoJe21zby1saXN0LWlkOjk5NTU3NDMwNDsNCgltc28tbGlzdC10ZW1wbGF0ZS1pZHM6Mjg0ODY2Mjg4O30NCm9sDQoJe21hcmdpbi1ib3R0b206MGluO30NCnVsDQoJe21hcmdpbi1ib3R0b206MGluO30NCi0tPjwvc3R5bGU-PC9oZWFkPjxib2R5IGxhbmc9IkVOLVVTIiBsaW5rPSJibHVlIiB2bGluaz0icHVycGxlIiBzdHlsZT0id29yZC13cmFwOmJyZWFrLXdvcmQiPjxkaXYgY2xhc3M9IldvcmRTZWN0aW9uMSI-PHAgY2xhc3M9Ik1zb05vcm1hbCI-VGVhbSwgYXMgd2Ugc3RhcnQgdG8gYnVpbGQgdGhpcyBvdXQgbGV0cyBpbmNsdWRlIEFsZXggYW5kIFdpbGwsIHRoZXkgaGF2ZSBzb21lIHJlYWxseSBjb29sIGNhcGFiaWxpdGllcyB3ZSBjYW4gYWRkIGluIGhlcmU8L3A-PHAgY2xhc3M9Ik1zb05vcm1hbCI-wqA8L3A-PHAgY2xhc3M9Ik1zb05vcm1hbCI-wqA8L3A-PHRhYmxlIGNsYXNzPSJNc29Ob3JtYWxUYWJsZSIgYm9yZGVyPSIwIiBjZWxsc3BhY2luZz0iMCIgY2VsbHBhZGRpbmc9IjAiIHdpZHRoPSIzNTAiIHN0eWxlPSJ3aWR0aDoyNjIuNXB0Ij48dHIgc3R5bGU9ImhlaWdodDoxNi41cHQiPjx0ZCBzdHlsZT0icGFkZGluZzowaW4gMGluIDEyLjBwdCAwaW47aGVpZ2h0OjE2LjVwdCI-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1hcmdpbjouMXB0Ij48YSBocmVmPSJodHRwczovL3d3dy5pbnZpc2libGUuY28vIiB0YXJnZXQ9Il9ibGFuayI-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMC4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7SGVsdmV0aWNhJnF1b3Q7LHNhbnMtc2VyaWY7Ym9yZGVyOm5vbmUgd2luZG93dGV4dCAxLjBwdDtwYWRkaW5nOjBpbjt0ZXh0LWRlY29yYXRpb246bm9uZSI-PGltZyBib3JkZXI9IjAiIHdpZHRoPSIxMjUiIGhlaWdodD0iMjIiIHN0eWxlPSJ3aWR0aDoxLjI5ODZpbjtoZWlnaHQ6LjIyOTFpbiIgaWQ9IlBpY3R1cmVfeDAwMjBfNDA2MTE2MTgiIHNyYz0iY2lkOmltYWdlMDAxLnBuZ0AwMURCRjczMi5FMENFNjYxMCI-PC9zcGFuPjwvYT48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEwLjBwdDtmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjtjb2xvcjojMGYwZjBmIj48L3NwYW4-PC9wPjwvdGQ-PC90cj48dHI-PHRkIHN0eWxlPSJwYWRkaW5nOjBpbiAwaW4gMGluIDBpbiI-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1hcmdpbjouMXB0O2xpbmUtaGVpZ2h0OjE4LjBwdCI-PGI-PHNwYW4gc3R5bGU9ImZvbnQtZmFtaWx5OiZxdW90O0hlbHZldGljYSZxdW90OyxzYW5zLXNlcmlmO2NvbG9yOiMwZjBmMGYiPk1hdHQgRml0enBhdHJpY2s8L3NwYW4-PC9iPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTAuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0hlbHZldGljYSZxdW90OyxzYW5zLXNlcmlmO2NvbG9yOiMwZjBmMGYiPjwvc3Bhbj48L3A-PC90ZD48L3RyPjx0cj48dGQgc3R5bGU9InBhZGRpbmc6MGluIDBpbiAwaW4gMGluIj48cCBjbGFzcz0iTXNvTm9ybWFsIiBzdHlsZT0ibWFyZ2luOi4xcHQ7bGluZS1oZWlnaHQ6MTUuMHB0Ij48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEwLjBwdDtmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjtjb2xvcjojMGYwZjBmIj5DaGllZiBFeGVjdXRpdmUgT2ZmaWNlciDigKIgSW52aXNpYmxlIFRlY2hub2xvZ2llczwvc3Bhbj48L3A-PC90ZD48L3RyPjx0cj48dGQgc3R5bGU9InBhZGRpbmc6MGluIDBpbiAwaW4gMGluIj48cCBjbGFzcz0iTXNvTm9ybWFsIiBzdHlsZT0ibWFyZ2luOi4xcHQ7bGluZS1oZWlnaHQ6MTUuMHB0Ij48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEwLjBwdDtmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjtjb2xvcjojMGYwZjBmIj48YSBocmVmPSJtYWlsdG86TWF0dEBJbnZpc2libGUuY28iIHRhcmdldD0iX2JsYW5rIj48c3BhbiBzdHlsZT0iY29sb3I6IzBmMGYwZiI-TWF0dEBJbnZpc2libGUuY288L3NwYW4-PC9hPjwvc3Bhbj48L3A-PC90ZD48L3RyPjx0ciBzdHlsZT0iaGVpZ2h0OjYwLjBwdCI-PHRkIHN0eWxlPSJwYWRkaW5nOjkuMHB0IDBpbiAwaW4gMGluO2hlaWdodDo2MC4wcHQiPjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtYXJnaW46LjFwdCI-PGEgaHJlZj0iaHR0cHM6Ly93d3cuaW52aXNpYmxlLmNvLyIgdGFyZ2V0PSJfYmxhbmsiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTAuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0hlbHZldGljYSZxdW90OyxzYW5zLXNlcmlmO2JvcmRlcjpub25lIHdpbmRvd3RleHQgMS4wcHQ7cGFkZGluZzowaW47dGV4dC1kZWNvcmF0aW9uOm5vbmUiPjxpbWcgYm9yZGVyPSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjgwIiBzdHlsZT0id2lkdGg6My4xMjVpbjtoZWlnaHQ6LjgzMzNpbiIgaWQ9IlBpY3R1cmVfeDAwMjBfODM0ODk0MDYiIHNyYz0iY2lkOmltYWdlMDAyLnBuZ0AwMURCRjczMi5FMENFNjYxMCIgYWx0PSJSYW5rZWQgIzIgZmFzdGVzdCBncm93aW5nIEFJIGNvbXBhbnkgaW4gQW1lcmljYSBpbiB0aGUgMjAyNCBJbmMuIDUwMDAiPjwvc3Bhbj48L2E-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMC4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7SGVsdmV0aWNhJnF1b3Q7LHNhbnMtc2VyaWY7Y29sb3I6IzBmMGYwZiI-PC9zcGFuPjwvcD48L3RkPjwvdHI-PHRyIHN0eWxlPSJoZWlnaHQ6MjQuMHB0Ij48dGQgc3R5bGU9InBhZGRpbmc6Ni4wcHQgMGluIDBpbiAwaW47aGVpZ2h0OjI0LjBwdCI-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1hcmdpbjouMXB0Ij48YSBocmVmPSJodHRwczovL3d3dy5saW5rZWRpbi5jb20vY29tcGFueS9pbnZpc2libGUtdGVjaG5vbG9naWVzLWluYy0vIiB0YXJnZXQ9Il9ibGFuayI-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMC4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7SGVsdmV0aWNhJnF1b3Q7LHNhbnMtc2VyaWY7dGV4dC1kZWNvcmF0aW9uOm5vbmUiPjxpbWcgYm9yZGVyPSIwIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHN0eWxlPSJ3aWR0aDouMzMzM2luO2hlaWdodDouMzMzM2luIiBpZD0iUGljdHVyZV94MDAyMF8zNzI4MjEyMTQiIHNyYz0iY2lkOmltYWdlMDAzLnBuZ0AwMURCRjczMi5FMENFNjYxMCIgYWx0PSJMaW5rZWRJbiI-PC9zcGFuPjwvYT48YSBocmVmPSJodHRwczovL3guY29tL2ludnRlY2hpbmMiIHRhcmdldD0iX2JsYW5rIj48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEwLjBwdDtmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjt0ZXh0LWRlY29yYXRpb246bm9uZSI-PGltZyBib3JkZXI9IjAiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgc3R5bGU9IndpZHRoOi4zMzMzaW47aGVpZ2h0Oi4zMzMzaW4iIGlkPSJQaWN0dXJlX3gwMDIwXzE3NDE1MDc3ODYiIHNyYz0iY2lkOmltYWdlMDA0LnBuZ0AwMURCRjczMi5FMENFNjYxMCIgYWx0PSJYIj48L3NwYW4-PC9hPjxhIGhyZWY9Imh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL0BpbnZpc2libGV0ZWNobm9sb2dpZXMiIHRhcmdldD0iX2JsYW5rIj48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEwLjBwdDtmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjt0ZXh0LWRlY29yYXRpb246bm9uZSI-PGltZyBib3JkZXI9IjAiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgc3R5bGU9IndpZHRoOi4zMzMzaW47aGVpZ2h0Oi4zMzMzaW4iIGlkPSJQaWN0dXJlX3gwMDIwXzQwODU2MDA3NCIgc3JjPSJjaWQ6aW1hZ2UwMDUucG5nQDAxREJGNzMyLkUwQ0U2NjEwIiBhbHQ9IllvdVR1YmUiPjwvc3Bhbj48L2E-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMC4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7SGVsdmV0aWNhJnF1b3Q7LHNhbnMtc2VyaWY7Y29sb3I6IzBmMGYwZiI-PC9zcGFuPjwvcD48L3RkPjwvdHI-PC90YWJsZT48cCBjbGFzcz0iTXNvTm9ybWFsIj7CoDwvcD48cCBjbGFzcz0iTXNvTm9ybWFsIj7CoDwvcD48ZGl2IHN0eWxlPSJib3JkZXI6bm9uZTtib3JkZXItdG9wOnNvbGlkICNlMWUxZTEgMS4wcHQ7cGFkZGluZzozLjBwdCAwaW4gMGluIDBpbiI-PHAgY2xhc3M9Ik1zb05vcm1hbCI-PGI-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMS4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7Q2FsaWJyaSZxdW90OyxzYW5zLXNlcmlmIj5Gcm9tOjwvc3Bhbj48L2I-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMS4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7Q2FsaWJyaSZxdW90OyxzYW5zLXNlcmlmIj4gQ2FybCBTYW5kYmVyZyAmbHQ7Y2FybC5zYW5kYmVyZ0BpbnZpc2libGUuZW1haWwmZ3Q7IDxicj48Yj5TZW50OjwvYj4gV2VkbmVzZGF5LCBKdWx5IDE2LCAyMDI1IDEwOjE1IFBNPGJyPjxiPlRvOjwvYj4gTWF0dGhldyBGaXR6cGF0cmljayAmbHQ7bWF0dEBpbnZpc2libGUuZW1haWwmZ3Q7PGJyPjxiPkNjOjwvYj4gPGEgaHJlZj0ibWFpbHRvOm5hZGVhdW1AaG1jLmhhcnZhcmQuZWR1Ij5uYWRlYXVtQGhtYy5oYXJ2YXJkLmVkdTwvYT47IDxhIGhyZWY9Im1haWx0bzpzbXVybnl5eUBobWMuaGFydmFyZC5lZHUiPnNtdXJueXl5QGhtYy5oYXJ2YXJkLmVkdTwvYT47IDxhIGhyZWY9Im1haWx0bzptYWpld3NraWRAaG1jLmhhcnZhcmQuZWR1Ij5tYWpld3NraWRAaG1jLmhhcnZhcmQuZWR1PC9hPjsgQWxleGl1cyBXcm9ua2EgJmx0O2FsZXhpdXNAaW52aXNpYmxlLmVtYWlsJmd0OzsgQWxla3NlaSBTaGt1cmluICZsdDthbGVrc2VpQGludmlzaWJsZS5lbWFpbCZndDs7IFVzaGEgUmFvICZsdDt1c2hhLnJhb0BpbnZpc2libGUuZW1haWwmZ3Q7OyBSaWNoYXJkIFp1cm9mZiAmbHQ7cmljaGFyZC56dXJvZmZAaW52aXNpYmxlLmVtYWlsJmd0OzsgWHVlcWkgQ2hhbmcgJmx0O3h1ZXFpLmNoYW5nQGludmlzaWJsZS5lbWFpbCZndDs8YnI-PGI-U3ViamVjdDo8L2I-IFJlOiBJbnZpc2libGUvSE1DIGNvbm5lY3Q8L3NwYW4-PC9wPjwvZGl2PjxwIGNsYXNzPSJNc29Ob3JtYWwiPsKgPC9wPjxkaXY-PHAgY2xhc3M9Ik1zb05vcm1hbCI-TWlrZS0tSnVzdCBjYXVnaHQgdXAgd2l0aCBNYXR0IHJlZ2FyZGluZyB0aGUgdXBkYXRlZCB0aW1lbGluZSBvbiB5b3VyIHNpZGUuIFdlJiMzOTtyZSBsb29raW5nIGZvcndhcmQgdG8gdGhlIG5leHQgc3RlcHMuwqA8L3A-PGRpdj48ZGl2PjxwIGNsYXNzPSJNc29Ob3JtYWwiPsKgPC9wPjwvZGl2PjxkaXY-PHAgY2xhc3M9Ik1zb05vcm1hbCI-Q2FybMKgPC9wPjwvZGl2PjwvZGl2PjwvZGl2PjxwIGNsYXNzPSJNc29Ob3JtYWwiPsKgPC9wPjxkaXY-PGRpdj48cCBjbGFzcz0iTXNvTm9ybWFsIj5PbiBXZWQsIEp1bCAxNiwgMjAyNSBhdCA5OjU3PHNwYW4gc3R5bGU9ImZvbnQtZmFtaWx5OiZxdW90O0FyaWFsJnF1b3Q7LHNhbnMtc2VyaWYiPuKArzwvc3Bhbj5QTSBNYXR0aGV3IEZpdHpwYXRyaWNrICZsdDs8YSBocmVmPSJtYWlsdG86bWF0dEBpbnZpc2libGUuZW1haWwiPm1hdHRAaW52aXNpYmxlLmVtYWlsPC9hPiZndDsgd3JvdGU6PC9wPjwvZGl2PjxibG9ja3F1b3RlIHN0eWxlPSJib3JkZXI6bm9uZTtib3JkZXItbGVmdDpzb2xpZCAjY2NjY2NjIDEuMHB0O3BhZGRpbmc6MGluIDBpbiAwaW4gNi4wcHQ7bWFyZ2luLWxlZnQ6NC44cHQ7bWFyZ2luLXJpZ2h0OjBpbiI-PGRpdj48ZGl2PjxkaXY-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvIj5NaWtlLCBhbGwsPC9wPjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtc28tbWFyZ2luLXRvcC1hbHQ6YXV0bzttc28tbWFyZ2luLWJvdHRvbS1hbHQ6YXV0byI-wqA8L3A-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvIj5XZSByZWFsbHkgZW5qb3llZCB0aGUgY29udmVyc2F0aW9uIGxhc3Qgd2VlaywgdGhhbmsgeW91IGZvciB0YWtpbmcgdGhlIHRpbWUgYW5kIEkgZGVmaW5pdGVseSB0aGluayBvdXIgcGxhdGZvcm1zIGNvdWxkIGJlIGhlbHBmdWwgZm9yIHNvbWUgb2YgdGhlIGRhdGEgY2hhbGxlbmdlcyB5b3UgYXJlIGdyYXBwbGluZyB3aXRoPC9wPjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtc28tbWFyZ2luLXRvcC1hbHQ6YXV0bzttc28tbWFyZ2luLWJvdHRvbS1hbHQ6YXV0byI-wqA8L3A-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvIj5JIHdhbnRlZCB0byBmb2xsb3cgdXAgb24gdGhlIG5leHQgc3RlcHMgd2UgZGlzY3Vzc2VkLCB0byBzdGFydCB0byB0aGUgcHJvY2VzcyBvbiBhbiBpbml0aWFsIGRlcGxveW1lbnQgb2Ygb3VyIGRhdGEgcGxhdGZvcm0gdG8gZGVtb25zdHJhdGUgZXh0cmFjdGluZyBleHBvc3VyZSBhbmQgcG9zaXRpb25hbCBkYXRhIGZyb20gbWFuYWdlciBsZXR0ZXJzLiBJIHRoaW5rIHdlIGRpc2N1c3NlZCAzIHNwZWNpZmljIGFjdGlvbnM8L3A-PG9sIHN0YXJ0PSIxIiB0eXBlPSIxIj48bGkgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvO21zby1saXN0OmwwIGxldmVsMSBsZm8xIj5XZSBjYW4gZXhlY3V0ZSBhbiBOREEgaWYgeW91IHNlbmQgb3ZlcjwvbGk-PGxpIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtc28tbWFyZ2luLXRvcC1hbHQ6YXV0bzttc28tbWFyZ2luLWJvdHRvbS1hbHQ6YXV0bzttc28tbGlzdDpsMCBsZXZlbDEgbGZvMSI-V2UgY2FuIHN0YXJ0IHRoZSBwcm9jZXNzIG9mIHBlcm1pc3Npb25pbmcgb3Igc2V0dGluZyB1cCBhbiBlbnZpcm9ubWVudDwvbGk-PGxpIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtc28tbWFyZ2luLXRvcC1hbHQ6YXV0bzttc28tbWFyZ2luLWJvdHRvbS1hbHQ6YXV0bzttc28tbGlzdDpsMCBsZXZlbDEgbGZvMSI-WW91IGNhbiBnaXZlIHVzIGEgc2V0IG9mIHJlbGV2YW50IGRvY3VtZW50cyByZWNlaXZlZCwgcmVkYWN0ZWQgYXMgbmVlZGVkLCBhcyB3ZWxsIGFzIHRoZSB0YXJnZXQgc3RhdGUgb3V0cHV0cyB5b3Ugd2VyZSBzZWVraW5nIHRvIGRlbGl2ZXI8L2xpPjwvb2w-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvIj7CoDwvcD48cCBjbGFzcz0iTXNvTm9ybWFsIiBzdHlsZT0ibXNvLW1hcmdpbi10b3AtYWx0OmF1dG87bXNvLW1hcmdpbi1ib3R0b20tYWx0OmF1dG8iPkxldCB1cyBrbm93IHRoZSBiZXN0IHdheSB0byBnZXQgc3RhcnRlZCBvbiB0aGlzPyBBIHN1YnNldCBvZiB0aGlzIGdyb3VwIGNhbiBhbHNvIGp1bXAgb24gdGhpcyBwaG9uZSB0b21vcnJvdyBhcyBuZWVkZWQg4oCTIENhcmwgY2FuIHF1YXJ0ZXJiYWNrIGZyb20gb3VyIHNpZGU8L3A-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvIj7CoDwvcD48cCBjbGFzcz0iTXNvTm9ybWFsIiBzdHlsZT0ibXNvLW1hcmdpbi10b3AtYWx0OmF1dG87bXNvLW1hcmdpbi1ib3R0b20tYWx0OmF1dG8iPk1hdHQ8L3A-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvIj7CoDwvcD48ZGl2PjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtc28tbWFyZ2luLXRvcC1hbHQ6YXV0bzttc28tbWFyZ2luLWJvdHRvbS1hbHQ6YXV0byI-wqA8L3A-PHRhYmxlIGNsYXNzPSJNc29Ob3JtYWxUYWJsZSIgYm9yZGVyPSIwIiBjZWxsc3BhY2luZz0iMCIgY2VsbHBhZGRpbmc9IjAiIHdpZHRoPSIzNTAiIHN0eWxlPSJ3aWR0aDoyNjIuNXB0Ij48dHIgc3R5bGU9ImhlaWdodDoxNi41cHQiPjx0ZCBzdHlsZT0icGFkZGluZzowaW4gMGluIDEyLjBwdCAwaW47aGVpZ2h0OjE2LjVwdCI-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1hcmdpbjouMXB0Ij48YSBocmVmPSJodHRwczovL3d3dy5pbnZpc2libGUuY28vIiB0YXJnZXQ9Il9ibGFuayI-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMC4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7SGVsdmV0aWNhJnF1b3Q7LHNhbnMtc2VyaWY7Ym9yZGVyOm5vbmUgd2luZG93dGV4dCAxLjBwdDtwYWRkaW5nOjBpbjt0ZXh0LWRlY29yYXRpb246bm9uZSI-PGltZyBib3JkZXI9IjAiIHdpZHRoPSIxMjUiIGhlaWdodD0iMjIiIHN0eWxlPSJ3aWR0aDoxLjI5ODZpbjtoZWlnaHQ6LjIyOTFpbiIgaWQ9Im1fNTE5Mzg1ODI2MjM3NzY0OTAwNG1fNzkzMDYxOTE1MzcxODMwNzE0N21fODc3MTA3OTUzMjkxOTgwODE2OVBpY3R1cmVfeDAwMjBfMSIgc3JjPSJjaWQ6aW1hZ2UwMDEucG5nQDAxREJGNzMyLkUwQ0U2NjEwIj48L3NwYW4-PC9hPjwvcD48L3RkPjwvdHI-PHRyPjx0ZCBzdHlsZT0icGFkZGluZzowaW4gMGluIDBpbiAwaW4iPjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtYXJnaW46LjFwdDtsaW5lLWhlaWdodDoxOC4wcHQiPjxiPjxzcGFuIHN0eWxlPSJmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjtjb2xvcjojMGYwZjBmIj5NYXR0IEZpdHpwYXRyaWNrPC9zcGFuPjwvYj48L3A-PC90ZD48L3RyPjx0cj48dGQgc3R5bGU9InBhZGRpbmc6MGluIDBpbiAwaW4gMGluIj48cCBjbGFzcz0iTXNvTm9ybWFsIiBzdHlsZT0ibWFyZ2luOi4xcHQ7bGluZS1oZWlnaHQ6MTUuMHB0Ij48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEwLjBwdDtmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjtjb2xvcjojMGYwZjBmIj5DaGllZiBFeGVjdXRpdmUgT2ZmaWNlciDigKIgSW52aXNpYmxlIFRlY2hub2xvZ2llczwvc3Bhbj48L3A-PC90ZD48L3RyPjx0cj48dGQgc3R5bGU9InBhZGRpbmc6MGluIDBpbiAwaW4gMGluIj48cCBjbGFzcz0iTXNvTm9ybWFsIiBzdHlsZT0ibWFyZ2luOi4xcHQ7bGluZS1oZWlnaHQ6MTUuMHB0Ij48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEwLjBwdDtmb250LWZhbWlseTomcXVvdDtIZWx2ZXRpY2EmcXVvdDssc2Fucy1zZXJpZjtjb2xvcjojMGYwZjBmIj48YSBocmVmPSJtYWlsdG86TWF0dEBJbnZpc2libGUuY28iIHRhcmdldD0iX2JsYW5rIj48c3BhbiBzdHlsZT0iY29sb3I6IzBmMGYwZiI-TWF0dEBJbnZpc2libGUuY288L3NwYW4-PC9hPjwvc3Bhbj48L3A-PC90ZD48L3RyPjx0ciBzdHlsZT0iaGVpZ2h0OjYwLjBwdCI-PHRkIHN0eWxlPSJwYWRkaW5nOjkuMHB0IDBpbiAwaW4gMGluO2hlaWdodDo2MC4wcHQiPjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtYXJnaW46LjFwdCI-PGEgaHJlZj0iaHR0cHM6Ly93d3cuaW52aXNpYmxlLmNvLyIgdGFyZ2V0PSJfYmxhbmsiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTAuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0hlbHZldGljYSZxdW90OyxzYW5zLXNlcmlmO2JvcmRlcjpub25lIHdpbmRvd3RleHQgMS4wcHQ7cGFkZGluZzowaW47dGV4dC1kZWNvcmF0aW9uOm5vbmUiPjxpbWcgYm9yZGVyPSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjgwIiBzdHlsZT0id2lkdGg6My4xMjVpbjtoZWlnaHQ6LjgzMzNpbiIgaWQ9Im1fNTE5Mzg1ODI2MjM3NzY0OTAwNG1fNzkzMDYxOTE1MzcxODMwNzE0N21fODc3MTA3OTUzMjkxOTgwODE2OVBpY3R1cmVfeDAwMjBfMiIgc3JjPSJjaWQ6aW1hZ2UwMDIucG5nQDAxREJGNzMyLkUwQ0U2NjEwIiBhbHQ9IlJhbmtlZCAjMiBmYXN0ZXN0IGdyb3dpbmcgQUkgY29tcGFueSBpbiBBbWVyaWNhIGluIHRoZSAyMDI0IEluYy4gNTAwMCI-PC9zcGFuPjwvYT48L3A-PC90ZD48L3RyPjx0ciBzdHlsZT0iaGVpZ2h0OjI0LjBwdCI-PHRkIHN0eWxlPSJwYWRkaW5nOjYuMHB0IDBpbiAwaW4gMGluO2hlaWdodDoyNC4wcHQiPjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtYXJnaW46LjFwdCI-PGEgaHJlZj0iaHR0cHM6Ly93d3cubGlua2VkaW4uY29tL2NvbXBhbnkvaW52aXNpYmxlLXRlY2hub2xvZ2llcy1pbmMtLyIgdGFyZ2V0PSJfYmxhbmsiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTAuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0hlbHZldGljYSZxdW90OyxzYW5zLXNlcmlmO3RleHQtZGVjb3JhdGlvbjpub25lIj48aW1nIGJvcmRlcj0iMCIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBzdHlsZT0id2lkdGg6LjMzMzNpbjtoZWlnaHQ6LjMzMzNpbiIgaWQ9Im1fNTE5Mzg1ODI2MjM3NzY0OTAwNG1fNzkzMDYxOTE1MzcxODMwNzE0N21fODc3MTA3OTUzMjkxOTgwODE2OVBpY3R1cmVfeDAwMjBfMyIgc3JjPSJjaWQ6aW1hZ2UwMDMucG5nQDAxREJGNzMyLkUwQ0U2NjEwIiBhbHQ9IkxpbmtlZEluIj48L3NwYW4-PC9hPjxhIGhyZWY9Imh0dHBzOi8veC5jb20vaW52dGVjaGluYyIgdGFyZ2V0PSJfYmxhbmsiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTAuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0hlbHZldGljYSZxdW90OyxzYW5zLXNlcmlmO3RleHQtZGVjb3JhdGlvbjpub25lIj48aW1nIGJvcmRlcj0iMCIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBzdHlsZT0id2lkdGg6LjMzMzNpbjtoZWlnaHQ6LjMzMzNpbiIgaWQ9Im1fNTE5Mzg1ODI2MjM3NzY0OTAwNG1fNzkzMDYxOTE1MzcxODMwNzE0N21fODc3MTA3OTUzMjkxOTgwODE2OVBpY3R1cmVfeDAwMjBfNCIgc3JjPSJjaWQ6aW1hZ2UwMDQucG5nQDAxREJGNzMyLkUwQ0U2NjEwIiBhbHQ9IlgiPjwvc3Bhbj48L2E-PGEgaHJlZj0iaHR0cHM6Ly93d3cueW91dHViZS5jb20vQGludmlzaWJsZXRlY2hub2xvZ2llcyIgdGFyZ2V0PSJfYmxhbmsiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTAuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0hlbHZldGljYSZxdW90OyxzYW5zLXNlcmlmO3RleHQtZGVjb3JhdGlvbjpub25lIj48aW1nIGJvcmRlcj0iMCIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBzdHlsZT0id2lkdGg6LjMzMzNpbjtoZWlnaHQ6LjMzMzNpbiIgaWQ9Im1fNTE5Mzg1ODI2MjM3NzY0OTAwNG1fNzkzMDYxOTE1MzcxODMwNzE0N21fODc3MTA3OTUzMjkxOTgwODE2OVBpY3R1cmVfeDAwMjBfNSIgc3JjPSJjaWQ6aW1hZ2UwMDUucG5nQDAxREJGNzMyLkUwQ0U2NjEwIiBhbHQ9IllvdVR1YmUiPjwvc3Bhbj48L2E-PC9wPjwvdGQ-PC90cj48L3RhYmxlPjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtc28tbWFyZ2luLXRvcC1hbHQ6YXV0bzttc28tbWFyZ2luLWJvdHRvbS1hbHQ6YXV0byI-wqA8L3A-PC9kaXY-PHAgY2xhc3M9Ik1zb05vcm1hbCIgc3R5bGU9Im1zby1tYXJnaW4tdG9wLWFsdDphdXRvO21zby1tYXJnaW4tYm90dG9tLWFsdDphdXRvIj7CoDwvcD48ZGl2PjxwIGNsYXNzPSJNc29Ob3JtYWwiIHN0eWxlPSJtc28tbWFyZ2luLXRvcC1hbHQ6YXV0bzttc28tbWFyZ2luLWJvdHRvbS1hbHQ6YXV0byI-PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMS4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7Q2FsaWJyaSZxdW90OyxzYW5zLXNlcmlmIj4tLS0tLU9yaWdpbmFsIEFwcG9pbnRtZW50LS0tLS08YnI-PGI-RnJvbTo8L2I-IDxhIGhyZWY9Im1haWx0bzptYXR0QGludmlzaWJsZS5lbWFpbCI-bWF0dEBpbnZpc2libGUuZW1haWw8L2E-ICZsdDs8YSBocmVmPSJtYWlsdG86bWF0dEBpbnZpc2libGUuZW1haWwiPm1hdHRAaW52aXNpYmxlLmVtYWlsPC9hPiZndDsgPGJyPjxiPlNlbnQ6PC9iPiBOb25lPGJyPjxiPlRvOjwvYj4gPGEgaHJlZj0ibWFpbHRvOm1hdHRAaW52aXNpYmxlLmVtYWlsIj5tYXR0QGludmlzaWJsZS5lbWFpbDwvYT47IDxhIGhyZWY9Im1haWx0bzpuYWRlYXVtQGhtYy5oYXJ2YXJkLmVkdSIgdGFyZ2V0PSJfYmxhbmsiPm5hZGVhdW1AaG1jLmhhcnZhcmQuZWR1PC9hPjsgWHVlcWkgQ2hhbmc7IEFsZXhpdXMgV3JvbmthOyBBbGVrc2VpIFNoa3VyaW47IFVzaGEgUmFvOyA8YSBocmVmPSJtYWlsdG86c211cm55eXlAaG1jLmhhcnZhcmQuZWR1IiB0YXJnZXQ9Il9ibGFuayI-c211cm55eXlAaG1jLmhhcnZhcmQuZWR1PC9hPjsgQ2FybCBTYW5kYmVyZzsgPGEgaHJlZj0ibWFpbHRvOm1hamV3c2tpZEBobWMuaGFydmFyZC5lZHUiIHRhcmdldD0iX2JsYW5rIj5tYWpld3NraWRAaG1jLmhhcnZhcmQuZWR1PC9hPjsgQ29uZi4gUm0uIChBL1YpIC0gTWlrZSYjMzk7cyBPZmZpY2UgLSAxNnRoIEZsb29yIC0gNDxicj48Yj5TdWJqZWN0OjwvYj4gSW52aXNpYmxlL0hNQyBjb25uZWN0PGJyPjxiPldoZW46PC9iPiBXZWRuZXNkYXksIEp1bHkgOSwgMjAyNSAzOjAwIFBNLTM6NDUgUE0gKFVUQy0wNTowMCkgRWFzdGVybiBUaW1lIChVUyAmYW1wOyBDYW5hZGEpLjxicj48Yj5XaGVyZTo8L2I-IDxhIGhyZWY9Imh0dHBzOi8vaW52LXRlY2guem9vbS51cy9qLzkxMDY5Mjc1NDk2IiB0YXJnZXQ9Il9ibGFuayI-aHR0cHM6Ly9pbnYtdGVjaC56b29tLnVzL2ovOTEwNjkyNzU0OTY8L2E-PC9zcGFuPjwvcD48L2Rpdj48cCBjbGFzcz0iTXNvTm9ybWFsIiBzdHlsZT0ibXNvLW1hcmdpbi10b3AtYWx0OmF1dG87bXNvLW1hcmdpbi1ib3R0b20tYWx0OmF1dG8iPsKgPC9wPjxwPuKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgDxicj5NYXR0IEZpdHpwYXRyaWNrIGlzIGludml0aW5nIHlvdSB0byBhIHNjaGVkdWxlZCBab29tIG1lZXRpbmcuPGJyPkpvaW4gWm9vbSBNZWV0aW5nPGJyPjxhIGhyZWY9Imh0dHBzOi8vaW52LXRlY2guem9vbS51cy9qLzkxMDY5Mjc1NDk2IiB0YXJnZXQ9Il9ibGFuayI-aHR0cHM6Ly9pbnYtdGVjaC56b29tLnVzL2ovOTEwNjkyNzU0OTY8L2E-PGJyPjxicj5NZWV0aW5nIElEOiA5MTAgNjkyNyA1NDk2PGJyPjxicj4tLS08YnI-PGJyPk9uZSB0YXAgbW9iaWxlPGJyPisxNjQ2ODc2OTkyMywsOTEwNjkyNzU0OTYjIFVTIChOZXcgWW9yayk8YnI-KzE2NDY5MzEzODYwLCw5MTA2OTI3NTQ5NiMgVVM8YnI-PGJyPi0tLTxicj48YnI-RGlhbCBieSB5b3VyIGxvY2F0aW9uPGJyPuKAoiArMSA2NDYgODc2IDk5MjMgVVMgKE5ldyBZb3JrKTxicj7igKIgKzEgNjQ2IDkzMSAzODYwIFVTPGJyPuKAoiArMSAzMDEgNzE1IDg1OTIgVVMgKFdhc2hpbmd0b24gREMpPGJyPuKAoiArMSAzMDUgMjI0IDE5NjggVVM8YnI-4oCiICsxIDMwOSAyMDUgMzMyNSBVUzxicj7igKIgKzEgMzEyIDYyNiA2Nzk5IFVTIChDaGljYWdvKTxicj7igKIgKzEgNzE5IDM1OSA0NTgwIFVTPGJyPuKAoiArMSAyNTMgMjA1IDA0NjggVVM8YnI-4oCiICsxIDI1MyAyMTUgODc4MiBVUyAoVGFjb21hKTxicj7igKIgKzEgMzQ2IDI0OCA3Nzk5IFVTIChIb3VzdG9uKTxicj7igKIgKzEgMzYwIDIwOSA1NjIzIFVTPGJyPuKAoiArMSAzODYgMzQ3IDUwNTMgVVM8YnI-4oCiICsxIDUwNyA0NzMgNDg0NyBVUzxicj7igKIgKzEgNTY0IDIxNyAyMDAwIFVTPGJyPuKAoiArMSA2NjkgNDQ0IDkxNzEgVVM8YnI-4oCiICsxIDY2OSA5MDAgNjgzMyBVUyAoU2FuIEpvc2UpPGJyPuKAoiArMSA2ODkgMjc4IDEwMDAgVVM8YnI-PGJyPk1lZXRpbmcgSUQ6IDkxMCA2OTI3IDU0OTY8YnI-PGJyPkZpbmQgeW91ciBsb2NhbCBudW1iZXI6IDxhIGhyZWY9Imh0dHBzOi8vaW52LXRlY2guem9vbS51cy91L2Fkc213TEZKaFQiIHRhcmdldD0iX2JsYW5rIj5odHRwczovL2ludi10ZWNoLnpvb20udXMvdS9hZHNtd0xGSmhUPC9hPjxicj48YnI-LS0tPGJyPjxicj5Kb2luIGJ5IFNJUDxicj7igKIgPGEgaHJlZj0ibWFpbHRvOjkxMDY5Mjc1NDk2QHpvb21jcmMuY29tIiB0YXJnZXQ9Il9ibGFuayI-OTEwNjkyNzU0OTZAem9vbWNyYy5jb208L2E-PGJyPjxicj4tLS08YnI-PGJyPkpvaW4gYnkgSC4zMjM8YnI-4oCiIDE0NC4xOTUuMTkuMTYxIChVUyBXZXN0KTxicj7igKIgMjA2LjI0Ny4xMS4xMjEgKFVTIEVhc3QpPGJyPuKAoiAxMTUuMTE0LjEzMS43IChJbmRpYSBNdW1iYWkpPGJyPuKAoiAxMTUuMTE0LjExNS43IChJbmRpYSBIeWRlcmFiYWQpPGJyPuKAoiAxNTkuMTI0LjE1LjE5MSAoQW1zdGVyZGFtIE5ldGhlcmxhbmRzKTxicj7igKIgMTU5LjEyNC40Ny4yNDkgKEdlcm1hbnkpPGJyPuKAoiAxNTkuMTI0LjEwNC4yMTMgKEF1c3RyYWxpYSBTeWRuZXkpPGJyPuKAoiAxNTkuMTI0Ljc0LjIxMiAoQXVzdHJhbGlhIE1lbGJvdXJuZSk8YnI-4oCiIDE3MC4xMTQuMTgwLjIxOSAoU2luZ2Fwb3JlKTxicj7igKIgNjQuMjExLjE0NC4xNjAgKEJyYXppbCk8YnI-4oCiIDE1OS4xMjQuMTMyLjI0MyAoTWV4aWNvKTxicj7igKIgMTU5LjEyNC4xNjguMjEzIChDYW5hZGEgVG9yb250byk8YnI-4oCiIDE1OS4xMjQuMTk2LjI1IChDYW5hZGEgVmFuY291dmVyKTxicj7igKIgMTcwLjExNC4xOTQuMTYzIChKYXBhbiBUb2t5byk8YnI-4oCiIDE0Ny4xMjQuMTAwLjI1IChKYXBhbiBPc2FrYSk8YnI-PGJyPk1lZXRpbmcgSUQ6IDkxMCA2OTI3IDU0OTY8YnI-PGJyPjxicj7ilIDilIDilIDilIDilIDilIDilIDilIDilIDilIA8L3A-PC9kaXY-PC9kaXY-PC9kaXY-PC9ibG9ja3F1b3RlPjwvZGl2PjwvZGl2PjwvYm9keT48L2h0bWw-DQo="}}]}, {"partId": "1", "mimeType": "image/png", "filename": "image001.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image001.png\""}, {"name": "Content-Disposition", "value": "inline; filename=\"image001.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image001.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.1"}], "body": {"attachmentId": "ANGjdJ9317fNETCP8m59rlG7u7cwZ4NfhSZIdKKovUhMu4dmxw86cMmF-Rieop1g90M_KYTdMioYVzWnun2rBZ3HJmBqJ_x-RyNU7WoQ-OuycNRbzeW65eF1GJX1mAjwwtnth90vgVx8d3Sl4JPiSTlWX0QLdM5DHtDSHPMpiq721VSU0OyFsa9Ef54rLn2sGLP0KtBgYDzyXkOR3EXNp-EQ0hVwKF3ip5BxEuG2HEXgtVBFolNIayHxMkARU1R8jGT179G1Bh7CqGyofYsUXoy-WXbkRCFNjc2EjLIz3rtN8PrD9mksim8ojzqJ4NpAL_7qwTiSDD5lJvvkm0xYNR4c4Re-NpUC8ounq84ceWTyBFs-aF0HlIysYDyD_l2o9V8q0cK0XSZGgpwF0cQg", "size": 4372}}, {"partId": "2", "mimeType": "image/png", "filename": "image002.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image002.png\""}, {"name": "Content-Disposition", "value": "inline; filename=\"image002.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image002.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.2"}], "body": {"attachmentId": "ANGjdJ_SulD1SlFguL2T5lB54esCQnqOZdGwISD21t43B16nhA85WW8mSJJuiy66wkZXI7VjqWInsgqhVtv9AZUUSvs5L_BBh8-O9q7DR_m8VFqyhe6ij7Yc-xas9DRQDhGUcb2rNXbw_NUtFT6TbPWOm07AyaKlhuDnRlsEJvjQIGFa6ChK3QG4Zj5RsWAMjig9yx1aocu-mVvndaslNL6Ok11L8yTZNQRpQM7tPRBjkg0-caJIXTvhfHfaS4QzRtGB0ZwINmsLV_mIgkcdyAw1m5F3BBG9PcUJY_4QSmbm-pB-soZOmjZFlVkQli4rqBIkp5twSDVUbxoZkXS6pR7flrC925ShUZELZDbS22TBMB4RtASeUG4qvsOLL8yllW5lKXbh0q4CNitVu1Sb", "size": 32085}}, {"partId": "3", "mimeType": "image/png", "filename": "image003.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image003.png\""}, {"name": "Content-Disposition", "value": "inline; filename=\"image003.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image003.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.3"}], "body": {"attachmentId": "ANGjdJ-c8qXjQ_jXakqScDNXlAB9uMzRlJ4NL71BpPc9tm-KKykbW5gtVGtUUQ5AERfnxjPMCMjJ8q9GRvDJosWOoxvw2kZRTIBbwvULfEu7qGpaI2CF3ocvQL2eY4hTuV0xSkltGP7X-wVOQQTk6LBQgiBObZUtyMfEUnoOcEnYw-fLsn3JzbIy9gDb1dZ4U90suFuf8UJua5a_Nr2VRInh59BmXuVh2Dgd_QMQxtdch88sm0ORuaGDd3vNMlRlTNFrRbaJeQzc0A8ZpUFABQ3kxe_g_aDPdr9jMT0mu_sj_Pho1QHHWUfpGe_sbwLM_LCkllXTv986kIzVIsdh07YLFvxNURT2of8v-wOK_ptax_bjGAxdFU1W-9hkksJ7f1_9ah5LhSpHuFfj_CDe", "size": 1582}}, {"partId": "4", "mimeType": "image/png", "filename": "image004.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image004.png\""}, {"name": "Content-Disposition", "value": "inline; filename=\"image004.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image004.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.4"}], "body": {"attachmentId": "ANGjdJ81tLeHuEgjl6bIHRFV69uZO39v-HihQZxN5_ocWaOYTD5Y-caaFIfuDSdjHtB5sBPgrZY828HR8hdrwghljXML8cQEeHzQNt3e1e3OXEbJxbt1_g7n8aX4GWGfFAw6cVEtFAVTnu_Dw6gtXd2iOObuYF1OP1bOciNf6kAxgC_E5GZ35rMuTna-V8eW5fHeAEIH8cSVprNhqaYnvCJhBL8KeS6n_TrsB3iaonki40wgI3Z2FHRsCu3CXZ9W4cjWjO5I-ylTC1FMHoidIGxX43BKgg0lgK_agSFK5hTl0jnYgYYAKmfk1FZhyss3hOtKBKLbUFomxHVZGw_Sc8NerB-LYBN5dp3QpBC1o9p9kddNDE0F6OjfiaDYzI0DYez75JEZt0JX3Z_yYoSh", "size": 2137}}, {"partId": "5", "mimeType": "image/png", "filename": "image005.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image005.png\""}, {"name": "Content-Disposition", "value": "inline; filename=\"image005.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image005.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.5"}], "body": {"attachmentId": "ANGjdJ8spy6LDt5sjmIyYWK0V7em-fcxluf5KlEH0zO8zR_drSUKIBEyYiIJhByXyx2jGDIAwgIeZsvMwN0aqgSUl8M3Rx3qeKyKaITeXS_hF4Gt3nGFR48AI0q-ezd9RGmvzu_F4wuOAvguONV7NVl47hANw90yG8RDocBjMEEfp3kd-hUBqagl7nVc4zx-bhP8_HtANM4K1jKbY_M5Dcb6lf_zV4r-45mKEh8boK4684WdMo_U_Vi4AS4IHRcpWpo3xXnhwHOGuIaSzwMu7WpiTPohqBvbs_2DmZWiLlHL9YeZfCI3FvkP2SQ6DG37QCp_8CMyUDbiBphfJHoLuT7uBoOqVXx8wZIqGKzkiu4C3N80XaSdpY7n6_0GiwMlL6NdJqIxoX6tGls0WlOO", "size": 1613}}]}, "sizeEstimate": 87159, "historyId": "2538654", "internalDate": "1752781983000"}, {"id": "1981a4e792ebb6f9", "threadId": "19819f2981f148b2", "labelIds": ["Label_19", "UNREAD", "IMPORTANT", "CATEGORY_PERSONAL", "INBOX"], "snippet": "Terrific--let&#39;s do it. Thanks all. On Thu, Jul 17, 2025 at 3:53 PM <PERSON> &lt;<EMAIL>&gt; wrote: Team, as we start to build this out lets include <PERSON> and <PERSON>, they have", "payload": {"partId": "", "mimeType": "multipart/mixed", "filename": "", "headers": [{"name": "Delivered-To", "value": "<EMAIL>"}, {"name": "Received", "value": "by 2002:a05:6200:4498:b0:5ad:7480:a0e4 with SMTP id w24csp939877qnz;        Thu, 17 Jul 2025 14:33:27 -0700 (PDT)"}, {"name": "X-Forwarded-Encrypted", "value": "i=2; AJvYcCW8FqCEJA7GDEPfEhnEZ3dP1j3MCn5ZrEmIHzjaE1FakupZsjV3s9FeyZNYONIJ/4p+xpG7LoEqPp2I6Q==@invisible.email"}, {"name": "X-Received", "value": "by 2002:a17:90b:5485:b0:311:b0ec:135b with SMTP id 98e67ed59e1d1-31c9f47c799mr11605889a91.24.1752788007192;        <PERSON><PERSON>, 17 Jul 2025 14:33:27 -0700 (PDT)"}, {"name": "ARC-Seal", "value": "i=1; a=rsa-sha256; t=1752788007; cv=none;        d=google.com; s=arc-20240605;        b=Gl3vXN+IjGMTgBPOvb3EINmsHQQx0+8f0DHpq2+dZWTNU2ayPJr8yKJ4oeqcKwd69h         4z4dSDADlc3iLeLjx7qM8RHNOuNH0xhII01jkpEChBWaUCg5jgp50LMI+Hez9yDi02+2         juLi4VIg2fxsLK6svImH7WYcmgIdxDP7Sjb2pwH+XyQcyhAUWPOBcHGZxqMGMtk0+Qlr         sBoOWTtOyMVXUeqGx1Apq5fF62ooy35I8y3GG/WumMBFAg8Tlc+RXgbk+UyIFwV96WF3         0o1hc33GpBtV9blicIs1CKfo1PxHgxcfbKn7kPDt4Gu9F5zyiyQJ9JHYLABKa0EO6O/l         HvUg=="}, {"name": "ARC-Message-Signature", "value": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;        h=cc:to:subject:message-id:date:from:in-reply-to:references         :mime-version:dkim-signature;        bh=EN1yqFJ5nIcSuBnnS4riv1vgHb235zrq06eJPvdddfw=;        fh=u6bbgVRFbPJPGTl/oNROTEHNqvltN43MQJZIMU6+YB8=;        b=hpzmI/c2OmRBYYstSVVD8Dc18pZ5XI/Ob8qw7r4twMkZ3LGjVrjNcOrX9uw6Hq+q80         8B2B16lFUZMXNXj93bvy8S03lwHKay/2u8SX/DisE2FAjBWIpkXFiuUkDnQtr9T/iMO9         4tTw/KTsL+VBfJJD/4ddf55m1zhLRGuP28esLCZVsCdoJHa3RR0kgoY9y7OhqLnM7kLy         J8uE1FscQXXv4j2R54DMvPWghZUWDOWvo7aUq39ghbhTB/wIfKG0o7juU8W6DTR5HeFF         AjrhyESkZU8/b/zee82iM/zIqGst7jXFvVFsW2F3SePMZtuSo8ghPYvVzzx0UsejQpsA         VUSg==;        dara=google.com"}, {"name": "ARC-Authentication-Results", "value": "i=1; mx.google.com;       dkim=pass header.i=@invisible.email header.s=google header.b=h6JCOXoY;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=invisible.email;       dara=neutral header.i=@invisible.email"}, {"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Received", "value": "from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])        by mx.google.com with SMTPS id 98e67ed59e1d1-31cc3e53013sor8865a91.2.2025.***********.26        for <<EMAIL>>        (Google Transport Security);        Thu, 17 Jul 2025 14:33:27 -0700 (PDT)"}, {"name": "Received-SPF", "value": "pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;"}, {"name": "Authentication-Results", "value": "mx.google.com;       dkim=pass header.i=@invisible.email header.s=google header.b=h6JCOXoY;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=invisible.email;       dara=neutral header.i=@invisible.email"}, {"name": "DKIM-Signature", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=invisible.email; s=google; t=1752788006; x=1753392806; darn=invisible.email;        h=cc:to:subject:message-id:date:from:in-reply-to:references         :mime-version:from:to:cc:subject:date:message-id:reply-to;        bh=EN1yqFJ5nIcSuBnnS4riv1vgHb235zrq06eJPvdddfw=;        b=h6JCOXoYSjSAhNwDwf7CaaB9wFhNIrxB7jyxYSu0bd3yu8EmKO+aQypexIDpDlZp85         YUCQTvhotRG65I5D7pw4hxy/O+jYXLzji1gtUhWjWHvjlOzNY36tMXAsWze6wayHlElJ         RQfMW1GSQMDozAj4Fv7W3pvnSktysK02ouOk8="}, {"name": "X-Google-DKIM-Signature", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20230601; t=1752788006; x=1753392806;        h=cc:to:subject:message-id:date:from:in-reply-to:references         :mime-version:x-gm-message-state:from:to:cc:subject:date:message-id         :reply-to;        bh=EN1yqFJ5nIcSuBnnS4riv1vgHb235zrq06eJPvdddfw=;        b=ShCVRdT8A8KX+Fo5tLv9eadQzfW7QCGUs+sjXcLVL8KgkAsWDfXf8ji3ak6oatFO10         n3+l7ODcNMolcjh5MtunHvW+5xIyUiqhBH3S3CNO+njCx6PTmo9NfiKZn8w5HYLpyV/l         7gNGHGz6FtI9M/tVzTxubwZeB0s7s3msxqVoouQRQzFsC5Yij4l2uK1cDhttto5qgbSr         jblgKXI4bXYZZM1idKWEsfmlxqv+2uK4KV1wDNe9ppR3Ys0zkiD6NDW5Fu/k+mA8algY         UGPMEPxjk74klAlSNCvjnz+T31vWVP/IxtbxLWlQ9ySnJxBIqLKiKmVGV55irsH7JHKL         zuNg=="}, {"name": "X-Forwarded-Encrypted", "value": "i=1; AJvYcCUeyQ2DJttkAutFyRshfT4tcs6XkE4Xo3AvI/GkaE+RKDkeJMvE5Jr/Ubtv+KQ90d2H8nDjdXeIDcmQug==@invisible.email"}, {"name": "X-Gm-Message-State", "value": "AOJu0YweQuyka6CfxUpIgmTkMqJb6knFyiFQyzqVgO60KO2ZYC/TZgLR W7b9mtd0XyHWUaO+y9v5Ubpi50eMe0BHGlSxj8oBDsXnJXwy52nbwdNvziCXvZBHUseQ6Dg1QNg N9r3lxpAQZ6qzLlhjpF8G9b2WlmsBgP9VWieCy45zKkEn"}, {"name": "X-Gm-Gg", "value": "ASbGncuAsSFu/9L/pM92bnLFkXRGQqRUZN8CW3+7axOtDoU4wWPQVKvZFz/U7zOyPK9 vasiyr3MxIqf1zNpZ5VZ0ADR/I1J3tgClcJtHqatt4m8I8uLChXWgocW8CLrEej4ccIrGUH7aoX JITl1bBabDVGuJ/NBKh1WC+sdW5yNjaU2JOUNjOlaQcJZc/YIY/q9+8V3ZPIqx8TyH5ajyn5RL3 wT2zbHM"}, {"name": "X-Google-Smtp-Source", "value": "AGHT+IE2xPPbd2mMayfjNpeUHoUnutdp7GHHW6ollUqHl8l4hmpyZw6H4SbIFlt3xzdEarQA0/fCo7c/d0c96WeyeGA="}, {"name": "X-Received", "value": "by 2002:a17:90b:514d:b0:312:1d2d:18e2 with SMTP id 98e67ed59e1d1-31c9f45e299mr12606505a91.20.1752788006065; <PERSON><PERSON>, 17 Jul 2025 14:33:26 -0700 (PDT)"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "References", "value": "<<EMAIL>> <<EMAIL>> <<EMAIL>>"}, {"name": "In-Reply-To", "value": "<<EMAIL>>"}, {"name": "From", "value": "<PERSON> <<EMAIL>>"}, {"name": "Date", "value": "Thu, 17 Jul 2025 17:33:09 -0400"}, {"name": "X-Gm-Features", "value": "Ac12FXwOXCP566nwjwQywvirw2AkKtDx-GgoMj7PYtQddq5mq_ldprJgz5GY_Ls"}, {"name": "Message-ID", "value": "<CAA-SExPCkvSPSnJWvxpNOTen+tb=<EMAIL>>"}, {"name": "Subject", "value": "Re: Invisible/HMC connect"}, {"name": "To", "value": "<PERSON> <<EMAIL>>"}, {"name": "Cc", "value": "<PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <ale<PERSON><PERSON>@invisible.email>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>"}, {"name": "Content-Type", "value": "multipart/mixed; boundary=\"000000000000780f06063a26c234\""}], "body": {"size": 0}, "parts": [{"partId": "0", "mimeType": "multipart/alternative", "filename": "", "headers": [{"name": "Content-Type", "value": "multipart/alternative; boundary=\"000000000000780f05063a26c232\""}], "body": {"size": 0}, "parts": [{"partId": "0.0", "mimeType": "text/plain", "filename": "", "headers": [{"name": "Content-Type", "value": "text/plain; charset=\"UTF-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "body": {"size": 5142, "data": "VGVycmlmaWMtLWxldCdzIGRvIGl0LiBUaGFua3MgYWxsLg0KDQpPbiBUaHUsIEp1bCAxNywgMjAyNSBhdCAzOjUz4oCvUE0gTWF0dGhldyBGaXR6cGF0cmljayA8bWF0dEBpbnZpc2libGUuZW1haWw-DQp3cm90ZToNCg0KPiBUZWFtLCBhcyB3ZSBzdGFydCB0byBidWlsZCB0aGlzIG91dCBsZXRzIGluY2x1ZGUgQWxleCBhbmQgV2lsbCwgdGhleSBoYXZlDQo-IHNvbWUgcmVhbGx5IGNvb2wgY2FwYWJpbGl0aWVzIHdlIGNhbiBhZGQgaW4gaGVyZQ0KPg0KPg0KPg0KPg0KPg0KPiA8aHR0cHM6Ly93d3cuaW52aXNpYmxlLmNvLz4NCj4NCj4gKk1hdHQgRml0enBhdHJpY2sqDQo-DQo-IENoaWVmIEV4ZWN1dGl2ZSBPZmZpY2VyIOKAoiBJbnZpc2libGUgVGVjaG5vbG9naWVzDQo-DQo-IE1hdHRASW52aXNpYmxlLmNvDQo-DQo-IFtpbWFnZTogUmFua2VkICMyIGZhc3Rlc3QgZ3Jvd2luZyBBSSBjb21wYW55IGluIEFtZXJpY2EgaW4gdGhlIDIwMjQgSW5jLg0KPiA1MDAwXSA8aHR0cHM6Ly93d3cuaW52aXNpYmxlLmNvLz4NCj4NCj4gW2ltYWdlOiBMaW5rZWRJbl0NCj4gPGh0dHBzOi8vd3d3LmxpbmtlZGluLmNvbS9jb21wYW55L2ludmlzaWJsZS10ZWNobm9sb2dpZXMtaW5jLS8-W2ltYWdlOiBYXQ0KPiA8aHR0cHM6Ly94LmNvbS9pbnZ0ZWNoaW5jPltpbWFnZTogWW91VHViZV0NCj4gPGh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL0BpbnZpc2libGV0ZWNobm9sb2dpZXM-DQo-DQo-DQo-DQo-DQo-DQo-ICpGcm9tOiogQ2FybCBTYW5kYmVyZyA8Y2FybC5zYW5kYmVyZ0BpbnZpc2libGUuZW1haWw-DQo-ICpTZW50OiogV2VkbmVzZGF5LCBKdWx5IDE2LCAyMDI1IDEwOjE1IFBNDQo-ICpUbzoqIE1hdHRoZXcgRml0enBhdHJpY2sgPG1hdHRAaW52aXNpYmxlLmVtYWlsPg0KPiAqQ2M6KiBuYWRlYXVtQGhtYy5oYXJ2YXJkLmVkdTsgc211cm55eXlAaG1jLmhhcnZhcmQuZWR1Ow0KPiBtYWpld3NraWRAaG1jLmhhcnZhcmQuZWR1OyBBbGV4aXVzIFdyb25rYSA8YWxleGl1c0BpbnZpc2libGUuZW1haWw-Ow0KPiBBbGVrc2VpIFNoa3VyaW4gPGFsZWtzZWlAaW52aXNpYmxlLmVtYWlsPjsgVXNoYSBSYW8NCj4gPHVzaGEucmFvQGludmlzaWJsZS5lbWFpbD47IFJpY2hhcmQgWnVyb2ZmIDxyaWNoYXJkLnp1cm9mZkBpbnZpc2libGUuZW1haWw-Ow0KPiBYdWVxaSBDaGFuZyA8eHVlcWkuY2hhbmdAaW52aXNpYmxlLmVtYWlsPg0KPiAqU3ViamVjdDoqIFJlOiBJbnZpc2libGUvSE1DIGNvbm5lY3QNCj4NCj4NCj4NCj4gTWlrZS0tSnVzdCBjYXVnaHQgdXAgd2l0aCBNYXR0IHJlZ2FyZGluZyB0aGUgdXBkYXRlZCB0aW1lbGluZSBvbiB5b3VyDQo-IHNpZGUuIFdlJ3JlIGxvb2tpbmcgZm9yd2FyZCB0byB0aGUgbmV4dCBzdGVwcy4NCj4NCj4NCj4NCj4gQ2FybA0KPg0KPg0KPg0KPiBPbiBXZWQsIEp1bCAxNiwgMjAyNSBhdCA5OjU34oCvUE0gTWF0dGhldyBGaXR6cGF0cmljayA8bWF0dEBpbnZpc2libGUuZW1haWw-DQo-IHdyb3RlOg0KPg0KPiBNaWtlLCBhbGwsDQo-DQo-DQo-DQo-IFdlIHJlYWxseSBlbmpveWVkIHRoZSBjb252ZXJzYXRpb24gbGFzdCB3ZWVrLCB0aGFuayB5b3UgZm9yIHRha2luZyB0aGUNCj4gdGltZSBhbmQgSSBkZWZpbml0ZWx5IHRoaW5rIG91ciBwbGF0Zm9ybXMgY291bGQgYmUgaGVscGZ1bCBmb3Igc29tZSBvZiB0aGUNCj4gZGF0YSBjaGFsbGVuZ2VzIHlvdSBhcmUgZ3JhcHBsaW5nIHdpdGgNCj4NCj4NCj4NCj4gSSB3YW50ZWQgdG8gZm9sbG93IHVwIG9uIHRoZSBuZXh0IHN0ZXBzIHdlIGRpc2N1c3NlZCwgdG8gc3RhcnQgdG8gdGhlDQo-IHByb2Nlc3Mgb24gYW4gaW5pdGlhbCBkZXBsb3ltZW50IG9mIG91ciBkYXRhIHBsYXRmb3JtIHRvIGRlbW9uc3RyYXRlDQo-IGV4dHJhY3RpbmcgZXhwb3N1cmUgYW5kIHBvc2l0aW9uYWwgZGF0YSBmcm9tIG1hbmFnZXIgbGV0dGVycy4gSSB0aGluayB3ZQ0KPiBkaXNjdXNzZWQgMyBzcGVjaWZpYyBhY3Rpb25zDQo-DQo-ICAgIDEuIFdlIGNhbiBleGVjdXRlIGFuIE5EQSBpZiB5b3Ugc2VuZCBvdmVyDQo-ICAgIDIuIFdlIGNhbiBzdGFydCB0aGUgcHJvY2VzcyBvZiBwZXJtaXNzaW9uaW5nIG9yIHNldHRpbmcgdXAgYW4NCj4gICAgZW52aXJvbm1lbnQNCj4gICAgMy4gWW91IGNhbiBnaXZlIHVzIGEgc2V0IG9mIHJlbGV2YW50IGRvY3VtZW50cyByZWNlaXZlZCwgcmVkYWN0ZWQgYXMNCj4gICAgbmVlZGVkLCBhcyB3ZWxsIGFzIHRoZSB0YXJnZXQgc3RhdGUgb3V0cHV0cyB5b3Ugd2VyZSBzZWVraW5nIHRvIGRlbGl2ZXINCj4NCj4NCj4NCj4gTGV0IHVzIGtub3cgdGhlIGJlc3Qgd2F5IHRvIGdldCBzdGFydGVkIG9uIHRoaXM_IEEgc3Vic2V0IG9mIHRoaXMgZ3JvdXANCj4gY2FuIGFsc28ganVtcCBvbiB0aGlzIHBob25lIHRvbW9ycm93IGFzIG5lZWRlZCDigJMgQ2FybCBjYW4gcXVhcnRlcmJhY2sgZnJvbQ0KPiBvdXIgc2lkZQ0KPg0KPg0KPg0KPiBNYXR0DQo-DQo-DQo-DQo-DQo-DQo-IDxodHRwczovL3d3dy5pbnZpc2libGUuY28vPg0KPg0KPiAqTWF0dCBGaXR6cGF0cmljayoNCj4NCj4gQ2hpZWYgRXhlY3V0aXZlIE9mZmljZXIg4oCiIEludmlzaWJsZSBUZWNobm9sb2dpZXMNCj4NCj4gTWF0dEBJbnZpc2libGUuY28NCj4NCj4gW2ltYWdlOiBSYW5rZWQgIzIgZmFzdGVzdCBncm93aW5nIEFJIGNvbXBhbnkgaW4gQW1lcmljYSBpbiB0aGUgMjAyNCBJbmMuDQo-IDUwMDBdIDxodHRwczovL3d3dy5pbnZpc2libGUuY28vPg0KPg0KPiBbaW1hZ2U6IExpbmtlZEluXQ0KPiA8aHR0cHM6Ly93d3cubGlua2VkaW4uY29tL2NvbXBhbnkvaW52aXNpYmxlLXRlY2hub2xvZ2llcy1pbmMtLz5baW1hZ2U6IFhdDQo-IDxodHRwczovL3guY29tL2ludnRlY2hpbmM-W2ltYWdlOiBZb3VUdWJlXQ0KPiA8aHR0cHM6Ly93d3cueW91dHViZS5jb20vQGludmlzaWJsZXRlY2hub2xvZ2llcz4NCj4NCj4NCj4NCj4NCj4NCj4gLS0tLS1PcmlnaW5hbCBBcHBvaW50bWVudC0tLS0tDQo-ICpGcm9tOiogbWF0dEBpbnZpc2libGUuZW1haWwgPG1hdHRAaW52aXNpYmxlLmVtYWlsPg0KPiAqU2VudDoqIE5vbmUNCj4gKlRvOiogbWF0dEBpbnZpc2libGUuZW1haWw7IG5hZGVhdW1AaG1jLmhhcnZhcmQuZWR1OyBYdWVxaSBDaGFuZzsgQWxleGl1cw0KPiBXcm9ua2E7IEFsZWtzZWkgU2hrdXJpbjsgVXNoYSBSYW87IHNtdXJueXl5QGhtYy5oYXJ2YXJkLmVkdTsgQ2FybA0KPiBTYW5kYmVyZzsgbWFqZXdza2lkQGhtYy5oYXJ2YXJkLmVkdTsgQ29uZi4gUm0uIChBL1YpIC0gTWlrZSdzIE9mZmljZSAtDQo-IDE2dGggRmxvb3IgLSA0DQo-ICpTdWJqZWN0OiogSW52aXNpYmxlL0hNQyBjb25uZWN0DQo-ICpXaGVuOiogV2VkbmVzZGF5LCBKdWx5IDksIDIwMjUgMzowMCBQTS0zOjQ1IFBNIChVVEMtMDU6MDApIEVhc3Rlcm4gVGltZQ0KPiAoVVMgJiBDYW5hZGEpLg0KPiAqV2hlcmU6KiBodHRwczovL2ludi10ZWNoLnpvb20udXMvai85MTA2OTI3NTQ5Ng0KPg0KPg0KPg0KPiDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIANCj4gTWF0dCBGaXR6cGF0cmljayBpcyBpbnZpdGluZyB5b3UgdG8gYSBzY2hlZHVsZWQgWm9vbSBtZWV0aW5nLg0KPiBKb2luIFpvb20gTWVldGluZw0KPiBodHRwczovL2ludi10ZWNoLnpvb20udXMvai85MTA2OTI3NTQ5Ng0KPg0KPiBNZWV0aW5nIElEOiA5MTAgNjkyNyA1NDk2DQo-DQo-IC0tLQ0KPg0KPiBPbmUgdGFwIG1vYmlsZQ0KPiArMTY0Njg3Njk5MjMsLDkxMDY5Mjc1NDk2IyBVUyAoTmV3IFlvcmspDQo-ICsxNjQ2OTMxMzg2MCwsOTEwNjkyNzU0OTYjIFVTDQo-DQo-IC0tLQ0KPg0KPiBEaWFsIGJ5IHlvdXIgbG9jYXRpb24NCj4g4oCiICsxIDY0NiA4NzYgOTkyMyBVUyAoTmV3IFlvcmspDQo-IOKAoiArMSA2NDYgOTMxIDM4NjAgVVMNCj4g4oCiICsxIDMwMSA3MTUgODU5MiBVUyAoV2FzaGluZ3RvbiBEQykNCj4g4oCiICsxIDMwNSAyMjQgMTk2OCBVUw0KPiDigKIgKzEgMzA5IDIwNSAzMzI1IFVTDQo-IOKAoiArMSAzMTIgNjI2IDY3OTkgVVMgKENoaWNhZ28pDQo-IOKAoiArMSA3MTkgMzU5IDQ1ODAgVVMNCj4g4oCiICsxIDI1MyAyMDUgMDQ2OCBVUw0KPiDigKIgKzEgMjUzIDIxNSA4NzgyIFVTIChUYWNvbWEpDQo-IOKAoiArMSAzNDYgMjQ4IDc3OTkgVVMgKEhvdXN0b24pDQo-IOKAoiArMSAzNjAgMjA5IDU2MjMgVVMNCj4g4oCiICsxIDM4NiAzNDcgNTA1MyBVUw0KPiDigKIgKzEgNTA3IDQ3MyA0ODQ3IFVTDQo-IOKAoiArMSA1NjQgMjE3IDIwMDAgVVMNCj4g4oCiICsxIDY2OSA0NDQgOTE3MSBVUw0KPiDigKIgKzEgNjY5IDkwMCA2ODMzIFVTIChTYW4gSm9zZSkNCj4g4oCiICsxIDY4OSAyNzggMTAwMCBVUw0KPg0KPiBNZWV0aW5nIElEOiA5MTAgNjkyNyA1NDk2DQo-DQo-IEZpbmQgeW91ciBsb2NhbCBudW1iZXI6IGh0dHBzOi8vaW52LXRlY2guem9vbS51cy91L2Fkc213TEZKaFQNCj4NCj4gLS0tDQo-DQo-IEpvaW4gYnkgU0lQDQo-IOKAoiA5MTA2OTI3NTQ5NkB6b29tY3JjLmNvbQ0KPg0KPiAtLS0NCj4NCj4gSm9pbiBieSBILjMyMw0KPiDigKIgMTQ0LjE5NS4xOS4xNjEgKFVTIFdlc3QpDQo-IOKAoiAyMDYuMjQ3LjExLjEyMSAoVVMgRWFzdCkNCj4g4oCiIDExNS4xMTQuMTMxLjcgKEluZGlhIE11bWJhaSkNCj4g4oCiIDExNS4xMTQuMTE1LjcgKEluZGlhIEh5ZGVyYWJhZCkNCj4g4oCiIDE1OS4xMjQuMTUuMTkxIChBbXN0ZXJkYW0gTmV0aGVybGFuZHMpDQo-IOKAoiAxNTkuMTI0LjQ3LjI0OSAoR2VybWFueSkNCj4g4oCiIDE1OS4xMjQuMTA0LjIxMyAoQXVzdHJhbGlhIFN5ZG5leSkNCj4g4oCiIDE1OS4xMjQuNzQuMjEyIChBdXN0cmFsaWEgTWVsYm91cm5lKQ0KPiDigKIgMTcwLjExNC4xODAuMjE5IChTaW5nYXBvcmUpDQo-IOKAoiA2NC4yMTEuMTQ0LjE2MCAoQnJhemlsKQ0KPiDigKIgMTU5LjEyNC4xMzIuMjQzIChNZXhpY28pDQo-IOKAoiAxNTkuMTI0LjE2OC4yMTMgKENhbmFkYSBUb3JvbnRvKQ0KPiDigKIgMTU5LjEyNC4xOTYuMjUgKENhbmFkYSBWYW5jb3V2ZXIpDQo-IOKAoiAxNzAuMTE0LjE5NC4xNjMgKEphcGFuIFRva3lvKQ0KPiDigKIgMTQ3LjEyNC4xMDAuMjUgKEphcGFuIE9zYWthKQ0KPg0KPiBNZWV0aW5nIElEOiA5MTAgNjkyNyA1NDk2DQo-DQo-DQo-IOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgA0KPg0KPg0K"}}, {"partId": "0.1", "mimeType": "text/html", "filename": "", "headers": [{"name": "Content-Type", "value": "text/html; charset=\"UTF-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "body": {"size": 13745, "data": "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"}}]}, {"partId": "1", "mimeType": "image/png", "filename": "image002.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image002.png\""}, {"name": "Content-Disposition", "value": "attachment; filename=\"image002.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image002.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.2"}], "body": {"attachmentId": "ANGjdJ_E9l-D7UiIT863tdS1cT8UnJLd5eSXox0WPpd2oc1NNorZQOb0y-JX3wwtVmIYqCRrNxhFUN9AQh9pz2kh3_hu0DIjY8H4knrS87T0cTZIqsPP6KH-Gk84oKh37-yGHQzZOXesiJXOR47gzKH3ehYuve3uIEfsS_K5G6oLRcKHJjcBCcMMLi-WxgdLeHbjr7jIsmihfjCmWetd1yqCGe7Kb_r6yf3oid-6ehIe-GQtcc-VH8w-Po3Vmj6gMwV1VmwvHAXB3eOysfqorRscq9AfV1u0cvUdO9vXfDGk53FPFofY2iki4jviqte1heHKayCmzzSPhJNhw3aJUr9Yy2zRHu2itOePiwkwLwJd8lFqOQ3qr2JPyb_Vfjq3_ATD6MnYAiySizhuGCLa", "size": 32085}}, {"partId": "2", "mimeType": "image/png", "filename": "image003.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image003.png\""}, {"name": "Content-Disposition", "value": "attachment; filename=\"image003.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image003.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.3"}], "body": {"attachmentId": "ANGjd<PERSON>_JI_UUOzpRrrX7J9isiuqS4EIMu9Bwac0mkL7yFVWYQKH9WfwOy2oI5hEYhqRXu32N_rP-H3D3gQvwDn-TGrfNV5_zQIzAO29VBL9uCCFsF8fNGY6XgyXKTm6_qcHCCI3CV6JtWNfiMSr023kpJGVr4dtBGDT8I1yKXy6TFyneFgqwNQcllIfay6U1BYGMTZ1JQLop22P1eIhGYAFE_CTc7mbXWGJp3xIXJe_Q8vvQWZBo7JQBni_Hpt4_Gz0DMXrIrn36iIl1sEPWZ5fXnHrpqM8jmh4WSeGkwUms-rGd-m5jRXsrZXPCi324RajlATh7wHxozLHYgb4XGEzNvFPNY5k-_RrrQ8eMygz3iExWdDMArlq0MNSAzHqXecVQR3_UBcv8HFosWTZZ", "size": 1582}}, {"partId": "3", "mimeType": "image/png", "filename": "image001.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image001.png\""}, {"name": "Content-Disposition", "value": "attachment; filename=\"image001.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image001.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.1"}], "body": {"attachmentId": "ANGjdJ_futOgAL2Zk8f5bW1Ojsd0TK0VEjUmDCik98MtWEJZPnj0luEY3JfV47yN9-5w46BrFLslBCGcDREpOfOptkFKSb7LBI7mk5lJJeKbPplYTGrnnmioiUA5MBi7igEu56MSoRGb3-s7hVPkTV5GWQt1wc7akPXVu8Vk5Q-WH6un-ksvTs9_Ypp7otWbLZTqQQExfpferTaWyUsW3XlOanBss_oyHAUlGobWOD7rlNwHBKtyofAyiadR-TxR_2K5XKmuHRx6u9aBAskgUs7SD4I3BXhY7-IdCDtEPYR3ssV0aUcb-c2lNDaBQ6v9o9tu7htktbdJy3Xh7IUAxUC-BKvrNCM1pjXMcQyjDszE49JIYDg2C8vlcBvPRy2U7t0J8upbFV6Wp8vmXK-J", "size": 4372}}, {"partId": "4", "mimeType": "image/png", "filename": "image004.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image004.png\""}, {"name": "Content-Disposition", "value": "attachment; filename=\"image004.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image004.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.4"}], "body": {"attachmentId": "ANGjdJ9a7nEkAYwmOyollqTMZoNGcTmQV7Y2g_YE1UYWs-dIVbPA4fBR4hTZ5haL7bEjeQcmQ-bgeL3jiaJeFvYzASY-Re7by2rNlP46goaJcdn8_AYK1XKVTBHS7bt1tsx9CoHt12ZbHBx4GsnFkJ2fHYSrufk3VK2EpL27Rq0j3-WEQLhE6tWz_zE8_2G_eS1ux-cVEfKDZo8gJ8UOPkL3U33RSQqKEAIUnvoDwNMh30Id3b-JIasXh61QcotFVdR1KWKCsMB9U7NfSGmVx-tPm2Kx7-HouYlA2Hk7haVv2zb0T90H5IdpEPhLjj24awKT6Q67QQilmnzcNLBXdk5_-_C9oz8PQzjCAUGlvPdh7ePO50Hx_1l5R2RfcKRPHfBuYtWw_K9u-E-P4R-L", "size": 2137}}, {"partId": "5", "mimeType": "image/png", "filename": "image005.png", "headers": [{"name": "Content-Type", "value": "image/png; name=\"image005.png\""}, {"name": "Content-Disposition", "value": "attachment; filename=\"image005.png\""}, {"name": "Content-Transfer-Encoding", "value": "base64"}, {"name": "Content-ID", "value": "<image005.png@01DBF732.E0CE6610>"}, {"name": "X-Attachment-Id", "value": "358d88fc4d0d8f6a_0.5"}], "body": {"attachmentId": "ANGjdJ88R2IVf-LasisJPSHuP2-L40JIYZF5PgAQ_4t4LbXpgxFkzUM3OAdL98nXQS9c31HqtD46QuJzQDRnpgNONrHpVAgUqARZQ9HAsWNZYFPUzF84s1UvA9U2wMoSL5mIk3_zJ1L24TBgfMBGGnYkbmOB8KgYCULwqucJKFd0Si1Xzf6f1yx_0U7M05z6DyzNoOgJBXa1aygLTsWtl5twlBjs7qBibzNO-8mCG9QWHYAs1-gW3AQfE-gIOsxnln2soZ_1GWRyAesfvaC700qjnZhhAJAk9OIE7FA4lWjjj4C4skKgLlY88HSw4lhxntHqjghR_DphN383yUfjFO4-x2SxX1bBy0vjXPJUYQi6AmzG0VBTa4ndsoP4mItemFeuGQVwjTL8TqL76gVg", "size": 1613}}]}, "sizeEstimate": 85867, "historyId": "2539657", "internalDate": "1752787989000"}]}}