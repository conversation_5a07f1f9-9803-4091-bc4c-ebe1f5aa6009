#!/usr/bin/env python3
"""
Terminal-friendly monitor for VSCode terminal pane
Provides a clean, updating display of the monitor status
"""
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from datetime import datetime
import time

console = Console()

def create_monitor_display(check_count=0, last_check=None, last_change=None):
    """Create a rich display for the monitor status"""
    table = Table(title="🤖 NotePlanBot Monitor", show_header=True, header_style="bold magenta")
    table.add_column("Status", style="cyan", width=20)
    table.add_column("Value", style="green")
    
    table.add_row("Monitor Status", "✅ Running")
    table.add_row("Bridge File", "bridge.md")
    table.add_row("Check Interval", "30 seconds")
    table.add_row("Checks Performed", str(check_count))
    table.add_row("Last Check", last_check or "Starting...")
    table.add_row("Last Change Detected", last_change or "None yet")
    
    panel = Panel(
        table,
        title="[bold blue]Todo List Monitor - Third Pane[/bold blue]",
        subtitle="[dim]Press Ctrl+C to stop[/dim]"
    )
    
    return panel

def main():
    """Run the monitor with a nice terminal display"""
    console.print("[bold green]Starting NotePlanBot Monitor...[/bold green]")
    console.print("[yellow]This terminal will show monitor status[/yellow]")
    console.print("[yellow]Edit bridge.md in the first pane to trigger changes[/yellow]\n")
    
    check_count = 0
    last_change = None
    
    with Live(create_monitor_display(), refresh_per_second=1) as live:
        while True:
            try:
                check_count += 1
                last_check = datetime.now().strftime("%H:%M:%S")
                
                # Update display
                live.update(create_monitor_display(
                    check_count=check_count,
                    last_check=last_check,
                    last_change=last_change
                ))
                
                # In a real implementation, this would check for file changes
                # For now, just sleep
                time.sleep(30)
                
            except KeyboardInterrupt:
                console.print("\n[red]Monitor stopped by user[/red]")
                break

if __name__ == "__main__":
    # Import and run the actual monitor in parallel
    from NotePlanBot.todolist_monitor import TodoListMonitor
    import threading
    
    # Start the real monitor in a background thread
    bridge_file = os.path.join(
        os.path.dirname(__file__),
        "src", "NotePlanBot", "files", "bridge", "bridge.md"
    )
    
    def run_monitor():
        monitor = TodoListMonitor(bridge_file)
        monitor.run()
    
    monitor_thread = threading.Thread(target=run_monitor, daemon=True)
    monitor_thread.start()
    
    # Run the display
    main()