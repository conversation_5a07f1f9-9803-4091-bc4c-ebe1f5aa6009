# CLAUDE.md - PA System Prompt

This file contains instructions for how to perform PA (Personal Assistant) tasks effectively. Keep this updated with working procedures.

## Core PA Capabilities

### Email Management
```bash
# Check email status
cd NotePlanBot/gmail_tool && just status

# Open interactive Gmail UI
cd NotePlanBot/gmail_tool && just ui

# Generate draft replies
cd NotePlanBot/gmail_tool && just drafts
```

**Gmail UI Commands:**
- `1-10`: Open thread by number
- `3`: Group emails using AI
- `4x`: Snooze thread x (e.g., '41' snoozes thread 1)
- `5x`: Create task from thread x
- `n/p`: Next/previous page
- `q`: Quit

### Email Search via Code
```python
from src.tools.gmail.service import GmailService
gmail = GmailService()

# List unread messages
messages = gmail.list_unread_messages(max_results=25)

# Search with query
messages = gmail.search_messages("from:<EMAIL>")

# Get full thread
thread = gmail.get_thread(message['threadId'])
```

### NotePlan Search

**Using Grep (fastest for known keywords):**
```bash
# Search all notes for a term
grep -r "Matt F" ../Notes/

# Search with context (2 lines before/after)
grep -r -C 2 "meeting" ../Notes/

# Search calendar files
grep -r "Thursday" ../Calendar/

# Case-insensitive search
grep -ri "ceo" ../Notes/
```

### NotePlan Todo Format

**Checkbox vs Task:**
- Checkboxes: `+ [ ]` (incomplete) or `+ [x]` (complete)
- Tasks: `- [ ]` (incomplete) or `- [x]` (complete)

**Hierarchy:**
- Indented items are sub-items of the first unindented item above
- Use tabs or spaces for indentation
- Example:
```
+ [ ] Main checkbox
	+ [ ] Sub-checkbox 1
	+ [x] Sub-checkbox 2
- [ ] Main task
	- [ ] Sub-task
```

**Using NotePlan Reader (for structured parsing):**
```python
from src.NotePlanBot.reader.noteplan_reader import NotePlanReader
reader = NotePlanReader()

# Read specific note
content = reader.read_note("../Calendar/20250714.txt")

# Extract tasks from note
tasks = reader.extract_tasks(content)  # Gets - [ ] items
checkboxes = reader.extract_checkboxes(content)  # Gets + [ ] items

# Get todos with hierarchy
todos_tree = reader.extract_todos_with_hierarchy(content)
# Returns list of todo items with 'children' arrays

# Get today's todos
today = reader.get_today_todos()
# Returns: {'tasks': {'incomplete': [...], 'complete': [...]}, 
#           'checkboxes': {'incomplete': [...], 'complete': [...]}}

# Get outstanding todos from past N days
outstanding = reader.get_all_outstanding_todos(days_back=7)
```

**Using NotePlan Calendar functions:**
```python
from src.NotePlanBot.reader.noteplan_reader_calendar import NotePlanCalendar
cal = NotePlanCalendar()

# Get calendar entries for date range
entries = cal.get_calendar_entries(start_date, end_date)
# Returns dict with dates as keys, each containing:
# {'date', 'day_name', 'events', 'tasks', 'checkboxes', 'raw_content'}

# Add calendar event
cal.add_calendar_event('20250717', '10:00', 'Prep for Matt F meeting')

# Create iCal format (for export/sharing)
ical_str = cal.create_ical_event(date, '14:00', 'Team meeting', duration_hours=1)
```

**Using Glob for file discovery:**
```bash
# Find all calendar files for this month
ls ../Calendar/202507*.txt

# Find all notes in Vision project
ls ../Notes/10*Projects/Vision/*.txt

# Find recently modified notes
ls -lt ../Notes/**/*.txt | head -20
```

### Calendar Management

**Check schedule:**
```python
# Today's calendar
today = datetime.now().strftime("%Y%m%d")
calendar_path = f"../Calendar/{today}.txt"

# Read and parse events
content = reader.read_note(calendar_path)
# Events start with HH:MM format
# Tasks start with "- [ ]" or "- [x]"
```

**Add calendar event:**
```python
from src.NotePlanBot.editor.noteplan_editor import NotePlanEditor
editor = NotePlanEditor()

# Add to specific date
date_str = "20250717"
event_line = "11:00 Meeting with Matt F"
editor.add_line_to_note(f"../Calendar/{date_str}.txt", event_line)
```

### PA Dashboard Usage
```bash
# Run full PA dashboard
just pa

# Quick actions available:
# 1. Open Gmail UI
# 2. Draft email replies
# 3. Create task from email
# 4. Add calendar event
# 5. View full email list
# 6. Search notes
```

### Task Management

**From Bridge File:**
- Monitor checks `src/NotePlanBot/files/bridge/bridge.md` every 30 seconds
- Changes > 1 LOC trigger Claude Code
- Tasks section passed to Claude for interpretation

**From Emails:**
- Use Gmail UI option `5x` to create task from thread x
- Tasks stored in `gmail_tasks.json`
- AI generates action-oriented titles

### Project Context

**Find project information:**
```python
# Check project store
from src.tools.gmail.storage.project_store import ProjectStore
store = ProjectStore()

# Get all projects
projects = store.get_all_projects()

# Find person's projects
projects = store.get_person_projects("Matt Fitzpatrick")
```

### Memory Management

**Long-term memories:**
- Stored in `./pa_memories/long_term_memories.md`
- Single-line entries for important facts
- People, roles, preferences, key dates

**Session context:**
- Check `../PROMPTS/every.txt` for user preferences
- Review recent calendar entries for context
- Check `zTasks.md` for current priorities

## Common PA Workflows

### Meeting Preparation
1. Search emails for meeting invite: `grep -r "meeting_name" .`
2. Check calendar for conflicts: `cat ../Calendar/YYYYMMDD.txt`
3. Search notes for previous meetings: `grep -r "person_name" ../Notes/`
4. Review project context from Excel store

### Daily Summary
1. Run `just pa` for dashboard view
2. Check unread emails: `just email-status`
3. Review today's calendar and tasks
4. Check upcoming events (next 3 days)

### Email Triage
1. Open Gmail UI: `just email`
2. Group by topic (option 3)
3. Snooze non-urgent (option 4x)
4. Create tasks from important emails (option 5x)
5. Draft replies: `just drafts`

## Important Paths
- NotePlan Notes: `../Notes/`
- Calendar Files: `../Calendar/YYYYMMDD.txt`
- Gmail Tool: `./NotePlanBot/gmail_tool/`
- Bridge File: `./src/NotePlanBot/files/bridge/bridge.md`
- Task Lists: `./src/NotePlanBot/files/todolists/`
- Long-term Memory: `./pa_memories/long_term_memories.md`
- CRM Files: `./CRM/fname_sname.md` (people tracking)

## CRM System
- Store individual contact information in `./CRM/fname_sname.md`
- Track email addresses, roles, context, conversation history
- Update when learning new information about people
- Use lowercase format: `firstname_lastname.md`
- Hiring subdirectories:
  - `./CRM/Hiring/Leads/` - Potential candidates to pursue
  - `./CRM/Hiring/Connectors/` - People who can make introductions
- Research script: `./CRM/Hiring/research_candidate.py` for comprehensive candidate research

## System Details
- Python commands need `python3` not `python`
- Virtual envs at `.venv` (not `venv`)
- Claude Code at `/Users/<USER>/.claude/local/claude`
- Gmail auth stored in `gmail_token.pickle`
- API keys in `.env` files

## Maintenance
- Update this file when discovering new workflows
- Test commands before documenting
- Keep examples concrete and working
- Focus on "how to do" not "what is"