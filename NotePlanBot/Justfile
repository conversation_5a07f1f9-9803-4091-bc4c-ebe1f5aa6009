# NotePlanBot Justfile - Quick command reference for developers
# Run 'just' to see available commands
# 
# Monitor variants:
#   just run           - Basic monitoring, no UI
#   just monitor-simple - Simple status display
#   just monitor       - Full three-pane UI (recommended)

# Default recipe - show help
default:
    @just --list

# 🤖 Personal Assistant Dashboard
pa:
    cd af_pa1 && PYTHONPATH=../src:../gmail_tool/src python3 pa.py

# 📧 Gmail UI
email:
    cd NotePlanBot/gmail_tool && just ui

# 📧 Draft email replies
drafts:
    cd NotePlanBot/gmail_tool && just drafts

# 📧 Email status
email-status:
    cd NotePlanBot/gmail_tool && just status

# Run the bot (basic monitoring, no UI)
run:
    PYTHONPATH=src python run.py

# Run with custom bridge file
run-bridge FILE:
    PYTHONPATH=src python run.py {{FILE}}

# Run monitor with simple status UI
monitor-simple:
    PYTHONPATH=src python monitor_terminal.py

# Run monitor with full three-pane UI (recommended)
monitor-ui:
    PYTHONPATH=src python -m NotePlanBot.visualizer

# Aliases for common use
monitor: monitor-ui
visualizer: monitor-ui

# Test NotePlan reader
test-reader:
    PYTHONPATH=src python -m NotePlanBot.reader.noteplan_reader

# Test NotePlan editor
test-editor:
    PYTHONPATH=src python -m NotePlanBot.editor.noteplan_editor

# Show configuration
config:
    PYTHONPATH=src python -m NotePlanBot.config

# Install dependencies
install:
    pip install -e .

# Install dev dependencies
install-dev:
    pip install -e ".[dev]"

# Run tests (falls back to regression tests if pytest not installed)
test:
    pytest tests/ 2>/dev/null || PYTHONPATH=src python tests/test_regression.py

# Format code
format:
    black src/ tests/
    ruff format src/ tests/

# Lint code
lint:
    ruff check src/ tests/

# Clean up
clean:
    find . -type d -name __pycache__ -exec rm -rf {} +
    find . -type f -name "*.pyc" -delete
    rm -rf .pytest_cache
    rm -rf .ruff_cache

# Create virtual environment
venv:
    python -m venv .venv
    @echo "Run 'source .venv/bin/activate' to activate"

# Update CLAUDE.md with current status
update-claude:
    @echo "Remember to update CLAUDE.md with implementation details!"

