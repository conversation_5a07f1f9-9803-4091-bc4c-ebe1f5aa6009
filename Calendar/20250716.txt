# CORE - OG
Two potential wins
Initial token spend.
GLEAN; 
We need to scale up capacity per person.
We need to 10x the productivity of these 10 people.
Case study: Apollo: they have a C-level driven $200 million AI transformation budget for this. we just need to take it. How? 
Hire people.
X people
Charge y .. margin something something
This is okay, this scale is somewhat slow slowly, but we can make money from this
We could spend years ramping up with an Apollo but we need to ramp up as quickly as possible and get this product working as quickly as possible so that we can get this large piece of pie as we can and find the way to capture and prioritise everything that we’re doing there so that we can run rampant through the rest of the financial services industry.
1. Discovery
2. Automation Automation
Requirements factory framing to avoid <PERSON> toes
— here is what we’ve found.
— 
Apollo - recon and rare scenario of c level want to transform. 200m. Expand.
Expand across fin serve
Our engineers get 10x productivity
Discovery automation automation
The funds are a large market that we can do well in by themselves, we think the really big opportunity here though is the portfolio companies. Something about in a world where you can’t automate the entire job of a person you then need to have lots of interfaces between the humans and the automations and this requires a lot of work and a lot of governance change Something something work out how to make this point the point is private equity funds are quite uniquely positioned and trained to actually push through the amount of change and work required to do high scale automation.
# CORE - Great Lines
**Story (narrative):** We frame a rapid‐scale growth plan centered on the Apollo opportunity. The narrative is that Apollo (with a $200M innovation budget) is a rare, C‐level–driven chance to transform their operations, and they trust us. By deploying our new automation tools (the Domain Knowledge Extractor and the “Requirements Factory”), we boost each engineer’s output 10× and quickly capture as much of Apollo’s budget as possible (we’re already eyeing ~$9M by year’s end). We then use this momentum to expand across Apollo’s portfolio companies and the broader financial services industry. In short, **“We have a proven entry point with Apollo, cutting‐edge automation tech to supercharge productivity, and a hiring plan to scale fast – all combining to seize a big slice of the financial services market.”**

**Safe Plan**
1. Hire 10 serious engineers a month and run a tight pipeline for deploying them crashing out dozens and dozens of prompt engineered automation features.
2. 40%. 10 people per month, $200k each, charge at $333k, = $133k *10 per year per month. 
3. $1m new GP per month.
4. We have to be able to meaningfully deploy them → discovery + mgmt + automation pipelines.


**Breakthroughs**
• We have had, we think, two big breakthroughs.
Context: We are confident in the reality that enterprises today already have not hundreds but thousands of tasks that they could automate using today's LLM technology-level. That number is growing way, way faster than they're making even a dent in the task list. [show growth chart A vs need chart B, then show startup appears and dominates enterprise].
Enterprises are in a kind of danger they've never faced before. 
Protecting them from that danger is very valuable, and the story sells. It will only sell easier in 6,12,18 months.
What if we were on  a trajectory to be able to target not just one bespoke automation project here, one unique and complex automation project there, but high-scale automation tooling and rollout?
	– Cortex / Requirements Factory (automated spec-driven build & test loop). 10x FDE productivity. 
	- **PFC / Requirements Factory. ** *Automation Automation*. [**Move to opening of main slide>>**. The human 'PFC' (Pre-frontal-cortex) is extremely slow and expensive. Its job is to tell stories (trajectories) to figure out how to do things, so that the various subconscious fast-brain can learn new tricks at incredible speed. ]PFC / Cortex / Requirements Factory turns terribly defined, fuzzy tasks into rigorously testable specs at scale. 10x FDE productivity. Synthetic testing and high-scale feedback signal starts to open the door to very high scale strategies. Compatible with any agentic platform as focused on *meta-automation*. At-scale, automated feedback signal.
	- **Automation Guilds / Domain Knowledge Extractor**.  Enterprise *Value of information*. The most valuable enterprise reconnaissance possible right now is **"which**, of my thousands of white collar **tasks** performed **today** by hundreds of different operational roles - **are** automatable?". Using en-masse QnA and confidence ensembling, we are moderately confident we can answer this question for any CE/FO, and potentially more importantly, a PE fund doing DD. [**Remove this.>>** ~~~We answer it via observation, automated decomposition, and using automation automation to see~~~] We scan & extract then attempt to (fully) automatically automate _all_ their tasks, automatically. Succeeding isn't the target, how far each task gets; is. If Cortex / Requirements Factory can get '70% of the way there' for any given task by itself... that tells us that it can _VERY LIKELY_ be Automated at production-grade by smart human specialists and better domain knowledge extraction.

	We have some serious AI firepower brewing but we're missing a winning edge.
	
	

### Includes
- **Concrete results/pilots:** There’s no mention of pilot data or early results to back up claims (e.g. evidence of 10× productivity or past wins). Even a brief case study or proof point would strengthen the story. → Early versions of the Requirements Factory did about 80% of the work for several prompt deliverables. In two days, David delivered three prompts to Apollo using it whilst I was still working on the first (which was now not necessary).
- Aaron note in Requirements Factory page.
- Proof in the pudding / 
	- Concrete results / pilots.


#### Timeline / Plan
- **Plan and next steps:** Details like a high-level timeline, hiring ramp, and how we hit milestones would shore up the story. For instance, mention how the proposed 40% margin and “10 hires per month” plan achieves the $9M target.
- Show 'just 10 hires a month 40% margin plan' vs., RequirementsFactory take-off 10x-ing those 10 hires a month (ramp to over 2 months) vs., DKE working and the SIZE OF THAT MARKET (huge huge huge)

## Now



## Other
- [ ] frame requirements factory



+ Message the Jeremy pitch to Alexei and Dan Collins, ask for any last minute suggestions / pointers. Any of this tread on anyone's toes?  Alexei → if this works, can you see it fitting into your vision?

# Make fucking sure
1. List of Asks prepared
2. Steve → resources
3. V smart industrious high dopamine associate to work directly with me. Never not had it, productive efficiency is down 70%, which is still v high but it's a total waste. Compensating w unsustainable sleep & number of hours (80-90 pw), age just started kicking and I can't get away with the sleep as I was.
2. Include Whimsical Diagram
3. Taking a serious bet on Domac.
	4. Joins R&D team.
	5. Salary → $120k. 
	6. David Hire Balcans → Five * $50k each.
7. Invisible Agents Automation Guild
	8. VDI → virtual machine.
	9. Speak aloud to say what they're going to do. → transcribed.
	10. Also write it down.