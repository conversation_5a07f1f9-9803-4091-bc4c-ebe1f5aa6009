- [ ] How this plays with <PERSON><PERSON> is important → don't appear to be overlapping.
- [ ] Core insight → get from Caspar recording
- [ ] Optimization → Continuous Re-alignment
- [ ] Value of Information

This is how you transform a massive enterprise.
Discovery at scale → Discovery as Sales
Solutions at scale. → 

Think = role = a bc d e tasks.
Reality = role = hundreds of tasks.
Dozens of roles, hundreds of tasks, thousands of agents required.
Something about timeline pressure.
You don't need to be able to handle all cases if you fall out to a human.


### Dynamic Few Shot = 
[From here](https://invisibletech-team.slack.com/archives/C06JWBXDS8L/p1750425635111139?thread_ts=**********.593349&cid=C06JWBXDS8L). Dynamic / active few shot = something like: 
```
func (prompt: Prompt , input: str, bank_of_edge_case_IO_examples) → Prompt:
   x = is_input_likely_an_edge_case(input)
   if x:
       edge_case_examples = llm(
           "based on {prompt} and {input}, "          
           "select the three most relevant examples from { bank_of_edge_case_IO_examples }."
           "Output JSON. shots: [three examples, verbatim] , reasoning_for_each: [why included]"
       )           
       return prompt + \n\n + '<examples>' +\n+ edge_case_examples +</examples>
  return prompt
```
few shot included for edge cases but otherwise left out.

