+ [x] Buy XMR, £500 me £500 Keri
	+ Send to Will
+ Pay Thames Water
	+ may be <PERSON>’s account. Else <PERSON> to pay me £75
+ [x] Buy Cables etc
	+ Get paid £170 from house account.
+ Pay Will Monero #important 

- [x] Do we have a time for Thursday?
- [x] ∆ 18:15 - 20:30 Call Domac → ABSTRACT
- [ ] Check Vas' work.
- [ ] Cortex E2E Test Cases. From Will: Deliveroo: [Link](https://docs.google.com/spreadsheets/d/1yJj-PqjLQ0S4buA18PIbOVrsLNwQB7X6CyUyKqHaGPg/edit?gid=**********#gid=**********)

### Note to Jeremy
**<PERSON>**:
That's awesome man!
There's just a lot more competition and sensitivity, sales is hard.
I'm pitching our two 'silver bullets' to <PERSON> on Thursday, we'll see if it sticks or we're just going scale the Apollo approach
(Apollo approach = get in, get integrated, hire loads of engineers, do lots of discovery and make lots of prompts, chains, agents for people to use, deploy deploy deploy)
It works, but I don't love it as a model.
**<PERSON>**:
what are the silver bullets you're pitching?
**<PERSON>**:
Silver bullets = Requirements Factory / Cortex → spend tonnes of tokens working out how to solve as much of an LLM problem automatically as possible via synthetic test cases and requirements judges. Very similar to what the foundation models were doing with web trajectories. Doesn't matter how much it costs to solve it as once it's solved, it's automated.
and Domain Knowledge Extractor / Automation Guilds. It's more controversial. We've been using watchers to take loads of screenshots and abstract DOM details of someone working. Multiple layers of abstraction via LLMs and technique similar to David and my "en-mass QnA" concept he deployed for Nvidia.
Hyper-Rich-Observation-Data → tasks+subtasks → try to automate all of them automatically.
Then the hopeful slam dunk of it is value of information.
•  Toy Model: If you refurb a house for £200k and sell it for £400k more, value of refurb is £200k.
•  A tool that tells you which house would sell for £800k more for a £200k refurb... that information is worth £400k.
upshot → automatically automating orchestrations up to production is probably just way too hard.
But if 'how far the automatic automation got by itself' is a strong signal on whether it could be production automated...
We'd be selling very valuable information.
And it's kind of the ideal sales platform; knowing 1000 tasks that could be automated with enough effort across an org.

### PRD Overview → Claude
PRD Overview → 3 critical questions. [Claude](https://claude.ai/artifacts/inspiration/3d81ba29-d1ad-4e9b-b58e-3e0f46ba8afd)
1.  I've been working on a number of approaches for a feedback system for our Agentic platform. What I want and I've been working on is the idea that you have a single spec file that a developer is working on, and in the background, you have a series of loops that is looking at that file and any other context that's required, saying "Right, is there something new that needs to be done or something that I should change?" It looks at what's been made already or what's been done at the task logs, and whilst you're working on this spec file which has the initial problem and the requirements and the various scenarios that you think we should test this with, it's sort of working away and it has a whole load of system prompts for different general components that it should be making for things in general be working on AI features, not front-end stuff. The hard bit really is just having it fire off these new Claude code instances and keeping an eye on everything, so trying to work out what the most important next components are. I was working on the like a chat interface for that to be the central thing rather than just a requirements document edited by like vibe coding. That's where we're at. 
2. Target users are Enterprise CEOs and CFOs. The problem this solves is: we want to be able to 50-70% automate some very large number of tasks. They don't have to be done up to human level, this is just a way of generating signal as to what can and can't be automated. We already have a system for watching and tracking a really large amount of extracting tasks from watching people work automatically. And then this is the part that just works really really hard, spends a lot of tokens to attempt to automate that task. The important thing is that we see how far it gets and that we use the requirements-based grading system to measure that. 
3. Whether the agentic system is able to automate a very large number of tasks given a lot of support initially, and whether the system is able to course correct critically because we're happy to spend the tokens if it needs to be able to spot it veering away from its requirements and specifications itself by continuously coming back and reviewing its specification. 

### Results in this PRD:
# One-pager: Agentic Task Automation Platform

## 1. TL;DR
An autonomous feedback system that continuously monitors and improves task automation for enterprise workflows. The platform uses AI agents to process specification files, automatically generate code components, and self-correct through requirements-based feedback loops. Designed for Enterprise CEOs and CFOs who need to identify and automate 50-70% of large-scale business tasks without requiring human-level performance.

## 2. Goals
### Business Goals
* Achieve 50-70% automation rate across enterprise task portfolios
* Generate actionable intelligence on task automation feasibility
* Reduce manual oversight requirements through self-correcting AI systems
* Maximize ROI on AI token spend through targeted automation attempts

### User Goals
* Identify which business processes can be effectively automated
* Reduce time spent on routine task management and execution
* Gain visibility into automation progress and success rates
* Scale operational efficiency without proportional headcount increases

### Non-Goals
* Achieve human-level performance on all automated tasks
* Replace human decision-making for strategic initiatives
* Provide front-end development automation capabilities
* Support individual contributor or small team use cases

## 3. User stories
**Enterprise CEO:** "I need to understand which of our thousands of daily tasks can be automated so I can make informed decisions about operational scaling and resource allocation."

**Enterprise CFO:** "I want to quantify the ROI of our AI automation investments and identify the highest-impact areas for further automation development."

**Operations Leadership:** "I need a system that can attempt task automation at scale while providing clear metrics on success rates and failure modes."

## 4. Functional requirements
### Core Platform (P0)
* Specification file processing and continuous monitoring
* Multi-agent feedback loops for requirement validation
* Automated Claude code instance generation and management
* Requirements-based grading system for automation assessment

### Intelligence Layer (P1)
* Task extraction from user behavior monitoring
* Priority ranking for component development
* Self-correction mechanisms for specification drift
* Chat interface for central requirement management

### Enterprise Features (P2)
* Token usage optimization and cost tracking
* Automation success rate dashboards
* Task portfolio analysis and reporting
* Integration with existing enterprise workflow systems

## 5. User experience
### Primary User Journey
* Executive defines automation goals and uploads task specifications
* System continuously monitors spec files and generates automation candidates
* AI agents attempt task automation with real-time feedback loops
* Requirements-based grading provides automation success metrics
* Dashboard shows automation rates and identifies optimization opportunities

### Edge Cases and UI Notes
* Handle specification ambiguity through clarification requests
* Provide fallback mechanisms when automation attempts fail
* Support iterative refinement of automation requirements
* Enable manual intervention points for critical task corrections

## 6. Narrative
Sarah, a CFO at a Fortune 500 company, starts her Monday morning by reviewing the Agentic Platform dashboard. Over the weekend, the system processed 847 routine tasks across finance, operations, and customer service. The platform shows that 582 tasks (69%) were successfully automated, with detailed breakdowns by department and task complexity.

The system flagged 23 tasks that began deviating from their original specifications and automatically course-corrected using the requirements-based feedback loops. Sarah notices that invoice processing automation improved from 45% to 73% success rate after the system refined its approach based on continuous spec monitoring.

She opens the chat interface to modify requirements for expense report automation, knowing that the AI agents will immediately begin testing new approaches in the background. By lunch, she receives a notification that the updated specifications have been validated and are ready for deployment across the organization.

## 7. Success metrics
* **Automation Success Rate**: 50-70% of attempted tasks successfully automated
* **Self-Correction Accuracy**: 90%+ of specification drift detected and corrected
* **Task Coverage**: Number of unique enterprise tasks processed monthly
* **Token Efficiency**: Cost per successfully automated task
* **Time to Automation**: Average time from spec creation to working automation
* **Requirements Compliance**: Percentage of automations meeting original specifications

## 8. Milestones & sequencing
### Phase 1: Core Infrastructure (Months 1-3)
* Build specification file monitoring system
* Implement basic feedback loop architecture
* Create requirements-based grading framework
* Deploy MVP with single enterprise pilot

### Phase 2: Intelligence Layer (Months 4-6)
* Add multi-agent coordination capabilities
* Implement self-correction mechanisms
* Build chat interface for requirement management
* Expand to 3-5 enterprise customers

### Phase 3: Enterprise Scale (Months 7-12)
* Optimize token usage and cost management
* Build comprehensive analytics dashboard
* Add advanced task prioritization algorithms
* Scale to 50+ enterprise deployments 