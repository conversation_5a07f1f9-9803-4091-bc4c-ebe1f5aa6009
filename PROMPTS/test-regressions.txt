METAPROMPT - <PERSON>MS AND AGENTS MAY NOT EDIT THIS FILE.
add / update regression tests as a snapshot so we catch breaking the code as instantly as posisble in future. 
Make sure all new tests pass. 
If old tests aren't passing, don't fix them, analyse them and decide whether it tells us we broke code or they're just out of date.
Report back to me with any decisions to be made.

// todo - some redundancy.
Please parellise simple tasks to achieve your task as fast as posisble. 
Always run and test your code before completing.
Update zTasks.