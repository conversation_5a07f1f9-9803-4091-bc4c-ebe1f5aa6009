TODO - remove the specific stuff.
NOTE - this was based on the autoprompt 3July ./._tmp/Glossary.md we did first after thinking the poblem through.

> ok look at @_tmp/_glossary.py I've been told by a few friends that it's a good process to start off by defining your pedantic 
  classes and trying to be ruthless about what you need and what you don't. Write down both, then comment out what you don't need
   so you can expressly choose not to include things.

  I don't think those classes should generally involve the functions that you need. Some of the classes do have functions in 
  them, like where's the boundary here in the process that we're talking about? How should I define for myself the class writing 
  step? Where are we going to define the pedantic classes? The types? It's types really isn't it? Before moving on to the highest
   level business logic.
  ⎿  Read _tmp/_glossary.py (373 lines)

✻ Thinking…

  Looking at the glossary file, I can see there's a comprehensive set of definitions and a 10-step plan for building a test case 
  generator. The user is asking about the process of defining pedantic classes (Pydantic classes) and where to draw boundaries.

  Let me analyze what's in the file:
  1. There's a GLOSSARY class with various attributes defining terminology
  2. There's a 10-step plan starting with models.py (Pydantic data models)
  3. There are class diagrams and sequence diagrams

  The user is asking about:
  1. The process of starting with pedantic (Pydantic) classes
  2. Being ruthless about what you need vs what you don't
  3. Where the boundary is between class definitions and functions
  4. Where to define the Pydantic classes/types before moving to business logic

  Based on the file, it seems like Step 1 (models.py) is exactly what they're asking about - defining the core data models 
  without the business logic.

  Let me provide a clear answer about this architectural approach.

⏺ Looking at your glossary, you're absolutely right about starting with Pydantic classes. This is a solid architectural approach.
   Here's how I'd think about the boundaries:

⏺ Update Todos
  ⎿  ☐ Define core Pydantic models in models.py - data structures only, no business logic
     ☐ Review and comment out unnecessary fields/models from initial design
     ☐ Create minimal test cases to verify model instantiation and validation

✻ Thinking…

  The user has updated the glossary with additional comments/questions about implementation details. They're asking about the 
  boundary between defining classes (types) and implementing functions. Let me provide a clear framework for how to approach 
  this.

⏺ The boundary is clear: Pydantic models should be pure data contracts.

  Here's the architectural approach:

  1. models.py (Pure Data Structures)
  - Only type definitions and validation rules
  - No methods that "do work" (no API calls, no generation logic)
  - OK to have: validators, computed properties, serialization helpers
  - Comment out what you're unsure about

  2. The Boundary Test
  Ask yourself: "Does this belong to the shape of the data or the manipulation of the data?"
  - Shape → models.py
  - Manipulation → separate modules

  3. Your Specific Case





  ⏺ Perfect! The models are working correctly. The test demonstrates:

  1. All models instantiate successfully with realistic example data
  2. Validation works - The realism score validation correctly rejected
   a value of 15.0 (max is 10.0)
  3. JSON serialization/deserialization works - Important for
  API/storage
  4. Properties calculate correctly - Coverage metric shows 50% (1 test
   case for 2 scenarios)
  5. Type validation works - All fields accept appropriate data types
  6. Default values work - Optional fields and factory defaults

  The models are ready to use as the foundation for the test case
  generator. Each model has:
  - Proper documentation
  - Field validation
  - JSON schema examples
  - Type hints
  - Appropriate constraints

  Next steps would be to implement the actual generation components
  that use these models.






  Great. Add end-of-line concise comments that explain why each thing is as it
   is and not some other way / why it's necessary vs we could get rid of it, 
  what NEEDS it?







  EXAMPLE





  """
Test Case Generator Data Models

This module defines the Pydantic models for the test case generation system.
All models use Pydantic for validation and serialization.
"""

from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field, field_validator
from datetime import datetime


class TaskProblem(BaseModel):
    """
    Represents a client's problem/task that needs automation.
    This is the top-level instruction or desire from the client.
    """
    description: str = Field(..., description="Natural language description of the problem")  # REQUIRED: Core input from user, drives all generation
    domain: str = Field(..., description="Problem domain (e.g., 'CRM', 'finance', 'healthcare')")  # REQUIRED: Helps LLM contextualize & generate relevant data
    constraints: Optional[List[str]] = Field(default=None, description="Any specific constraints or requirements")  # OPTIONAL: Guides test case boundaries
    
    class Config:
        json_schema_extra = {
            "example": {
                "description": "I have a document containing information about past contractors. I want to find the best one for certain use cases.",
                "domain": "CRM",
                "constraints": ["Must consider expertise area", "Should provide reasoning"]
            }
        }


class TaskSolution(BaseModel):
    """
    The prompt/agentic orchestration that solves the client's problem.
    """
    prompt: str = Field(..., description="The LLM prompt that solves the task")  # REQUIRED: The actual solution we're testing
    orchestration: Optional[str] = Field(None, description="Additional orchestration logic if needed")  # OPTIONAL: For multi-step/agent workflows
    model_requirements: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Model-specific requirements")  # OPTIONAL: Constraints for test execution


class Scenario(BaseModel):
    """
    An individual IFTTT test scenario with THIS (input) and THAT (expected output).
    """
    full_string: str = Field(..., description="Original natural language scenario description")  # REQUIRED: Preserves original for traceability
    this: str = Field(..., description="Natural language description of the input/scenario")  # REQUIRED: LLM-extracted input condition
    that: str = Field(..., description="Natural language description of the expected output/behavior")  # REQUIRED: LLM-extracted expected result
    grouping: Optional[str] = Field(None, description="Scenario group this belongs to")  # OPTIONAL: Helps organize test report
    
    @field_validator('full_string')
    def validate_contains_separator(cls, v):
        # Basic validation that scenario contains some form of separator
        separators = ['->', '=>', 'then', 'should', 'expect']  # Common IFTTT separators
        if not any(sep in v.lower() for sep in separators):
            # Just a warning, not an error
            pass  # LLM might use other patterns
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "full_string": "CRM: {{large_crm.csv}}. Find me the best advisor to hire for an agricultural PE inv. opportunity. -> Douglas Caulfield + reasoning",
                "this": "CRM data with large CSV file. User asks for best advisor for agricultural PE investment opportunity",
                "that": "System returns Douglas Caulfield with detailed reasoning about agricultural expertise",
                "grouping": "golden_path"
            }
        }


class ScenarioGroup(BaseModel):
    """
    A grouping of related scenarios for organization and readability.
    """
    name: str = Field(..., description="Group name (e.g., 'edge_cases', 'negative_cases')")  # REQUIRED: Categorizes for test strategy
    description: Optional[str] = Field(None, description="What this group tests")  # OPTIONAL: Human-readable context
    scenarios: List[Scenario] = Field(default_factory=list, description="Scenarios in this group")  # REQUIRED: Container for related tests


class TestCase(BaseModel):
    """
    A concrete test case with literal input and expected output.
    """
    scenario_id: Optional[str] = Field(None, description="ID of the scenario this test case implements")  # OPTIONAL: Links to source scenario
    input: Dict[str, Any] = Field(..., description="The actual input data for the test")  # REQUIRED: Concrete test input
    output: Dict[str, Any] = Field(..., description="The expected output data")  # REQUIRED: Expected result to compare
    category: Literal["golden_path", "edge_case", "negative_case", "ambiguous_case"] = Field(
        ..., description="Test case category"
    )  # REQUIRED: Drives test distribution strategy
    realism_score: float = Field(0.0, ge=0.0, le=10.0, description="How realistic the test data is (0-10)")  # REQUIRED: Quality metric for validation
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional test metadata")  # OPTIONAL: Extensibility hook
    
    class Config:
        json_schema_extra = {
            "example": {
                "scenario_id": "scenario_001",
                "input": {
                    "csv_data": "name,expertise,years_exp\nDouglas Caulfield,Agriculture,15\nJane Smith,Tech,10",
                    "query": "best advisor for agricultural PE investment"
                },
                "output": {
                    "advisor": "Douglas Caulfield",
                    "reasoning": "15 years of agricultural expertise directly matches the PE investment focus"
                },
                "category": "golden_path",
                "realism_score": 9.5
            }
        }


class SeedExample(BaseModel):
    """
    A real example provided by the client used as a template for generation.
    """
    input_data: Any = Field(..., description="Example input from the client")  # REQUIRED: Anchors synthetic generation
    output_data: Any = Field(..., description="Example output from the client")  # REQUIRED: Shows expected format/style
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Context about the example")  # OPTIONAL: Source tracking
    created_at: datetime = Field(default_factory=datetime.now)  # AUTO: Timestamp for versioning


class GenerationConfig(BaseModel):
    """
    Configuration for the test case generation process.
    """
    max_cases_per_scenario: int = Field(5, ge=1, description="Maximum test cases to generate per scenario")  # TUNABLE: Balance coverage vs cost
    diversity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum diversity between test cases")  # TUNABLE: Prevent duplicate tests
    target_realism: float = Field(8.0, ge=0.0, le=10.0, description="Target realism score")  # TUNABLE: Quality bar for generation
    
    # Category distribution
    golden_path_percentage: float = Field(0.4, ge=0.0, le=1.0, description="Percentage of golden path cases")  # TUNABLE: Most important cases
    edge_case_percentage: float = Field(0.3, ge=0.0, le=1.0, description="Percentage of edge cases")  # TUNABLE: Boundary testing
    negative_case_percentage: float = Field(0.2, ge=0.0, le=1.0, description="Percentage of negative cases")  # TUNABLE: Error handling
    ambiguous_case_percentage: float = Field(0.1, ge=0.0, le=1.0, description="Percentage of ambiguous cases")  # TUNABLE: Unclear inputs
    
    # LLM settings
    scenario_gen_model: str = Field("gpt-4", description="Model for scenario generation")  # CONFIGURABLE: LLM choice
    scenario_gen_temperature: float = Field(0.7, ge=0.0, le=2.0, description="Temperature for scenario generation")  # TUNABLE: Creativity vs consistency
    test_gen_model: str = Field("gpt-4", description="Model for test case generation")  # CONFIGURABLE: Can differ from scenario model
    test_gen_temperature: float = Field(0.9, ge=0.0, le=2.0, description="Temperature for test generation")  # TUNABLE: Higher for diversity
    
    # Processing settings
    batch_size: int = Field(10, ge=1, description="Batch size for parallel processing")  # TUNABLE: Speed vs API limits
    max_retries: int = Field(3, ge=0, description="Max retries for failed generations")  # TUNABLE: Reliability vs cost
    enable_human_validation: bool = Field(False, description="Whether to enable human-in-the-loop validation")  # FEATURE FLAG: Quality control
    
    @field_validator('golden_path_percentage')
    def validate_percentages(cls, v, info):
        # Note: This is called for golden_path_percentage, but we need all values
        # In practice, you'd want to validate after all fields are set
        return v  # TODO: Validate sum = 1.0


class MiniTestCase(BaseModel):
    """
    A mini test case for graders or components, automatically generated.
    Tests deeper aspects of the system's behavior.
    """
    parent_test_id: str = Field(..., description="ID of the parent test case")  # REQUIRED: Links to main test
    condition: str = Field(..., description="What this mini test is checking")  # REQUIRED: Specific check
    expected_behavior: str = Field(..., description="Expected behavior if condition is met")  # REQUIRED: Pass criteria
    category: Literal["inference_check", "reasoning_check", "format_check", "consistency_check"]  # REQUIRED: Type of validation
    
    class Config:
        json_schema_extra = {
            "example": {
                "parent_test_id": "test_001",
                "condition": "If input infers 8 items but output mentions different number",
                "expected_behavior": "Output should explain why the count differs or fail validation",
                "category": "consistency_check"
            }
        }


class TestGenerationResult(BaseModel):
    """
    Result of a test generation run.
    """
    task_problem: TaskProblem  # REQUIRED: Input that drove generation
    task_solution: TaskSolution  # REQUIRED: What we're testing
    scenario_groups: List[ScenarioGroup]  # REQUIRED: Organized test scenarios
    test_cases: List[TestCase]  # REQUIRED: Concrete executable tests
    generation_config: GenerationConfig  # REQUIRED: How it was generated
    generation_metadata: Dict[str, Any] = Field(default_factory=dict)  # OPTIONAL: Stats, timings, costs
    created_at: datetime = Field(default_factory=datetime.now)  # AUTO: Generation timestamp
    
    @property
    def total_scenarios(self) -> int:
        return sum(len(group.scenarios) for group in self.scenario_groups)  # COMPUTED: For metrics
    
    @property
    def total_test_cases(self) -> int:
        return len(self.test_cases)  # COMPUTED: For metrics
    
    @property
    def coverage_metric(self) -> float:
        """Calculate coverage as ratio of test cases to scenarios."""
        if self.total_scenarios == 0:
            return 0.0
        return min(self.total_test_cases / self.total_scenarios, 1.0)  # COMPUTED: Quality metric


if __name__ == "__main__":
    """Test the models with example instantiations."""
    print("Testing Test Case Generator Models...\n")
    
    # 1. Create a TaskProblem
    print("1. Creating TaskProblem:")
    task_problem = TaskProblem(
        description="I have a CSV of contractors with their expertise and experience. I need to find the best match for specific project requirements.",
        domain="CRM/Recruitment",
        constraints=["Must provide reasoning", "Should handle missing data gracefully"]
    )
    print(f"   ✓ TaskProblem created: {task_problem.domain}")
    print(f"     Description: {task_problem.description[:50]}...")
    
    # 2. Create a TaskSolution
    print("\n2. Creating TaskSolution:")
    task_solution = TaskSolution(
        prompt="""Given a CSV of contractors, analyze their expertise and experience 
        to find the best match for the project requirements. Provide detailed reasoning.""",
        model_requirements={"min_context_length": 4000, "requires_function_calling": False}
    )
    print(f"   ✓ TaskSolution created")
    print(f"     Prompt preview: {task_solution.prompt[:60]}...")
    
    # 3. Create some Scenarios
    print("\n3. Creating Scenarios:")
    scenario1 = Scenario(
        full_string="CSV with 10 contractors, asking for best React developer -> Returns John Doe with 5 years React experience",
        this="CSV data with 10 contractors including various skills, user asks for best React developer",
        that="System returns John Doe who has 5 years of React experience with reasoning",
        grouping="golden_path"
    )
    
    scenario2 = Scenario(
        full_string="Empty CSV file -> Returns error message about no data available",
        this="Empty CSV file provided",
        that="System returns helpful error message explaining no contractor data is available",
        grouping="edge_cases"
    )
    print(f"   ✓ Created {2} scenarios")
    print(f"     Scenario 1: {scenario1.this[:50]}...")
    print(f"     Scenario 2: {scenario2.this[:50]}...")
    
    # 4. Create a ScenarioGroup
    print("\n4. Creating ScenarioGroup:")
    scenario_group = ScenarioGroup(
        name="golden_path",
        description="Standard successful cases with valid data",
        scenarios=[scenario1]
    )
    edge_group = ScenarioGroup(
        name="edge_cases",
        description="Boundary conditions and unusual inputs",
        scenarios=[scenario2]
    )
    print(f"   ✓ Created {2} scenario groups")
    
    # 5. Create a TestCase
    print("\n5. Creating TestCase:")
    test_case = TestCase(
        scenario_id="scenario_001",
        input={
            "csv_data": "name,expertise,years_exp\nJohn Doe,React,5\nJane Smith,Python,8",
            "query": "Who is the best React developer?"
        },
        output={
            "selected_contractor": "John Doe",
            "reasoning": "John Doe has 5 years of React experience, making them the best match for React development",
            "experience_years": 5
        },
        category="golden_path",
        realism_score=9.0,
        metadata={"test_priority": "high"}
    )
    print(f"   ✓ TestCase created")
    print(f"     Category: {test_case.category}")
    print(f"     Realism score: {test_case.realism_score}/10")
    
    # 6. Create a SeedExample
    print("\n6. Creating SeedExample:")
    seed_example = SeedExample(
        input_data={
            "csv": "name,skills,years\nAlex,Python;ML,10",
            "request": "I need an ML expert"
        },
        output_data={
            "recommendation": "Alex",
            "reason": "10 years experience with ML"
        },
        metadata={"source": "client_example", "date": "2024-01-15"}
    )
    print(f"   ✓ SeedExample created")
    print(f"     Created at: {seed_example.created_at}")
    
    # 7. Create GenerationConfig
    print("\n7. Creating GenerationConfig:")
    config = GenerationConfig(
        max_cases_per_scenario=3,
        diversity_threshold=0.8,
        target_realism=8.5,
        golden_path_percentage=0.4,
        edge_case_percentage=0.3,
        negative_case_percentage=0.2,
        ambiguous_case_percentage=0.1,
        scenario_gen_model="gpt-4",
        scenario_gen_temperature=0.7,
        batch_size=5,
        enable_human_validation=True
    )
    print(f"   ✓ GenerationConfig created")
    print(f"     Model: {config.scenario_gen_model}")
    print(f"     Batch size: {config.batch_size}")
    print(f"     Distribution: Golden={config.golden_path_percentage}, Edge={config.edge_case_percentage}")
    
    # 8. Create a MiniTestCase
    print("\n8. Creating MiniTestCase:")
    mini_test = MiniTestCase(
        parent_test_id="test_001",
        condition="If CSV contains 5 contractors but output mentions 3",
        expected_behavior="Output should explain why only 3 contractors were considered (e.g., filtering criteria)",
        category="consistency_check"
    )
    print(f"   ✓ MiniTestCase created")
    print(f"     Category: {mini_test.category}")
    
    # 9. Create TestGenerationResult
    print("\n9. Creating TestGenerationResult:")
    result = TestGenerationResult(
        task_problem=task_problem,
        task_solution=task_solution,
        scenario_groups=[scenario_group, edge_group],
        test_cases=[test_case],
        generation_config=config,
        generation_metadata={"duration_seconds": 45, "llm_calls": 12}
    )
    print(f"   ✓ TestGenerationResult created")
    print(f"     Total scenarios: {result.total_scenarios}")
    print(f"     Total test cases: {result.total_test_cases}")
    print(f"     Coverage metric: {result.coverage_metric:.2%}")
    
    # 10. Test JSON serialization
    print("\n10. Testing JSON serialization:")
    try:
        json_str = test_case.model_dump_json(indent=2)
        print(f"   ✓ TestCase serializes to JSON successfully")
        print(f"     JSON preview: {json_str[:100]}...")
        
        # Test deserialization
        test_case_copy = TestCase.model_validate_json(json_str)
        print(f"   ✓ TestCase deserializes from JSON successfully")
        
    except Exception as e:
        print(f"   ✗ JSON serialization failed: {e}")
    
    # 11. Test validation
    print("\n11. Testing validation:")
    try:
        # This should fail - realism score out of range
        bad_test = TestCase(
            input={"data": "test"},
            output={"result": "test"},
            category="golden_path",
            realism_score=15.0  # Out of range!
        )
    except Exception as e:
        print(f"   ✓ Validation correctly caught error: {type(e).__name__}")
        print(f"     Error: {str(e)[:100]}...")
    
    print("\n✅ All model tests completed successfully!")