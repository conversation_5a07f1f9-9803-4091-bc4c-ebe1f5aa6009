# LLM Dev Constraints

### 1 Why phrases help harness stochastic coders
LLMs search a vast latent space; short, generic prompts leave entropy high. Named micro-disciplines (NSP, DBC, MISRA, “Power of 10”, etc.) act as *compression labels* that load large clusters of conventions in a single token span, slashing variance. Negative-Space Programming’s focus on “what must never happen” is a prime example—people and models both map that label to *assert-heavy, fail-fast* code.

+ Get that big coding corpus. Or similar. Filter based on some rubrics, cluster, classify, get examples → build the duplo empire.
	+ See get thread with that famous coder.






**EXEC TLDR**
Treat the LLM like an *unsafe junior dev* who must obey hard-edge “guard-phrase” contracts. Give it a prompt scaffold that:
- **Declares the discipline** you want (“Use Negative-Space Programming; every function must include ≥ 2 `assert` invariants”).
- **Pins the surface area** (“Return only valid Python in one fenced block, no prose”).
- **Injects tests/contracts it must satisfy** (doctest snippet or Hypothesis property).Then run an automated gate (mypy + pytest) and loop until it passes. Complement NSP with other constraint idioms—Design-by-Contract, “illegal-states-unrepresentable”, property-based fuzz checks, etc.—to tighten the probability mass around correct code.
---

## 1 Why phrases help harness stochastic codersLLMs search a vast latent space; short, generic prompts leave entropy high. Named micro-disciplines (NSP, DBC, MISRA, “Power of 10”, etc.) act as *compression labels* that load large clusters of conventions in a single token span, slashing variance. Negative-Space Programming’s focus on “what must never happen” is a prime example—people and models both map that label to *assert-heavy, fail-fast* code. ([double-trouble.dev](https://double-trouble.dev/post/negativ-space-programming/?utm_source=chatgpt.com), [loskutoff.com](https://www.loskutoff.com/blog/negative-space-is-misunderstood/?utm_source=chatgpt.com))
---

## 2 Prompt scaffold that locks the model down
```
SYSTEM:You are a senior Python reviewer.  Follow these rules:1. Apply **Negative-Space Programming**: ≥2 runtime assertions per function, guard impossible states early.2. Follow **Design by Contract (PEP 316)**: pre-/post-conditions in docstring “pre:” / “post:” sections.3. All functions typed `--strict`.4. Output **one** ```python block only—no narrative.USER:Write a function `safe_divide(a: float, b: float) -> float`• Pre: b != 0• Post: result * b == a• Include a `doctest` verifying the contract.
```
*Gate stage:* after LLM responds, run `python -m pytest -q` on the doctest + `mypy --strict`.
If either fails, feed the trace back in a “critic” message and ask for a corrected patch.
(You can automate this with an agent loop; keep temperature low to reduce lateral drift.)
---

## 3 Other constraint labels to demand
| Label / Shorthand | What it enforces | How to phrase the demand |
| ----------------- | ---------------- | ------------------------ |
| DBC / “Eiffel-style contracts” | explicit pre, post, invariant sections | “Add PEP 316-style contracts for every public func” (peps.python.org) |
| Illegal states unrepresentable | types that rule out invalid combos | “Redesign so the type system forbids zero-denominator; b: NonZeroFloat” |
| Property-based spec | stochastic test oracle | “Provide a Hypothesis @given test capturing the invariant” |
| Power of 10 (NASA) | two assertions per function, no dynamic memory in ISR, etc. | “Adhere to NASA Power-of-10 rules 3-6 in this module” (double-trouble.dev) |
| MISRA-C / “safe subset” | forbid foot-gun language features | “Use Python subset: no global state, no eval, no reflection” |
| Functional core / imperative shell | pure functions inside, side effects at edge | “Separate pure core from I/O; core must be referentially transparent” |
| Total FP / no partial functions | every function defined for all inputs | “Make it total: function returns Result not exceptions” |
| Given/When/Then tests first | TDD scaffolding | “Emit Gherkin spec + passing PyTest skeleton before code” |
| Literate patch | small diff only | “Return a unified diff with no extra context lines” |
Use them combinatorially: e.g. *“Negative-Space + DBC + Hypothesis property check”* gives you runtime fences, static intent, and random exploration coverage.
---

## 4 Implementation blueprint for an agentic coder
```
graph TDA[Prompt with guard-phrases] --> B(LLM code draft)B --> C{Static gate<br/>mypy + ruff}C --fail--> D[Critic msg<br/>“mypy: error: ...”]C --pass--> E{Dynamic gate<br/>pytest + Hypothesis}E --fail--> DE --pass--> F[Accept code]
```
*Tips*
- Keep temperature ≤ 0.3 during *repair* turns; randomness useful only on first pass.
- Cache passing patches; if model regresses, back-jump.
- Log which guard phrase caught each failure—this becomes training data for future prompt tuning.
---

## 5 Crafting your phrase library
1. **Curate** 5-10 labels that map cleanly to coding disciplines (table above).
2. **Define** each in a markdown README with mini-examples; feed that doc as a system context once so the model can ground the label.
3. **Version** the library—e.g., “NSP-v1.1” adds *assert response time < X ms*—and reference by tag to keep prompts short.
4. **Measure**: record pass-rate deltas when you toggle each phrase to see which constraints yield the biggest entropy reduction.
With this stack, you convert fuzzy natural-language requests into a *formal expectation contract* the LLM must satisfy, shrinking the probability distribution toward safe, deterministic code.



---






[ChatGPT](https://chatgpt.com/c/685a05fa-262c-8003-865a-ef6819286f06)

Your new **litellm_bench_v1.py** lives in the canvas (see right-hand pane).
It shows the whole stack in action:
- **Functional-core / imperative-shell split** – the only `try/except` is in a tiny adapter that converts the network call into a `Result`; every other function is pure.
- **Negative-Space Programming** – ≥ 2 assertions per func (`_assert_german`, `_duration`, config key guard).
- **No mocks, no dynamic tricks, no unchecked exceptions in the core**.
- **Strict typing** (`Result[T, E]` with Pydantic validators) so illegal states are unrepresentable.
- Fits Black + Ruff + `mypy --strict` out-of-the-box.
---

### 📄 Prompt templates for your agentic coder
```
SYSTEMRules = {FCIS-v1, NSP-2-assert, No-Exc, No-Mock, Black, Ruff}.Return ONE ```python block only, nothing else.USERWrite a pure function  add_vat(net: Decimal, rate: Decimal) -> Decimal• Pre: 0 ≤ rate ≤ 1• Post: result = net × (1 + rate)• Include 2 runtime assertions.
```
```
SYSTEMSame rules.  Architecture tag: CQRS-v1.USERGenerate boilerplate for the “RegisterUser” use-case:• Command DTO  (email:str, name:str)• Handler  __call__(cmd) -> Result[UserId,str]Pure core only, no DB code (that’s shell).
```
```
SYSTEMShell context.  Conversion to Result is allowed; no other exceptions.USERWrite an HTTP adapter that POSTs RegisterUser to /api/users• Catch network errors once, map to Result[None,str]• Timeout ≤ 3 s
```
Chain these in your agent loop:
1. Prompt with template 1/2 to generate pure core code → run `mypy --strict` & property tests.
2. Prompt with template 3 for side-effect layer → run Ruff/Black + integration test.
3. Reject & retry automatically on any gate failure.
Stick to these guard phrases (NSP-2-assert, FCIS-v1, No-Exc, No-Mock) and every LLM-generated feature will land looking almost identical to the last—tight, predictable, business-logic-only.