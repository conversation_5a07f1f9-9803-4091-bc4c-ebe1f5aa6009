# Goal: Find time w X
So this works:
```
https://calendar.google.com/calendar/r/eventedit?
action=TEMPLATE
&text=Sync+Up
&dates=20250702T090000Z/20250702T100000Z
&ctz=America%2FNew_York
&add=will.hayes%40invisible.email
```
[Link](https://calendar.google.com/calendar/r/eventedit?%0Aaction=TEMPLATE%0A&text=Sync+Up%0A&dates=20250702T090000Z/20250702T100000Z%0A&ctz=America%252FNew_York%0A&add=will.hayes%2540invisible.email)
- have got fill it.
- paste it
- Click Find a time.
But it can't get all the free slots.

Typing it into the LHS box on main calendar.google.com site could be great for this.

OR.. could just use the Gsuite API.







# CHATTY
**You:**

The 'find a time with someone' featyres in google calendar gsuite, we use it internally to book all meetings and it's great. Is it url parameter able to open it with a certain email address? I just type their name in on the LHS so not crazy complex. 

* * *

**ChatGPT:**

**TLDR:** There isn’t a public URL‐parameter to deep-link into the left-hand “Meet with…” (Find a time) drawer pre-populated with someone’s address. That mini-panel is purely client-side UI.

* * *

### Why not

* The “Meet with…” picker on the left is rendered by client-side JavaScript and has no distinct route or query-string hooks.
    
* Google’s documented URL-link scheme only lets you open the **event editor** with fields pre-filled (text, dates, location, etc.), not the free-busy drawer. [stackoverflow.com](https://stackoverflow.com/questions/10488831/link-to-add-to-google-calendar)
    

### Workaround: prefill **guests** in the event-editor

You can open the **new-event** form with guests already added, then manually click into “Find a time” if you want free-busy. For example:

```
https://calendar.google.com/calendar/r/eventedit?
 action=TEMPLATE
 &text=Sync+Up
 &dates=20250702T090000Z/20250702T100000Z
 &ctz=America%2FNew_York
 &add=<EMAIL>
```

* `action=TEMPLATE` → opens the event-create UI
    
* `text`, `dates`, `ctz` → title, start/end and timezone [stackoverflow.com](https://stackoverflow.com/questions/10488831/link-to-add-to-google-calendar)
    
* `add=…` → (undocumented) fills the Guests field
    

Once the editor is up, switch to the “Find a time” tab to see both calendars side-by-side.

* * *

> **Bottom line:** no direct URL param for the left “Meet with” drawer. Your best bet is to prefill the editor via `action=TEMPLATE` + `add=` and then hit “Find a time” in the UI.

* * *

**You:**

OK fill one of these for me 

https://calendar.google.com/calendar/r/eventedit?
 action=TEMPLATE
 &text=Sync+Up
 &dates=20250702T090000Z/20250702T100000Z
 &ctz=America%2FNew_York
 &add=<EMAIL>

<NAME_EMAIL>.

* * *

**ChatGPT:**

**TLDR:** Here’s the pre‐filled event‐editor link with Will Hayes as the guest:

```
https://calendar.google.com/calendar/r/eventedit?
action=TEMPLATE
&text=Sync+Up
&dates=20250702T090000Z/20250702T100000Z
&ctz=America%2FNew_York
&add=will.hayes%40invisible.email
```

**As a single URL (no line breaks):**  
https://calendar.google.com/calendar/r/eventedit?action=TEMPLATE&text=Sync+Up&dates=20250702T090000Z/20250702T100000Z&ctz=America%2FNew_York&add=will.hayes%40invisible.email