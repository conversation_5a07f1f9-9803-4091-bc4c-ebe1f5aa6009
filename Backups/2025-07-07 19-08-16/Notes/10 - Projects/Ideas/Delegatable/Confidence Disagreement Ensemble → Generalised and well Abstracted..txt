# Confidence Disagreement Ensemble → Generalised and well Abstracted.



## Agent task is too hard
Just like how we detect (or want to detect) looping or low-confidence plans that are not seeking to deal with their ambiguity, we also just want to be able to tell if an agent is doing a task that is likely just too hard and needs someone to look at it and work out what's the larger problem. Call it escalation. 