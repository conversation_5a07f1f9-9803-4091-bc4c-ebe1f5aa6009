# Claude Found these

# DKE Domain Knowledge Extractor - Complete Content Extraction
## 1. Google Drive Document: "The Requirements Factory: Automating Task Discovery and Implementation at Scale"*Created: June 6, 2025**Last Modified: June 6, 2025**Owner: <PERSON> <EMAIL>*
### What We're BuildingDomain Knowledge Extractor isn't just another automation tool - it's a methodology that answers the question every C-suite executive should be asking: "Which tasks that I'm paying people to do can be automated with today's technology, and how?"
Instead of waiting for clients to come to us with their automation ideas (which may or may not be feasible), we flip the script. We go in and systematically discover which tasks *should* be automated, break them down into subtasks, and provide a roadmap for implementation.

### The Problem We SolveThe automation industry has a dirty secret: the feedback cycle kills most projects before they deliver value.
### Traditional Automation Timeline:
### Day 1: Initial task description from client
### Day 3: Client responds to clarification questions
### Day 7: First prototype delivered
### Day 11: Client provides feedback (4 days delay)
### Day 18: Second iteration
### Day 25: More feedback (another week lost)
### ...
### Multiply by 100 tasks = Project deathScale this across 100 tasks in an enterprise, and you're looking at:
- 6+ months of discovery
- Incomplete data that misses critical edge cases
- Exhausted stakeholders who've lost interest
- Zero actual automation delivered
The core issue: people don't know how they do their jobs until they're doing them. Ask someone to describe their workflow, and you'll get maybe 60% accuracy. The remaining 40% - the edge cases, exceptions, and implicit knowledge - that's where automation fails.

### Our Solution: Generate, Don't ExtractWe've learned from the foundation model playbook. OpenAI, Meta, and others don't collect millions of examples - they collect thousands and synthetically extend them. We do the same for task automation.
Domain Knowledge Extractor works in two phases:
**Phase 1: Discovery**
- Deploy lightweight observation tools to capture how work actually happens
- Interview key personnel while they're performing tasks
- Generate comprehensive task maps showing:
  	- Core workflows
	- Decision points
	- Edge cases and exceptions
	- Data dependencies
	- Human judgment requirements
**Phase 2: Validation Through Documents**Instead of asking busy employees to spend 100 hours reviewing test cases (for work that might eliminate their jobs), we:
- Create simple, one-page requirement documents
- Use "if this, then that" declarative logic
- Present scenarios in familiar document format
- Get rapid feedback through document review, not testing
This approach compresses months of discovery into weeks, with higher quality output.

### The Bigger Vision: Requirements FactoryDomain Knowledge Extractor is the entry point to something larger. Over the past year, we've built complementary systems that now form a complete automation pipeline:
### graph TD
### A[Domain Knowledge Extractor] -->|Task Requirements| B[Prompt Swarm]
### B -->|Generated Implementations| C[Eval Constellations]
### C -->|Performance Metrics| D[Reward Learning]
### D -->|Optimized Models| E[Production Automation]
### E -->|Feedback| A**Components Already Built:**
- **Automated Prompt Engineer**: Generates optimal prompts for discovered tasks
- **Prompt Swarm**: Creates diverse implementation approaches
- **Eval Constellations**: Comprehensive testing without human bottlenecks
- **Reward Learning Integration**: Continuous improvement based on real-world performance
Why This Works NowThe basic feedback loop in automation is simple:

### Human describes task → System attempts automation → Human provides feedback → RepeatThis loop fails because:
- Humans can't fully articulate their knowledge
- Feedback requires too much time/effort
- Edge cases emerge only in production
- Scale compounds all these problems
Our approach succeeds because:
- Generate comprehensive task understanding upfront
- Validate through efficient document review
- Use AI to simulate edge cases before production
- Create self-improving systems through reward learning
The Business CaseFor clients:
- **Discovery as a product**: Valuable reconnaissance even without automation
- **Clear ROI projections**: Know what can be automated before investing
- **Reduced risk**: Test automation feasibility before committing resources
For Invisible:
- **Premium positioning**: We find the opportunities, not just execute requests
- **Competitive moat**: Rich task data that improves our automation capabilities
- **Scalable growth**: Each client enriches our task understanding across industries
Next StepsDeploy Domain Knowledge Extractor with 2-3 pilot clients to:
- Validate the discovery methodology
- Build initial task corpus
- Demonstrate value of comprehensive task mapping
- Create case studies for broader rollout
The future of work isn't about replacing humans - it's about understanding what humans do so well that we can augment them effectively. Domain Knowledge Extractor is how we build that understanding at scale.
---

## 2. Email Thread with Jason Mather (Apollo Global Management)*Date: June 30 - July 1, 2025*
### Email from Alex Foster to Jason Mather - June 30, 2025Worth noting that this is just the simplest version of the tech from several weeks back so it could work in Glean. The full feedback loop agent is WIP.
One thing for us to touch base on soon: I've been looking for candidate 'big wins' and the below argument has been the biggest I've been sitting on for the last couple months.
There's a really interesting question combining automated prompt engineering with another Invisible R&D project: the domain knowledge extractor that extracts workflows by watching people work (essentially agentic process mining but from direct observation vs logs):
- Hypothesis: A core driver of PE buy decisions next year will be 'how much we could drive efficiency via LLM orchestration automation'. 2026 will be the year enterprise agentic automation takes off in earnest. Integration will remain challenging and Private Equity will have a unique position to be able to force through large restructurings.
- Fully automatically designing agentic systems to do entire people's jobs is likely still very hard to get working well enough for production >95% accuracy.
- Let's say we can get it to automate many workflows to 70% reliably.
- 70% accuracy might not be production-ready, but it does tell us with quite some confidence, which tasks/workflows *can* be automated, as we have a first pass. If we can get to '70%' then we can have surprisingly high confidence that it's doable.
- Doing this automatically and pairing it with systematic task discovery opens up what feels like it could be a very powerful advantage. A viable answer to the question "how much of this company's operations could be automated?".
How might we go about gathering some feedback around this argument? Any initial thoughts?
Best,Alex

### Response from Jason Mather - July 1, 2025To be clear, this is not around watching folks in our industry, but in other industries as we look to figure out how much efficiency we can add in different companies? if it's outside of our company, it's not clear how we'd get access to observe people / processes?
---

## 3. Mediar Email Correspondence*May - June 2025*
### Email from Alex Foster - May 28, 2025We'll also later professionally be trying to complete our domain knowledge extractor which will be watching professionals do their job then fully extracting all the tasks they do and how they did them, complete with rich qualitative insight and time stamps for later further interrogation.
### Email from Alex Foster - May 29, 2025Our main tech focus is not on extraction but on using scarce (but rich) examples of paid consultants doing the job we need automated and using those to generate RL milestones for agent teams learning in simulation.
I.e., our Agents are mostly generating their own feedback signal using reasoning, but they get stuck.
However, initially, the milestone we want to hit is just something that watches our screens and understands it well enough to give us feedback / redirect us + send tasks to the task queue (human / AI execution)

### From Louis Beaumont (Mediar) - June 4, 2025From the call, it's clear your goal is:
> 
An agent that observes high-signal consultant workflows, abstracts them into structured representations, and routes those insights to an LLM for feedback loops and task queueing.
That aligns extremely well with what Mediar does today, with layered abstraction (OS logs, screenshots, activities, workflows), developer-level visibility, and a privacy-first local design. Your LLM setup (two-tier system with QA agents and a central reasoner) is one of the best integration use cases we've seen.
---

## 4. Additional Context from Email Discussions
### Vassili Kusmartsev questions about workflow analysis (June 24, 2025):
1. Are there any analysis scripts you could share for the raw JSON or to access any of the processed steps that the GUI generates? Specifically in regards to the workflow analysis tabs.
2. On the workflow tab it seems to have processed some workflows nicely, but neither the AI assistant nor the "automate" UI buttons seem to work. Is this functionality implemented currently?
3. Is it possible to query the processed workflows via our own LLM queries? If so, how would one best access that processed data?
4. If I wanted to record some novel workflows separate from the previous workflows, is there a way to analyse those separately? It seems like the workflow tab processes all captured events, and so I'd need to do a "delete all" before recording fresh?

### Vassili's prioritization (June 26, 2025):I think the prioritisation order for us would be:
1. Making the workflows LLM queryable, i.e. your AI assistant I think.
2. Making the workflows downloadable, or at least some foreign key linking each event in the json/steps to a given workflow, so that the events in a workflow can be identified and analysed.
3. A checkmark for selecting given timeframes and being able to delete/select specific sections