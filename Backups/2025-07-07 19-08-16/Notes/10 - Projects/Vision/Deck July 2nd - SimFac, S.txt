# Deck July 2nd - SimFac, \S
Simulation /Requirements Factory / Guild
Soliciting Intuitions / Reactions / Wisdom / Domain Expertise:
* This 
Introduce pie chart of we think it's like this but actually it's like this graphic.
Introduce A → K. We think of tasks as A→B but even if we break a task down into the smallest reasonable sub-tasks, most of those _still_ have intermediary steps. If this seems similar to the Agentic Trajectory work we did for Google etc, a lot of the learnings from that went into this idea. Which can be part of the pitch to clients.
Introduce the Chasm. Az<PERSON><PERSON> quote + 30-50% of tasks could likely be automated with some  time and effort. Problem: They aren't doing it. Timeline: (find simple reason to need to do it by X, when 70% will be the new goal.), show how easily that time will be gone by.
Thousands of tasks.
Parellisation, NOT one at a time.
Guild → well financially incentivised best people who have *explicit verbal understanding* and who can communicate it.


---
Okay, so for the 70% is not enough, is enough to greenlight the point, I feel like the really critical insight there is that it's really hard to fully automate, like, problem engineering. It's really challenging. However, getting to 70% is very doable for a generalized thing. And if we can do that, there's this incredibly valuable thing that we unlock, which is that a lot of these really important decisions, it's not just the doing of it that is valuable, it is the deciding to do it that is extremely valuable. For the second, we watch, we don't ask. I feel like really the core thing here is that previously process mining was all about looking at logs, and it just didn't work very well. The point here is that we are able to just properly mine an extremely rich source of information that was just totally impossible previously. And we'd want to explain the sort of Q&A system we have, where you are building a narrative about what appears to be what's going on, and then challenging that narrative by asking lots of questions using a reasoning model, and then delegating those questions to smaller models in parallel to go and ask at different points along that timeline, ask questions, and come back with their answers to the reasoning model, which then updates its narrative with those questions, and then comes up with more questions. So, we could imagine, like, a graphic that is like a timeline. We've already introduced that timeline in a previous slide, and we have sort of a box that is the reasoning model, and it's looking at a portion of that timeline, that's like two-thirds of it, to show, like, this is where I've identified as a workflow. We can have that as an earlier slide, just identifying, like, workflow, and then identify the narrative, like the user is attempting to achieve X, and they've done this by navigating to this website, finding the answer, this data and that data, and then pasting them into Excel, and then reformatting them into this chart. And in the slide regarding the Q&A system, we want to have some sort of slight error or ambiguity in the slide before, which we then identify, ask a bunch of questions about. The graphic has those questions being asked against different points, different areas of the timeline, and then coming back with answers. Those answers can come up as speech bubbles, and the reasoning model goes, great, I see what's going on here, and it updates its narrative, and comes up with the next series of questions, which we can have off to the right-hand side as a speech bubble from the reasoning model. I think that will look really cool.
Okay, for the auto-generated task maps, I think the thing here is that any given person or role actually, we think of their job as like, you know, this much PowerPoint, this much Excel, this much meetings, but actually it's this complex mesh of tasks, and people don't even start and stop a given task in one go, and so you need to be able to pick up and then piece together those tasks from the complex chaos of their day.
The one page If This Then That is actually three documents, the requirements document which measures both what success looks like, what failure looks like, and then sort of sub-tests that break those requirements down a bit more to make them a bit more complete. The If This Then That Scenario Simulator is a document which basically says for this input this kind of output, except each one of these in the document is just a single natural language line. So, for the slide for this, we want to have one for the requirements and then one for the If This Then That. In both cases, we just show a few examples. Let me insert the top, state what it is those things are doing. We should probably have a link to an example of both, and then just have a few little arrows saying like natural language, easy to give feedback, and the next slide should just be the same again, but assuming it was in Google Docs, we want to see track changes. So, someone has edited it and we've gained information as a result.
RECORDING HERE
Okay, so the next slide we go to prompts, form draft, parallel robot graders, given student access. So here I think we wanted to make a few points we want to make around the requirement. Factory or automatic prompt engineer?
I think we don't want to tread on toes. So automatic prompt engineer is still a bit problematic. So maybe requirement flywheel and we should explicitly here have like Aaron's agent factory as a block in that slide.
So the first part we got the...
Requirement. Reconnaissance. Requirement? Scanner something like that.
Or requirement guild given the entire pitch comes back to this concept and this point that if you're going to pitch it to somebody, a CEO, they need to get some people who actually do it. Their best people to have this software and then have those like ethnographers, whatever you want to call them, sitting next to them.
Okay, the slides. So we have the first one which is just where this fits in that this is the requirement flywheel. And we show that clearly interacting with Aaron. Then the next slide should be like people companies attempting to automate are being highly irrationally frugal when it comes to their willingness to spend tokens automating work. They are seeing it as OPEX operating expenditure rather than as CAPEX.
And the next line should be something like, "However, this is not even CAPEX as what they should really be thinking of is investing in the process." That is going to be how they automate 30% to 70% of their operational work in the next two years, maybe even less, the next twelve months. Just needs to be aggressive and capable of scale. We should open the deck with the Azaza quote around like the 50% more of the market, and a statement that we're going to stand by ourselves, which is we think that something like 30% to 50% of tasks done by white-collar workers today could be automated today.
However, by the time they've done even 2% of that, six months will have gone by and that number will have gone up by 20%. And that's the world we are living in. Speed and scale are valuable today in a way that they were not before.
Enterprises will be starting to see startups able to build entire businesses with only 30 employees eating up various people in different industries. That's what's going to drive the fear, and it's going to make them realize how fast they need to be moving and scaling and changing the way they do things.
So, the next several slides, I think, are quite an important point to drop. We start off with the little bottom right in the bottom right. We're going to have the price for a hundred runs, let's say.
The first one, we have individual prompts, then we have reasoning models, and we have reasoning models on higher to a Popper poem. You can see it getting more expensive, then just added some extra numbers like accuracy, hallucinations, things missed, left out, whatever. Recall the next one we have.
Okay, here's an agentic system. You have this sort of loop. Add one recursion loop on the team. Add a few more lines and say, "This is," and show that it's costing you even more. Give a title that makes sense for this. Said the system, the Requirement Flywheel, that our current requirement will have a high-level diagram of the system as it is. You've got a diagram of this on your desk. Put the price in the bottom right.
It's going to be significantly higher. That is fine. Then, I think one more slide. Just take it one step further. The techniques we are currently deploying increase the chance we get from A to B where A is the starting point of the task and B is the finish point of the task. Input-output significantly. In particular, allowing the agents to realize when they've gone wrong or when they are in a low-confidence state and handle the fact that LMS will answer confidently no matter what.
Maybe this SDE actually goes the one after this. The next one then needs to be the following point, which is...
As previously covered...
This might be an extra slide earlier actually. Thousands of tasks, which is way more tasks than previously described as previously covered. Enterprises need to be attempting to automate hundreds to thousands of tasks as soon as possible, and the most valuable piece of information, given the scale required, is what can be automated.
I think this doesn't go here. But getting from A to B is we want to be left on the right curly line. That doesn't get there. It says you don't get from A to B with your automation. You now need humans to get involved. You can add an arrow for "human" in the loop coming in. You can add several prompt engineers.
Then the next slide had the same thing again, except show it in terms of time. So have the curly line and then just squash it horizontally and have like ten minutes. Then for the human, three human people. Just have a Gantt chart and annotate the steps to show the spin-up time, getting to understand the project, actually doing the work, delivering confusion around requirements, confusion, rewrites, some sort of break. Just total that all up to be like six weeks to make the point. We don't need to make the point about... Yes, I think that's the correct way to show that I've got that image in my mind. The next slide. Curly. Curly. Lost line.
But it actually gets there if... And just at the bottom, keep the timeline. But the timeline should say on that 20 minutes, half an hour, whatever it needs to be and then maybe just show that again versus the other items.
It's going to stack up, but a lot of these slides are duplicated. If we can announce this slide, then just... If you can get from A to B reliably, somewhat reliably, the process of trying to do it faster and cheaper can then still be done largely automatically because you have that initial blueprint of how to get from A to B.
Next slide. We can add more graders and more requirements and sub-requirements, as well as more and better simulated scenarios or test cases.
Those can just be little... I don't know, something that repels, like a magnetic or something better. If you have the pull and the push, the pull can be, maybe we can introduce these earlier in the deck, just a little green dot and a red dot for do and don't, and have one with little arrows pointing outwards and one as a suction pulling inwards.
I think if the arrows... Little arrow things with the color, you can add them on the curvy line where you've seen it. It's been meandering, and you push, you just show those little animation forces, and then ideally, this is going to be animated, beginning to animate it. Just put it in, you rerun it. That line then becomes much straighter. It'll still be a little bit abstract, but I think that is okay.
So someone will ask like what it is. We can add some more detail later, but the concept is there, and the point is made. It's a little bit complicated, but I think that we should get that whole concept across.
----
What Next? We're roughly at Graders.
Graders → Graders can initially be used in production (expensive but fast). But we need someone like Andrew Hicks / John Zhu to spearhead taking A→F trajectory and