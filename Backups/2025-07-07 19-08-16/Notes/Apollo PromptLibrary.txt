# Apollo PromptLibrary
See <PERSON><PERSON> in Main Repo here 

OK maybe this is more important than I think.
1. Prompt Library feels like a non-power move politically whilst it's not that good.
2. If it's our own stack on a sub-domain and isn't a security issue then we have somewhere for experiments which require UI to live that is within their firewall (data allowed). It's the **digital beach head.**
3. **digital beach head & foot ins** → If we had 20 Enterprise beach heads like UI path do, there's a TONNE of opportunities to get that foot in. 
## Idea: white label customer feedback / feature request / roadmap etc tool.


E.g., Frill
![image](Apollo%20PromptLibrary_attachments/DD797BAB-1C12-4801-9D9C-870A2F022554.png)
It has roughly the look I wanted.

1. Prompts Lib
	2. Voting for sorting
	3. Verification badges / rottenTom style double scoring. Karma algorithm for points & gamifying valuable actions (feedback, HITL, Ideation)
	4. 'There isn't an AI for that' (Prompt / AI Request). Form, 'brainstorm with AI' button to extract more domain knowledge.
		5. "Auto AI maker coming soon..." Greyed out, hype, resources, excitement.


Search bar:
1. Add sliders / low-med-high for:
	1. Robustness (aggregate score)
	2. Min Number of reviews (exponentially distributed if l-m-h)
	3. Minimum rating (exclude <n for 5* only, and think  about 4-5 gap.)

![image](Apollo%20PromptLibrary_attachments/D7A7F8C4-29ED-4567-8B5F-7FDC3CC91E39.png)