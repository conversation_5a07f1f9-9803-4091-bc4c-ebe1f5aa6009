# mac_MAX-AF v0.01
WHY
- "could work 70% less", "could consciously choose the *RabbitHole* or *TangentFireHose"*, concentration is a contract made with the subconscious → ritualise intentionality around that contract. ""
- enter: **THE RIVER**

WHAT
* Drop 4-5 prompts from cc here.


![image](mac_MAX-AF%20v0.01_attachments/Selfie%202025-06-28%20at%2008.00.11.png)

## Not quite worth of top priority 
→ will come back to them! If the subconscious decides. Capture it fast, let yourself flow back to the river. **5min timer?**
```
flowchart TD
    A[Start] -→ B{Decision?}
    B -→|Yes| C[Process A]
    B -→|No| D[Process B]
    C -→ E[End]
    D -→ E
```
+ Make 15m Timer. Stop, Step, Back? Breathe. 1QnA. Decide.
	+ Use Camera & llama 3.2 to decide if you've stepped back. OR take 10k photos of you working (notice Mac inputs), over next 3 weeks and train classifier on there vs not. Or just use 'anyFaceDetection' if it exists.
+ Always-on assistant. MVP = tool-use w shortlist of tools.
+ make mermaid work
+ Cerebras
+ Holding chatty music → sick idea where long LLM calls have fast model answer question (and speaking fast) in the mean time.
	+ o3: Which voice model has the best reply latency, if accounting for intonation quality. Likely realtime GPT right? Or Eleven? The tone of the voice and how I respond to it's personality is also important
	+ not so Ridiculous, but could have different trigger words for 3 different intelligences of quick reply, or "x-3"→ using top voice reply model. O3 processes the whole instruction either way separately. If GPT realtime's submission is....

