# Uber Time Estimator
**You:**

Can <PERSON><PERSON>'s Fare Estimator be called programmatically / via browser control without logging in? I want to add it to my Raycast AI etc.  Did you mean: uber cost estimate

Get a Price Estimate in Your City

Uber
https://www.uber.com › global › price-estimate
Use the Uber price estimator to find out how much a ride with Uber is estimated to cost before you request it. Get a cost estimate now with our fare ...
Get a price estimate for your trip | Riders

Uber
https://help.uber.com › en-GB › riders › article › get-a-...
Visit the page below to get a price estimate for a trip anywhere Uber is available:
Uber Fare Finder - Uber Estimate & Cost Calculator

UberFareFinder
https://uberfarefinder.com
UberFareFinder calculates the cost of your Uber ride. Enter your pickup and dropoff locations, and get the fare estimate and surge pricing information.

* * *

**ChatGPT:**

**Exec TLDR:** Yes—you can fetch Uber fare estimates programmatically without a rider “login” by calling Uber’s public Estimates API. You’ll need a developer token (legacy `server_token` or OAuth `Bearer`), but you won’t invoke the rider-login flow in a browser.

* * *

### 1. Official Uber Estimates API

Uber exposes a REST endpoint that returns price ranges for a given start/end geolocation:

```http
GET https://api.uber.com/v1.2/estimates/price
  ?start_latitude={LAT}
  &start_longitude={LNG}
  &end_latitude={LAT}
  &end_longitude={LNG}
```

**Authentication options:**

* **Server Token** (legacy): include header  
    `Authorization: Token <SERVER_TOKEN>`
    
* **OAuth 2.0**: include header  
    `Authorization: Bearer <ACCESS_TOKEN>`
    

No rider-side login UI is involved—just your app’s credentials.  
[stackoverflow.com](https://stackoverflow.com/questions/49786800/uber-price-time-estimate-api-now-returns-401-unauthorized?utm_source=chatgpt.com)[developer.uber.com](https://developer.uber.com/docs/riders/guides/authentication/server-token?utm_source=chatgpt.com)

> **Note:** New server tokens are no longer issued; existing tokens still work, but Uber now requires you to apply for production API access (via a Business Dev contact) to obtain a scoped access token [developer.uber.com](https://developer.uber.com/docs/riders/references/api/v1.2/estimates-price-get?utm_source=chatgpt.com).

* * *

### 2. Example Request (cURL)

```bash
curl -G 'https://api.uber.com/v1.2/estimates/price' \
  -H 'Authorization: Token YOUR_SERVER_TOKEN' \
  -H 'Accept-Language: en_US' \
  -d start_latitude=37.7752315 \
  -d start_longitude=-122.4180750 \
  -d end_latitude=37.7752415 \
  -d end_longitude=-122.5180750
```

_Response:_ JSON listing product IDs, display names, and fare ranges.  
[stackoverflow.com](https://stackoverflow.com/questions/49786800/uber-price-time-estimate-api-now-returns-401-unauthorized?utm_source=chatgpt.com)

* * *

### 3. Browser-Scraping Alternative (Brittle)

If you really want to avoid any API keys, you can automate a headless-browser (e.g. Puppeteer/Selenium) to visit:

```
https://www.uber.com/global/en/price-estimate/
```

populate the pickup/drop-off fields, intercept the behind-the-scenes GraphQL/XHR request, and parse its JSON response.

* **Pros:** zero credentials needed
    
* **Cons:** likely to break when Uber updates their frontend; requires full browser automation
    

* * *

### 4. Integration into Raycast AI

* **Backend plugin:** call the Estimates API directly from your Raycast extension (Node/Go/Python).
    
* **No user login:** your extension uses your app’s server or access token – end users never see a login prompt.
    
* **Error handling:** watch for HTTP 401/403 if your token is expired or access isn’t approved.
    

* * *

**Bottom line:**  
For stability and support, register at developer.uber.com, obtain your token, and hit the Estimates API. If you need zero-credential scraping, use headless-browser + XHR capture—but be prepared for occasional breakages.