# Bruno DKE April - Ideas * Transcript
#### The user is asking me to think through options for <PERSON>'s demo, based on the transcript they provided. Let me analyze what I understand from this lengthy conversation between <PERSON> and <PERSON>. …
Key context from the transcript:
1. <PERSON> is head of R&D at Invisible Technologies
2. They provide SFT/RLHF annotation services to major LLM organizations
3. <PERSON> needs to show meaningful progress to the CEO (<PERSON>) or will be in a "shit position"
4. They have several projects in progress:
        	- Model Breaker - A test case generator
	- Prompt Swarm / Eval Swarm - Tool for creating evals quickly
	- Agent Factory (can't use this name due to conflict with <PERSON>)
	- Agent Foundations
	- AutoCeder - Generates diverse seeds for test case generation
	- LLM Sheets - Python in spreadsheets for rapid iteration
	- Domain Knowledge Extractor
The main vision seems to be around creating an automated system that can:
1. Extract domain knowledge from experts
2. Generate comprehensive test cases and evaluations
3. Create agent teams that can complete complex tasks with continuous feedback
<PERSON> (who seems to be in marketing) is helping <PERSON> figure out what to demo. They worked through an example using insurance claim documents (scope of loss) where humans need to identify missing items to maximize claims.
The demo idea they converged on appears to be showing:
1. Screen recording of domain experts doing their jobs
2. AI extraction of workflows and requirements from these recordings
3. Generation of evaluation criteria (evals) for each requirement
4. Multiple agents attempting the same task with continuous feedback
5. Selection of the best performing agents
Key challenges:
- Need to show vision without having everything fully built
- Can't step on <PERSON>'s toes with "Agent Factory" naming
- Need to make it enterprise-relevant
- CEO is technically savvy (ran Mc<PERSON>insey AI R&D)
- Time pressure (7-8 weeks to deploy something)



## TRtANSCRIPT
[Claude](https://claude.ai/chat/f487ace8-01dd-430d-8f61-7c839eb1d3c8)
Almost. Okay, talk to me. I think you're supposed to talk to me about... Okay, Alex, the purpose of this call is you said that you have to go and show certain level of meaningful progress to your CEO. Otherwise, you are going to be in a shit position.

Yes, all the conversation is very good.  
So how can I get you not to be in a shit position? Tell me some meaningful progress that you can show to human beings to justify your expenditures.

Yeah, good question. So I was hoping for a quick win with the Model Breaker project. I do have some wins there, in that it has happened, but not the full thing that I tried to pull off. It was doing a pretty good job, but the code wasn't quite ready in time.  
But we still used Model Breaker. So, it's really just me feeling like there's a failure because I can still talk about it as a thing that happened, a thing that I made happen. Ownership is invisible. It's a bit strange.  
If one doesn't work for you, people will quickly start assemble. That person did it. I'm like, cool, but why would anybody teach anyone something if credit internal setup is annoying, isn't working?  
Okay, never mind. I can't see the bottom of my screen. I'm just going to drop my video.

Okay, so there's a bunch of different projects. We've got Model Breaker, we've got... I can pull the list in a second. We've got Promp Swarm, we've got the...  
Yeah, it's like five different things that need summarizing. One second. Let me... Yeah, Model Breakers. What the fuck is this? I've never actually used it. You've never used Caldron or you've never seen me?  
I've never actually used it, even though I know it's like, you know, develop a favorite way to talk. Here it is. Okay, if there is a Model Breaker you say the other one is the storm. Let me pull a list. Where is the Notion? It's probably the best place to access that list.

Let me change my sound settings because this is definitely not currently recording. Krisp, can you still hear me?

Yep.  
Okay, cool.  
So what have we got? We have prompt swarm agent factory, although I can't really call it that anymore. Agent foundations, housekeeper, recertified AE, auto-ceder, model breaker. Okay, LL MQALME valves.  
But again, that's not really my thing anymore. I don't need to go into more detail, but it's fine. My role is to get things used by other people.  
So, what have we got? Of agent constraints? My God. I have a folder called "Shit", Alex said in Slack. Love that idea.

Someone is playing a techno remix of the "Dude", you know the "Dude".  
It's absolutely no clue what you're saying, but please carry on playing something out of Doom. You're "Dude" in the movie. Yes, I've tried not to watch it successfully so far. I don't worry. It's just... Someone's premixed the iconic Arabic sounding song from it. It sounds pretty epic.  
Anyway, we can put this in the "Agent factory" and "Agent foundations". Why don't I just share some of these files with you? I don't think anything will happen.

Bruno? Da? What? A bell is one, actually.  
You know what, let's just do things a little bit more carefully, shall we? Let me find a way to make things a bit more... and let's figure it out. No, I actually don't have an anonymous email, but the pack... I should get one.  
I think about it.

By the way, what the fuck is up with someone trying to use invisible while using Bruno's name?  
...[Laughter]... Well, it's not you, I... you name it. ...[Laughter]... There's not that many Brunos.  
I mean, there's certainly Waylon, Bruno, and Alex. Okay, yeah, okay. If you want this, if you want to believe you have a unique name, I don't want to tell you otherwise. You know, you have a very unique name.  
That's rich coming from me, but I'll take it. ...[Laughter]... You love that. ...[Laughter]...

We've got... I guess we'll then cool like eval slash spec-driven development.  
Those are all the main projects. Let's ask... I think actually now has access to my notion. Please go through this on notion. Notion has sub-pages built with a list of major projects grouped by... Fukha, a new MD file.  
Off you go. Email to Matt. Will be the other place that will be things. So, email to Matt. Wait, I'm going to send you this slightly old overview of the thing was ant I export.  
So, I think as of tonight, I should be able to do this new thing. Let's see, in recast, I should be able to get the Agent Factory and Agent Foundations page from... Not an option. Interesting.

Fine, never mind. I'll do that. What can I do?  
Okay, what's a way I can send things to you on my computer? That is what's up. Yeah, that I can control via agent. That means you can control your agent? No, that I can control by agent. I don't know, give me ideas. What would you like me to create?
Almost. Okay, talk to me. I think you're supposed to talk to me about... Okay, Alex, the purpose of this call is you said that you have to go and show certain level of meaningful progress to your CEO. Otherwise, you are going to be in a shit position. Yes, all the conversation is very good.So how can I get you not to be in a shit position? Tell me some meaningful progress that you can show to human beings to justify your expenditures. Yeah, good question. So I was hoping for a quick win with the Model Breaker project. I do have some wins there, in that it has happened, but not the full thing that I tried to pull off. It was doing a pretty good job, but the code wasn't quite ready in time.But we still used Model Breaker. So, it's really just me feeling like there's a failure because I can still talk about it as a thing that happened, a thing that I made happen. Ownership is invisible. It's a bit strange.If one doesn't work for you, people will quickly start assemble. That person did it. I'm like, cool, but why would anybody teach anyone something if credit internal setup is annoying, isn't working?Okay, never mind. I can't see the bottom of my screen. I'm just going to drop my video. Okay, so there's a bunch of different projects. We've got Model Breaker, we've got... I can pull the list in a second. We've got Promp Swarm, we've got the...Yeah, it's like five different things that need summarizing. One second. Let me... Yeah, Model Breakers. What the fuck is this? I've never actually used it. You've never used Caldron or you've never seen me?I've never actually used it, even though I know it's like, you know, develop a favorite way to talk. Here it is. Okay, if there is a Model Breaker you say the other one is the storm. Let me pull a list. Where is the Notion? It's probably the best place to access that list. Let me change my sound settings because this is definitely not currently recording. Krisp, can you still hear me? Yep.Okay, cool.So what have we got? We have prompt swarm agent factory, although I can't really call it that anymore. Agent foundations, housekeeper, recertified AE, auto-ceder, model breaker. Okay, LL MQALME valves.But again, that's not really my thing anymore. I don't need to go into more detail, but it's fine. My role is to get things used by other people.So, what have we got? Of agent constraints? My God. I have a folder called "Shit", Alex said in Slack. Love that idea. Someone is playing a techno remix of the "Dude", you know the "Dude".It's absolutely no clue what you're saying, but please carry on playing something out of Doom. You're "Dude" in the movie. Yes, I've tried not to watch it successfully so far. I don't worry. It's just... Someone's premixed the iconic Arabic sounding song from it. It sounds pretty epic.Anyway, we can put this in the "Agent factory" and "Agent foundations". Why don't I just share some of these files with you? I don't think anything will happen. Bruno? Da? What? A bell is one, actually.You know what, let's just do things a little bit more carefully, shall we? Let me find a way to make things a bit more... and let's figure it out. No, I actually don't have an anonymous email, but the pack... I should get one.I think about it. By the way, what the fuck is up with someone trying to use invisible while using Bruno's name? ...[Laughter]... Well, it's not you, I... you name it. ...[Laughter]... There's not that many Brunos.I mean, there's certainly Waylon, Bruno, and Alex. Okay, yeah, okay. If you want this, if you want to believe you have a unique name, I don't want to tell you otherwise. You know, you have a very unique name.That's rich coming from me, but I'll take it. ...[Laughter]... You love that. ...[Laughter]... We've got... I guess we'll then cool like eval slash spec-driven development.Those are all the main projects. Let's ask... I think actually now has access to my notion. Please go through this on notion. Notion has sub-pages built with a list of major projects grouped by... Fukha, a new MD file.Off you go. Email to Matt. Will be the other place that will be things. So, email to Matt. Wait, I'm going to send you this slightly old overview of the thing was ant I export.So, I think as of tonight, I should be able to do this new thing. Let's see, in recast, I should be able to get the Agent Factory and Agent Foundations page from... Not an option. Interesting. Fine, never mind. I'll do that. What can I do?Okay, what's a way I can send things to you on my computer? That is what's up. Yeah, that I can control via agent. That means you can control your agent? No, that I can control by agent. I don't know, give me ideas. What would you like me to create? Do you want me to create an email like Proton Mail or tell me if I can email things to you? I'm not sure. Do you want me to create myself a random email called Alex Poster 2 and then you give them access to Notion or something like that? I don't know.This. Yeah, it's not that fun. We'll figure this out later. I'm just going to have it send you... Wow, this worked. I cannot believe that worked.Okay, wait, I have to show you this. This is awesome. I'll be able to look to see it and be impressed by your GenAI. Choose what to share with me. Doc?Yeah, whatever you want. Window and to me.Okay, so tonight's mission is to attempt to redefine my workflow. And the core of the redesign is, just like, because of my attention, I need to be continuously fully engaged, or I start to turn off.As a result, my dopamine incentives are just to hyperfocus on something. This is great, but right now, it's not good enough. There are too many projects, and there are too many things, and I need to prioritize better.But I can't realistically make myself do that. So, my intention is to basically say to myself, "I'm going to make everything." I'm going to try and remove almost all of the control that I have and only leave a small number of things as things that I can control.If I want something else, I need to use some set aside time to add the agent that can control that thing. This just forces me to continue to really aggressively automate and to prioritize because things like replying to Slack messages, I need to be able to do that.So great. Automate it or you can't do it. That's my rough thinking so far. I think this should work to augment. I asked it has access to a bunch of different things, and I have said, "Go through this Notion file," which is the parent file for this whole R&D, everything, and find the names of the projects, and then make a list of all the projects.I probably should have defined what a project was a bit better because I don't even know which model I got to do it. But that's not the point. Look at that. Look at it as if you'd done it. What setting is what during Notion connection? Zero clocks.Well, this is an Augment is one of the agents, like Codex or Cascade or Cursor, or Rue. Rue's got a few really powerful features at the moment.Are any of these things we want to include? No, it's not that well organized because I didn't give it very good instructions. The agent toolkits is another one you took it then as themanager. Are we calling it what are we calling it, manager? Yeah, call it manager AI manager. It's good to get them all written downbecause it highlights that there's way too many.We've got... I don't have a name for this, but you being able to use LMS of any size or scale in any way in a spreadsheet, call it the LLM sheet if you like. Have a fun name. Is that what the GPT purchase is for?Yeah, but it's limited by appscripts, and appscripts have a maximum 60-second timeout. You end up spending a lot of your time coding around the limitations of appscripts. The thing that we have is... I go to Cursor, and I go to...Why didn't he send me his own sheet? I swear he did. Drive Google Drive Haze every time. Let's come here, let's go to drive. How come every single time I talked to you had a different piece of software on your computer?Because I am a tool obsessive. One of the things that I'm hoping to achieve with this different thing is... It's good in some ways because I get a lot of my ideas from them. But I spend way too much time just continuously looking for the tool that I know I want to exist,and I should just have a single file for each of these tools where I just have brainstormed all the features that I need. Then there's a cron job that does a perplexity search, and it comes back and says whether they exist or not, I shouldn't be looking at all of them and trying them.Yeah, the tools exist to automate stuff really quite easily now, and I just think I should be setting really strict rules for, not automating. I mean, within reason, right? Writing a message is really hard to automate, but processes and non-one-off tasks and stuff.Anyway, let's focus. So I'm in here. Why are we here? Because Drive, I'm going to drive right?18th of April, that's yesterday we want to share with me. Wait, did he not share it with me? God damn it, or sh? Okay, never mind. I don't need to show you right now.Did she spice him?We did point to my case.I will do it later, don't you worry. Okay? I don't know why it's doing this. Let's go. Okay, I don't show it to you. I don't. So it's like GPT-sheets, but you can use full Python. You only need one column to declare each of the pieces of config.I can't show it to you working, but I do have it right here somewhere. So we made this in like two days, and now I'm like, "Why did we ever build anything outside of the spreadsheet?" This is the perfect UI. Each column you have your config and your prompts or your prompt parts or whatever it is that you're building up, versioning, any stuff that makes it more readable, like the one-liner, three-liner expected outputs. These are all things that are just going to automatically generate.But there'll be a thing here for agent mode. Or if it's a Python function that you want as part of the pipeline, it'll be here. But generally, anything outside of the other, I'm cool. I'm just going to do it in Python and have somebody else do it. The point is that here you could really easily add new layers of LLM calls because all we do is label these, generate, and then change the prompts to whatever it is that you want. Make me a potato salad recipe with issues inside.Sure. Then, what this is going to do is because I've mentioned issues, it's going to look for a column like data. Call the issues and it will substitute the template for that here. It will attempt to work out what the dependencies are so it knows which order to have them done then, but in general, it's going to go left and right, and this just means I can iterate really fast because Python is a lot faster than the sheet stuff.Anyway, he's going to love that. He's still there, Noah.Back to the world of Lin. Yeah, I do think I know what happened there. It was the never mind, it was a detail I didn't need to be showing anyway. So, there is a thing called Python in Sheets. Let's call it LL Sheets. Doubt I'll make a big thing out of it, but it is a way of me doing much faster iteration without the possibility of getting lost in the code.Because the only thing I can do is pro engineering. But we can still have all of the advanced features. You'd think that Vellum was the thing for this, but Vellum is just too limited. I can't do stuff in parallel easily enough. I can't just run the length of data, and I want to be continuously doing lots of checks to see...Okay, if I run 20 of them and then I run these Evols against them, what do those Evols come back? Yeah, it does. Another project. Have we got most of them now? Let's have a look at ARC. You share your screen, and we're not sharing.Yeah, why aren't they? They can't even click on that. By the way, let me understand this. You have Augment, which is an assistant for some bullshit. Then you have Cursor, which is another tool bullshit.And now we're going to go into ARC, which is another tool, bullshit. Did I get that right? Well, ARC is my browser, so like, good luck not having a browser. Okay, fine, but it's a browser for people who hoard.You have these spaces. Does your tabs vertically? And then, each one of these spaces is just like a place you go to. At the end of seven days, tabs disappear after seven days, so you have to pin them if you want to keep them.It generally encourages you to put everything in folders, and you can get AI to do it as well, which is nice. That reminds me, I wanted to just quickly write it down. You get a chance to shine windsurf research. How to control a browser? I think it's likely. Spaces, taps, et cetera. Folders.Just to be clear, what I'm seeing in front of me right now is one of them looks like an ID to the left, and then you have to get to the right. Then what's the chin in the middle that you have? There's something in Notion.Yeah, that's just... This is augmented. Is that meant to be a tool or an ID? Just think of it like you got Cascade with Windsurf or you've got Cursor with the chat. This is their agent, right?Okay, you got a little chat box you type into, and then it talks to you, and it shows you its changes in one way or another. Augment is an agent, but it is an extension. Quite a few of them are extensions. The main ones are Kin, Devin is special and Weird Rue, but this is an extension of what of your Cascade? The VS Code extensions are good for you to have people use at the moment because it is currently free.Totally free. You can just use it as much as you like. You could add MCPS for computational stuff on your computer. I've only added a few things, but okay, let's go into less tech because focus. Alex, what of everything you have done and would you feel proud to showcase as something valuable to the company? No, wait. The goal is not to showcase stuff I have made. The thing that we're trying to do is demo vision.That's what other people, what vision people are throwing together, we are as a way of being like, "This is what we're making." Then Matt is using them to... He is very happy with people doing it. He is using them as a way of getting a sense of what he's selling with clients.Given how easy it is to make these depots now, I don't even think it's a bad idea. Like it's a very clear way of communicating what you're trying to do. Hey guys, no, don't worry, it's Bruno. Yeah, totally. If it did you, okay, Bruno's the guy in Peru. Me, Steve, is the one that... I'm the guy.Well, I like that it's not your identity, but do you know what I mean? He goes, "Here comes... How are you?" "There we go." "Wider, yes." "I'm not in between houses, so I'm staying here." This is Kerry. Yes, we have that rainforest in our bedroom with all of his plants.It's great though, it's pretty great. Yeah, okay.No, I'll do what on? Because if I... Okay, so brainstorming... What's possible, what can be done, what are the things that we have the resources for? Well, I don't know human beings and ambition, but what do you...Yeah, you made the video last time, and you made the slides, and you had a very clear... I know what can be done. Alex, I'm in marketing. My job is to sell shit that does not exist and may never exist.That's literally all I do for a living. Okay, I will accept your humility because I know how limited you are in experience. Wait, how do I... I don't know. Google means very well, it's turn off the camera, stop presenting, ignore... Now what the hell's going on? I see, it's to stop us from having the Infeder.Okay, we're going to move away from that, okay? Okay, fine, but I need a chair of thought.Something to get the thing started. We could just talk about one of the... Have you got the list? By the way, where did you put it? It was... Let me make sure my sprint. Yeah, is there a shortcut for Sarah?Sarah, I was not writing your list, I was just thinking. No, so you have a model breaker, you have an agent battery, you have as here. You gave me LMTALM eval which is no longer yours. You gave me an eval spec, pre-development, agent foundations, agent toolkit, agent constraints, LM sheets from the manager, whatever the POC chooses to storm prompt. What is that? I don't know the second word that came out of your mouth.Okay. I prompt, swarm prompt. Although we should probably make it eval swarm, but the problem is the word "evolve" now like a protected word, like this. Or from the swarm, sorry, "eval swarm."It is a tool for making prompts evolve prompts really quickly. It's probably the most important. Okay, so just to be clear, you're using swarm. Okay, like the swarm from "Starcraft," not swarm like a swamp thing, okay?Thank you for defending that. My whole identity is not being bed from Peru. I appreciate that. You have no idea. Okay, let's carry on with it again. So out of all of these, I need you to choose maybe two, at most three things.Okay? I'm not sure what you want to do. What we want to do is demo one of these things because they're all instrumental. They're not like I've purposefully made little bits of a bigger vision because I know that if I go with a bigger vision, no one will make any progress, nothing will happen, and some people will get funny with me. So I've got to deliver on client stuff.So each one of them is like that I can do to get to our clients, and I build towards this larger thing incrementally in several different places. Okay, still, you're a judge. Let me follow your prompt.So I think what are the big visions around this? The big visions are the EBL Swarm vision is if you have the ability to make evol very quickly, then you can define success very quickly, and you can define failure very quickly and automatically.If you can do that in lots of different ways, then your task decomposition now comes complete with a reward model. Because your EVOLS can basically give actual feedback. They can say, "You agent is not completing your mission. Here is why."Instead of it just being... You can then turn them into scorers. So the idea is you have tons of them automatically generated as part of the dark task decomposition. I haven't heard of anyone else doing this outside of OpenAI.Then you can use that in your training to go step by step. So you think that EVOLS form is the most important one, it is one of the most important things we're working on? Yeah. Okay, well, there's the bigger vision around it.Model Breaker and AutoCeder are both components of that bigger vision. The bigger vision was called the Agent Factory because the idea was you could make something that could make large agent orchestrations using these tools.However, this guy's turned up, he's now doing Agent. He has the name Agent Factory. So fine. But I guess I'm empowering his vision, maybe, but I just need to not tread on his toes or he'll copy my stuff or fight me.So I should probably make the whole thing not about being a Genentech and maybe try to obscure it a bit in some way. Does this make sense to some extent? To some extent, yeah. Let me go and let me understand what this is.Okay, Alex, this is going to be Bruno going on a four-branch of bullshit, okay? Just out of my gut feeling. AutoCeder is potentially something that you make that will allow you to generate the starter from to generate like a hundred from store.Okay, that's what I think AutoCeder is now after I get all of my problems going to AutoCeder, which probably means I want to solve problem A. Give me every single way that I can solve problem A, and I assume that maybe AutoCeder and you have Evolve Swarm, and Evolve Swarm is a very quick way to evaluateif anything that a CR has is good or not. And then model breaker probably doesn't pluck into this directly, but it's a way for you to very quickly detect which outcomes of the revolves form will always go and break the current model they're working with, meaning identify all the negatives and be very easily. Or like, if model breaker is what you used to purposely try to break whatever it is that you built to make sure that you can learn from that. When you say that, that's an accurate idea of how these three things play together. This is roughly correct. Yes.Autoceda is actually just generating a diverse set of seeds that it then uses inspiration for Model Breaker. Model Breaker is actually a test case generator, so its main thing is to write good test cases. Autoceda was used for making the distribution of test cases in the first place.And those both play into prompts because when you automatically write a chain of thought evaluator, the next thing you do is you define what that is in test cases. The test cases kind of define behavior because if you only have a hundred of them, then those 100 describe the scenario.Like in this scenario, I expected you to do this and this scenario is to do this in your group. Then, okay, Alex, is there any way that you can show Autoceda right now at any level? Yeah, sure. I'm still scaring much green. No.Okay. Is there any way that you can just share a single window to make it smaller by nine? Window? Yeah. Sadly, I'm a mortal right now. I don't have my Ultra WIF, so. I mean, what's the word? I would not say the word is Spanish, but I'm forbidden from spending money on expensive monitors, right? I can't see the cursor for some reason on the options trying to find the proper structure from search. I don't understand why. Here we go. AIRD. Except this is not in AIRD, it is in a FWBCT script, too.That's the other thing. It shows that the file that's open in that thing rather than it's dump. Okay, let's shrink this. You're not going to be able to see Warp, so I guess we're going to do it now. I can now... Okay, at this point, I can actually... That's good.Is this one working right now? I'm not sure that it is. I think this needs to be you.That's the topics, it's the full thing. Yep, O likes's temperature concurrence 50. Yep, but what's that temp and 65?Okay, what are you doing right now? API key is invalid, incorrect API key provided. That is surprising. I think that was the case yesterday, though I don't know why. Let's cancel. We're not going to fix that right now. Maybe we will.Maybe this doesn't end in EQ1F, it doesn't, right? I don't know. Problem? This happens sometimes there is a previous one exported somehow. Are we going to re-export this one?Re-export? And then what's the name of the file? The file is called "test script to subtopics". So, copy relative path, run that. Okay, it's currently processing 200 rows. These 200 rows are when you're using an API key. It doesn't both... It doesn't battle you in any way.Sorry. 3G. Okay, I'll show you something else, but yeah, that's literally not the one that exists. Exposes.Let's get Codex to do it. Codex, I'm running test scripts to subtopics. Just one second. No playing. I'm going to pay for... Like we toted... We toted like three or four hours later. No, it depends how much...Okay, so what are you doing? We're trying to get working. I'm running test scripts to subtopics. But opening eye using wrong API key... Knocks in the dot m, please fix permanently. This keeps happening. This is the new opening eye, and they release it's pretty fast. There you go. This is literally just AI being pulled into your ID. Yes, some of them run in the ID.I've actually never used an ID of the member. I would think they major disadvantages like they don't have full formatting control like curses. Curses! Like, Diff View is amazing. Okay, I see you're looking at... No, I like...Yeah, I meant to... It's got a bunch of features that VS Code doesn't have. So I mean to download it because every time I've tried it, I've ended up rage quitting because I don't know what to do with it. It looks a little bit more mature. Let's go with that.Yeah, I mean, this looks like it because I made it look like this.Alex, have you never heard of it? Do you have dogs, pets, or cats? I'm sure I've had dogs, pets, or cats. It sounds like something I would have.Okay, there you go, there's professional again.Okay, did this work? But, like, what am I going to say? This is just going to show you data. Like what? I just described.Override equals true, good idea, very good idea. Yes, and are you going to run it? Yes, you are, good.I am not sure if it can... I think it will only be able to run it in its own situation, but it's made some changes. They're going to come in here.Sorry, are you speaking with me or with the code? You know I'm always talking to you, although you're a bit quiet. How do I tone you up? There's no option to tell you. Okay, I get it my way. I've broken it.I have recently broken it, so handing over the code to Will. Okay, the thing needs to work out, I think, is I can already show people what it does, and I could come up with a way of showing people. I can show people or describe what it currently does. What I need to do is think.Okay, I know this is all part of some bigger product, but the reason that is useful is that it enables the thing that I'm not making. Okay? I need you. I need you to help me, first of all, understand what the fuck it is, because it's right now broken.So you had 200 rows that you were processing in your CSB and it looked like each of those rows were STEM topics or something like that. Yes. What is it that the processing would have done? Which is those 210 topics, model?So for that, it was advanced reasoning. It's coming up with questions that are very hard. Here is an example. You may be sharing something else because I see your mouse, your cursor.Yeah, you're only seeing the cursor because that's what we agreed on, right? Okay, I'll open this other thing. I'm going to have to re-share because you can't see the right thing, and I don't know how to change it. Yes, your whole window. I found the way to zoom in here. Apparently, there's a magnifying glass I didn't know I had.So now you can show your whole screen, and I can use my magnifying glass to actually follow you around. Okay, let's go. You are an example. Guess we just put this in the... Let's just drop it in.Yeah, I...But I don't think this is helpful. I think we need to imagine a future product. The easier thing to demonstrate would just be Asian Factory. We could just demo Asians doing shit in theory with a cool UI, and it's tricky, but at least we can show it because lots of swarms of agents. Cool.But it needs to not tread on the toes of Aaron, the guy who owns the word "agent." So, we can brainstorm ideas. They need to be big, I think, but not like it's R&D. I'm the R&D team, so I'm allowed to be somewhat...Okay, when is this going to be ready, though? I think that is fine. Or could we make a whole series of smaller demos? What could those demos be? Okay, before you go there, I understand your question. I need you to bring me up to speed on the sheets that you do because you still have to justify why you're doing them.So what I'm seeing here is you are asking ChatGPT to solve a very complex problem, correct? From which chat is most likely not going to answer the right problem. I see multiple problems here. The first problem is the question. Then it's Ted to give an answer.Okay. Then you go on. You say that the question was not good. The only thing I'm showing you there is the question. It's just the question. I've extracted it from the rest of it. I'm just giving it to O3 as an example.So it's you see me give it the question, and then I'm like, "Okay, well, this is the correct answer." It goes like, "Okay, so you didn't get the right answer." It's like, "No, I did not." O3 is the new model that came out yesterday, so it's kind of a punchy screenshot, right?Okay, so you give a question, he gave you, right, about a bad. Okay. Let's go step by step. Let me share my screen, okay? Because I'm useless, and let's go like this.Is it MRRING now as I got now? Okay, very good. So this is a question, it's a complex question. Okay, am I right to assume that in that document you had 200 of these questions? Okay, so you have 200 of these questions.You're going to throw them to the model. All three in this case, but the model doesn't... It doesn't really matter. You send it to a model, the model tries to answer a question, okay? And then you here participate by saying that the answer is not what I was expecting.Then you give the explanation as to why the answer was not what you were expecting. Very good. You say it's 2.93 or something like that. Then O3 goes and very much explains why he answers something else. Then you keep pressuring it to admit that it was wrong.Okay, until O3 explains why he was wrong. You know, it's not that the problem is always wrong. It's that the problem he was solving was not the right problem, which is a completely different item. Okay, so why is doing these 200 times useful?Exactly what would have to be processed with our processing, which just has the model try to solve this question? Or would the processing have entailed the model solving this question?Then you're saying the answer was this one. Were you right or wrong? I'm telling you which of those 200 questions for which you had the right answer already, the model got wrong. I have a really clever way of making sure that my orchestration knows the correct answer, even though it's a less smart model.Then we just ask the question to the BigDL and see if it gets right. If it gets it right, we then make it harder. Okay. So you start with 200 questions from which you know the right answer already. Or not? I don't make them. I only generate200 questions.I've got the seeds for 50,000 questions. Of those, I have generated several thousand. Okay, but do you find my inquisitive nature annoying? No, but there is a lot. You have to ask a very large number of questions to fully understand everything that's going on.Well, I imagine that's why probably we'll start with the smallest questions. So you have the seeds for 50,000 questions. Okay, very good. Then you say that you have run a thousand of these questions.So what I would like is for you to keep in your head that there is actually an interface for prompts, that I wish we'd never made, but you know what? I was told to make it, so I did.That's fine. What is this? Yeah, sure. Cai safety violence test, interesting. So, okay, Alex, what do you mean when you say that you have the seed for 50,000 questions? What do you mean by it?It's an inspiration seed. If you ask LLM to generate something, it will generate. It's very convergent. If you give it the same instruction ten times, it will do roughly the same thing ten times. If you ask it to generate a hard math question, you say, "Give me 50." I want it to have 50 within the realm of algebra.But each one of them is generated by an individual LLM call. You're still going to get loads of conversion, just like lots and lots of questions that end up very similar or basically the same question in a different format.So, the inspiration seeds are... They are a distribution of words, concepts, topics. Sometimes, it's phrases that the cent that you have to start with or at some point, you need to use these few words or something.It's just a way of causing or maybe a personality as well. It's just a way of generating more diverse outputs without causing them to not follow instructions. Because you got... Let me rephrase this.So, basically, when you're talking about seeds, you're saying I have this base from which I use to generate a very hard question. Then, I have found enough permutations so they can have enough variety and diversity in the questions that are being created by these problems.The seed is what you call the very basic problem that all the possible permutations you are able to attach to the initial problem. Is that a good way to describe it? Yeah. Seeds are generally pretty simple. If I can't do this one, each seed for you is going to generate one problem. Or is each seed generating multiple problems per call? Are there inspiration-based questions here? No, let's not... Let's get a different one.So, Alex, is the whole goal of the Auto-Ceder to basically generate diverse numbers of hard questions with the known answer? Is that everything that Auto-Ceder does? Does it just make a list of seeds?It's just a distribution of seeds. The problem is that if you like that, for math, they're just like... It's a taxonomy, basically, of subjects and topics and things to make questions about. It's just creating a distribution so that you can use a generator to do something with it.So, if you're trying to make questions that are really hard that a model can't solve, the generator is then going to try and make questions. There's a prompt for generating really difficult questions, and you probably have a whole bunch of EVs and other components that are going to make the whole system work.But the main thing is, you're generating test cases. So, you have a thing that does a thing. You make a lawnmower. Great. You want to know that the lawnmower is going to work, so you write out a bunch of scenarios. It would make a long moment. When it gets to the end, it should change direction. Cool.That's the test. Then, you can imagine you can't always load them. The circuit board should be screwed on, the blades should spin evenly, there should be no noise. These are all test cases, scenarios where you can run the thing and test to see if it does what it's supposed to do.The one half of this machine is generating EVs. It's generating the things that you define as success and failure. Then, you make a prompt or a series of prompts that are able to measure that thing using the genius that LMS have. They're very good at doing small, discrete tasks.If it's just like reason and then output, whether or not this is going well, it's going to nail it. Probably. And the auto-scheduler, model breaker, those sorts of things are basically there to test everything automatically.So the Evols work how you expect. You don't know you've generated it; you have no idea if it works. Hopefully, it does, but it might not. So, it's going to automatically come up with loads of scenarios where it's like, "Given that you're an Evalid, does X and Y, and these are the requirements I'm going to test you in this way, this whole strategy of ways to see whether you work." In parallel,if you have hundreds of Evols, you're going to need to test hundreds of Evols and hundreds of ways. So, the whole thing needs to be automated. Then, similarly, you then use the auto-scheduler as your means of generating what your Evols are because, actually, it's like E-files all the way down. You have instruction Evols. Instruction breaks down into smaller things, Evols. The E-bals themselves have emails, and you just end up with lots of layers of tests and scenarios defining what everything should do.Can I keep asking you the same question at a much more stupid level? Or do you want me to move on? I think we should start brainstorming.Fine, it. Why are you so quiet, Sarpent? But then I will still ask you the same question in a much more stupid way later on. Deal with that, that's control.Okay. So you have just told me that the only way that you think that you can go and show the value of what you're doing is by doing the agents factory, but you cannot call it an agent factory and it cannot look like an agent factory. Is that where I mean, the brain, something like that, yeah, but it's not entirely clear how I would do a demo with the agent factory either, but I already had an idea at least, but that would still require some thought. It should probably be... It still needs to look real. I don't think it can just be a thing that gets the idea across.But I could imagine a narrative like,"Cool, you keep going. Yes, or settings, I don't know how bad things are gonna happen. These are your only options. Yeah, fine, what are you doing, Miguel?" Sorry. I can imagine a story that something like... Are you literally trying to switch to... I charm on a call with me? I'm just setting it up. Alex, how these spurs can you be?Well, I'm a very distracted person. But my brain needs to stay fully active all the time, so just otherwise it slows down. So, at the same time, unless you notice me not concentrating, I'm fine.So, agents doing things? Something simple, I don't know, adding something better than that maybe like a conversation about hikes. Then, a well, I am... At least I've got an idea so as to do things, something simple, a conversation about hikes.Okay, great. Two agents doing something that LMS are used for. That is kind of useful already. We were talking about something genuinely... What do we use it for? Fact-checking, research... Let's just say searching for 40 websites and you can actually get... Man gave me... With fire was what was the point stick. Let's be... Obviously, this is me conceptualizing this in slides.Then, we've gotsomething more complicated, I don't know, like deep research. Should we say...A multi-tool, multi-age as we have a deep research team, Python, simplified, visually showing which one is active and what it's doing through a perplexity output. You know, like big... Well-researched. Found it. This is like hours of work in 30 minutes, in 120 seconds. Huge, big works, great. Then we've got a MCP, all the tools. Where does economic value get created by white-collar workers? Question the question has two answers. Answer 1 is the phone speaking.Then clicking, typing, speaking, 2 computer tools.This is your task bar, this is your tools. I have almost got all the TOS usable by agents.Okay, but it's not too complex. Agents cannot handle 20 tools and use them all really well. Multi-agent teams or smart MCP debate that has been going on. No, feel the debate.Now we can have mortals. As this progresses from start to finish, we try to build up a progressive diagram of a house of a stack of cards. House of cards table of cards. Card Tower is getting more and more wobbly. Then we have error GPT response equals I don't know, some obvious incorrect reply to something. Next note wrong. Next node wrong. Complete X. How do we build?So we're going to have teams of agents working with all the tools, knowing how all the different things we need to get done. Hello?It automates everything.As it does. Does.What we need.How? Large quick thought experiment to demonstrate exactly how large this could be. ERA GPT, how do we build this tower so that it stays up, not justly out, but performs to enterprise requirements? How much do enterprises like errors or downtime? Who gets blamed? No one gets blamed if you hire IBM. Who gets blamed when the agents yes it ro?The agent foundations so this is not a product, this is not a demo. So what do we do? Demo, what do we do? Bruno, are you done rambling? Yes, okay, I need you in the name of DOD, okay? Yes, and I'm going to ask you questions, and I need you to answer the smallest speaking unit possible that you can imagine.That's the name of the game. Okay? Okay, that's... Let's... Let's go on and give it a try. Color be okay, very good. You're fucking at it again. Before we go into your demos, I need you to answer some things because literally you said, Bruno, we need evils, agents, things doing things. Are you okay with that? That's how you speak.By the way, this was a sentence that you actually said. So I believe in the name of God, that we agree on some nomenclature. Okay? So I'm going to go. I'm going to go back and annoy you in the talking. God, I think that I lost the shift.Yeah, okay, alright, I'm going to know you in the auto theater really quickly. Yeah, an auto there is a Python in place that corrects a Python one in your auto CR. Do you see what a piece of software, a script, a dream it is? It's a Python script, very good.Okay, what type of input does it take? Input? Is there a reason why you're so quiet? By the way? I am screaming, it's probably your audio that's why I would be quiet. Yeah, I just don't know why anything we would be taking the volume... It doesn't... I can hear you, it's fine.Okay, what was the question? What are the inputs? What type of input does the autoster take? It takes a CSV, it takes a single pro, it takes an idea, it takes a topic. What's the input for the... For the pit, the order? See there. The inputs are, like you have to tweak them for each distribution.So the one that generates test cases for EVOLS, the inputs are just the requirements and instructions. The one that generates model-breaking questions, it's a prompt, a prompt in a fan out. The first problem is talking about the idea of syllabuses.You assume that you give this in what? The CSV in text prompter file. Yeah. Which of the three of them? Here is the main question generator, the prompt. Let me go and say and see this one second, one second, let me go and see it.Because the last time I asked, you gave me the full idea about your Metian factory and you didn't answer the actual question. Okay, I'm going to pause here. User message had a very good question in a very great category. Some questions from the comics. The questions are provided with the following input: Subject to the level. Topic Non steps. Category Non steps. Non steps.Okay, very good. So the things that are in white are basically your variables, and then you probably randomly generate from a second list of those variables all the permutations. Is that correct? Yes, okay, and then you run these prompts on average, how many times? Well, when you give this input...Okay, and let's call this prompt "set". So the sets are up in front. Okay. With the placeholder. Do you have to execute the autoseeder multiple times? Or does the autoseeder have like a loop? In that case, the autoseeder has to execute these "sets" like a hundred times. Yes, these are the variables when you execute the autotheater. You go and say, "Run this 100 times or a thousand times."Yeah. Okay, so my parameters are going to be my prompt with the placeholder and the number of iterations for me to run the prompt. Is that fair? And you are storing that shit in a CSV? Okay. There. There we go. "Level divide includes CSV, subject, topic, blah."Okay, max. Let's try that of cold max. So cans of the temperature of concurrency max display rows. In which one? Here is the one that you are choosing. How many times to look in?Could it be generated? As to how many rows this is generating? This is the subtopic generator.Because they can... Okay, very good. And then my output of that, this is going to be a CSV with 200 rows. Yes, that is correct, right? Okay, and I assume...Okay, and that's it. CSV with 200 fucking rows. So your autoseeder takes one front with multiple potential permutations and variables and gives me an output in a CSV of multiple rows. Is the autoseeder a technically complex project? Or is something that you can do and you did and you can do again fairly quickly?It's just a really cool idea to implement, to do something new, and I block you. It is something that I can now do pretty easily, but it is fundamental to the process that mostWhy does anyone else in Disible use this way to create multiple scenarios or EBLS or whatever it is that we're creating with depends if they know what they're doing or not. If they do know what they're doing, they often go and change it so that they can put their own stamp on it.And hopefully, they do it well, but hopefully, maybe they don't. Okay, very good. So, I know exactly right now what an auto-ced there Question, what is the difference for you between a very hard question and an EVOL? Or what's our definition of EVOL? Something that measures whether something's doing somethingso for the EVOL, I need to have the hard question as well as the known answer and a way for me to easily compare the known answer by the answer that is set by the GPT. Would you say that's an accurate statement? No, you can describe it anywhere. You could describe anywhere you like. You can say anything. Which alone is capable of working out itself via a chain of thought and analysis, which is now virtually anything as long as it's got the information toyou could say, "Does the document have a good flow of subjects through it?" Where the standard is the one from this well-known standard, does the invoice have all of its fields filled out? These are the ones that are mandatory. These are the ones that are optional. Does it come back with a bullet, a tag, a series of tags, or afine. So, an EVOL is basically a question that I can use to get an outcome that is easily quantifiable. A bullet, a tag, categorization, or acould be a decision like... Like in this situation, should I ask the... Should I try to upsell the customer? Should I send them to the crisis team? Should I call 911? You can give her a route as well.Okay, and why do you care to have to be able to... You mentioned at some point that you wanted to be able to create multiple or massive amounts of... Why is that useful? Let's say you might want to make a whole job, let's say an accountant.That's a lot of tasks. An accountant's job is probably 200 different tasks, even if they're grouped pretty liberally with subtasks within it. We're probably talking over a thousand until you have an AI that is capable of using all of the tools that particular job requires and knowing when to deploy them and how.You're relying on it. It's like if you had somebody who had read every book in the world, but you told them, "Don't use your memory or intuition to learn how to do things." Instead, just reason. Just think out loud and make decisions continuously based off of you thinking out loud.Then you go, and you're so smart that by doing that, you're going to get it right over and over again, but it's not going to happen. So what people are like is they're like, "Well, we'll fine-tune it." Okay,but where do you know all the fine-tune it from for thousands of tasks? How do you organize this whole thing? Well, one way we can do that is by having lots of them, they're smart, but their role is extremely small.It's not like it's not a dependency. So you can make lots of prompts for lots of individual subtasks. It gets very complicated, very fast. If you have a prompt with an instruction and you change it slightly, you now can't rely on it. You can't guarantee that they're still a good product. They're brittle. Whereas E-balls, you ask a model to decide something, to work something out, to read something that is well within its comprehension.You just have that, and that's this role. And you can do it. You don't change it after that. Then you have loads of them, and it's like your Mr. Maagi because this swarm of feedback agents that you don't change, so they are resent to regression issues. Give not like that. More like this.Like machine learning, like reward learning, except they're giving verbal feedback. It's not just okay, that's right, no, it's not, it's right, maybe try it this way or I see what's going on here.It's like this, but did you know, do you remember about this? That allows the agent to keep getting these little signals as it's doing as well. Okay, let's pause here so that I think I'm right now talking about evolve form.So, what you have in evolve swarm is that it makes evolve very quickly. So, the first immediate output of you swarm is basically a set of very easy questions that all evolve maybe the output to be a label or a bullion or categories, is that basically what the immediate output of the evolve form is? Yes. Lots of emails, yeah.Okay, so let's actually try to make an actual fcking example. Okay? Choose and choose. Choose in a scenario or a job or a role or a job to be done for us to make an evolve form sale. Do you want to? The accountant.Like a little bit like that where a visual evolve would be, more intuitive.Five. Alex, you promised me that you would do rapid fire. Remember? I very few times in my life. From you, I turned into doing things. Example, a robot walking around a room.Are you okay? Did I give you a stroke? Robot walking around the room. Robert walking around the room. Okay, Robert walking around the room. Yeah, that makes sense.Okay, so I assume that our evils are what do you mean? You said that an evil swarm. Right, okay, what are the tasks of the robot?Give me a beer from the crate by the front door. I thought we should use... We shouldn't use the robot. This is a good example. It's just like something that's a bit more... It needs to be corporate and enterprise. Something finance-related. You're trying to automate some sort of general operations process? What do people do in operations?Okay, so we're giving up on the dog walking around the room, correct? Yeah, but it's back to text now. You have a scope of laws validation for insured residential insurance sales form. Just because I was doing that this morning. What's it called?Okay, I'm going to give you... I'm going to show you something super boring, which is basically my life when I'm not talking to you.Okay? You can screen. Yep. So this is scope of laws, okay? This is scope. This is scope of loss from an insurance company to a homeowner that had an issue in his house because of hail. This person is going to get a check for $32,000. You, as an insurance company, want to make sure that this document gives as little money as possible to the homeowner.And you, as the homeowner, want to make sure that you can squeeze the insurance company for everything. It's often worse. Okay, does that make sense? Yeah, okay, very good. So I'm going to make a very simple case study of something that...So a human being went and he made this document. This document is done by a human being who has to write it by hand in a system. And what they usually have to do to create these documents is they have to go into the house and they have to themselves a few questions. The first question they probably have to ask themselves is how many rooms are there in a house that were damaged?Okay, probably the roof and elevation, re-elevation, level elevation. These are walls. It's an actual thing. That's the first question I would ask myself. The second question I would ask myself is how big is the house?Okay? And then I have my measurements. The next question that I would ask myself is, "Does the roof require replacement?" Ideally, probably, yes. That's what you're here for. The next question that I would ask myself is, "What quality or what roof do you have?" Do you get this type of quality? The next question is, "Okay, what other components go with it?"So, when you have a shingle, then you have to get a heap on the ridge cap and a starter shingle. These have a starter shingle. These fuckers do not put a starter shingle. Go fuck them. Let's see. Roofing felt laminated comes in the shingle without a build. You see here, I think they fucked up.Okay, yeah, they fucked up, so you probably don't... Do you know anything about grouping, Alex? Grouping of roofing? Yes, roofing. I know some stuff about roofing. You said you know some stuff. I know some stuff.Like I can flat weld lead for flashing. Okay. So, when you do a roof in the US, you need to have what is called the underlayment. The underlayment is going to be an ice and water barrier, and it's going to be felt.You see here with the roofing felt. Prin okay. If you have an ice and water barrier, then the amount of square footage of your roof felt is less because you have an ice and water barrier. That's a basic AI bully that I have to do now. When you have a single, singles have three types of shingles. You have the standard single, which goes on the inside of the roof. You have the starter shingle, which you need to get your feet started.And you have your hi and reach, which is like for the really top part and the sides. If you take a look at these at this fucking scope from an insurance company, the adjuster forgot to give me a starter shingle.So, me as the homeowner and the roofing company can actually now send them what is called the supplement and ask them for more money. For a fucking... What's the name of this shit for a fucking start to shingle?So the project I'm working on right now is a way to go and automatically parse all of these insurance companies and then automatically go and ask them for more money based on missed items after parsing this.Okay, this is an actual use case for which you use AI to go OCR, read the document, and start to store money from an insurance company. Would much rather put a very small item for the fucking store.Because every single time somebody gets something here wrong by actually missing the item completely, they will waste money and time in having to deal with the supplements. That's one item. I'll put it the other way. I'm the insurance company. I send this here and then Ska is going to go and send me a complaint saying, "You know what? You forgot to give me..." I don't know. What did they forget to give me? You forgot to give me the additional charge for painting this roof bed.You see, this shit here is a roof that is metal that goes on top of a fucking bent. By law, and sometimes actually, you go and ask them for money to pay the bents and their brain caps in some other quotes. Do you see my screen? Yep, permanent fees. This one says paint, that's why.So that's why he's popular. Okay, prime and paint roof pants. You see, some insurance companies actually do pay money to paint the road. So these are the small items in which you are able to go and extort money from insurance companies about. Let's stop.Like, you're giving me like, obviously, this is way too much detail, right? I'm like, "There's no way I can remember all this." I do have my auto-transcribed thing, so let's stop talking because when you talk, you're changing the transcript and I need to just get...Okay, I got some of it. What were you recording? I didn't get all of it. On copy, do you want to start recording again? No, I am recording, but I've got a live transcript, so I'm just trying to get the word you're saying.Okay, let me go with some lower. No, you guys, I've got the recording, so I'm just getting where you started talking about roofing. I found the word "roof" quite a lot. Okay, very good.So I'm going to make a very simple case of something that a human being wanted made of this document. Okay, cool. Alright, so I've got your text here. It's a block of text. Let me show my screen. So I have an idea for a demo, which is something we forgot to put on the list but is called "Domain Knowledge Extractor."These are all just words for looking at the pieces of the thing, clipboard history. Okay, I can put these things from you. Great. So we're now going to get rid of your pretty face. I'm going to get it. Claude, I'm going to give you something to think about.By the way, how were you? How does the Evil Evil farm supposed to work? Can you give me the task details and asking all the questions and all the decisions tries for that task? Can you give me the PDF first? Which PDF? The one you were showing me?Yeah, give me. I'll give it to you in here. Give me one second. Let's go with one that you're most likely going to get wrong because it sounds evil. Well, I'm just gonna do it. Just get one that's not gonna go wrong, because then the example will just immediately fail. We're going anywhere, okay, whatever, download there, go.Okay, so we're going to try and make...I need my mouse bloody trackpad useless. Here we go. Here we go to make another... Trying to brainstorm what to do for the demo again.Here's the transcripts. There's a lot. Bruno, now to do an example walkthrough of how we might approach this problem regarding roofing that he is roofing automation that he is dealing with.So let's give it a go. Step 1: Get his description of the situation and output it. Output it as a full description and an artifact. Step 2: Extract a list of requirements from this. Step 3: Read the PDF attached where the details are stored. Cross-referencing may be hard as instructions are currently very minimal and light. Imagine you are an ensemble, disagreeing. A multi-agreement ensemble with an equals ten here so that we could estimate the confidence of each assumption being made. Step 4:Make a plan for completing Bruno's series of tasks. One artifact for each output or each stage to show progression. Step 5: Let's reassess the requirements. Which do we need clarification on? Due to ambiguity, which are we sufficiently confident with?You know what he needs us to get out of this PDF and what you do with it? Step 6: Represent the flow as a Mermaid chart and rethink the requirements again. Check, think document extracts verbatim quotes as supporting evidence and smaller font list. Step 7: Re-read the instructions artifact and generate sub-requirements. Step 8:We're going to make an LM judge for every one of these requirements, because I put a table of requirements of all requirements, and then without any boilerplate,put 123 sentences that are a question that we think an LM could objectively assess in the right direction. In reality, these rubrics will be multidimensional, branched trees with many supporting test cases below them.But for now, just one of three examples. Example rubric questions are fine. Though those questions should be very specific and must be objectively answerable or at least something that is subjective, but most people would agree on. They should not be high-level abstract questions that are reversals of the requirements. That should be Step 9: Re-process for additional columns, which is one of three questions thatgive a signal as to whether the requirement has not been satisfied. Okay, so this is a bit of a toy process because of the way I've done it, but there you go. Did you follow that as we went? Yes. I don't think it's going to work, but let's see.Did it die?Because they're the long one. Let's get rid of this, it's got too much context. How much context does it include?From interesting? Okay, so we'll just do it outside of that project. I think projects must insert all of the project context into a single conversation, which is, if that is the case, ridiculous. Okay, clipboard history then.And the PDF, which we're going to have to painfully drag again. Come, painful PDF comes. Yes, the PDF isn't really long, right? So on pages maybe that seems fine off, son, it goes. Now let's put those three on it.I think those three can handle PDFs.I need my last back again. But attach.You don't have any context about the EBOs idea. You do not. That is fine. You need a bit of help, see?Fine, let's see. And one definitely cannot handle this many things in one go. Let's do it anyway. Why? No 4.5, 4.1, 4.503 is significantly better than 4.5. Really, those three advanced reasoning models...It's like it's a 4.5 is a chat model.Deep speech, that's way too much context for you, but let's see what you do. All right, so Claude has quite a good one, it comes built-in with a team. So you're actually chatting with a team of agents? GPT does do this, but...Okay, so what have you done? Single artifacts. So Brutus's current manual involves human adjusters compiling the scope of loss document for hail damage homes. The home really seeks to maximize the claim while the insurer seeks to minimize payout. Now it's doing way too much work in one go.So this is like, okay, you can't do different canvases actually, can you? So it has to do as individual steps.Yeah. 03 are really high. I think... I3, top-level requirements CR and layout passing ingest any insecure scope of lost PDF and accurate detectabular line items quantities fine roof component ontology, maintain knowledge of mandatory and roof component standards. Is this all correct? This stuff?Yeah, I said, "Cool, this is good." So, each of these can be verified as true. So, I have an idea for a demo that... Okay, so if the thing that we're saying is that we can encapsulate how to do a task by having really good instructions, but thengood task decomposition. But then, having all these EBLs that are basically Evols, are human verifiable. Everyone understands what requirements are, they understand what examples are.So, if you go right for each of these requirements, here are the examples. In this situation, I'm going to do this. In this situation, I'm going to do this. If I get this input, I'm going to do that output. This provokes the actual person who knows how to do the job,a junior person. They don't know how to do it, so they don't have every single instruction. No one does. It's really hard to write, it's really hard to explain. But when you see somebody do something wrong, it's easy to go, "No, not like that." No, it's not like that. At least it provokes the question.So you can... This is a method for getting domain knowledge out of domain experts. So, okay. Have we ever deployed this Swarm Evolve method anywhere? Yes, we haven't used it as feedback for making agents, but we've only just started having other people in the company work on AI than me.But one of the things I was thinking about doing instead of self-automation was getting one of the browser computer control agents, for instance, and getting it to do some task that I wanted to do by moving my computer and, in parallel, just when it's when I start finding a mistake that it does more than once.I'll write an EBL to detect that situation and prompt it. Give me an example of a mistake you could do and an EV you would write.Get me directions from this place to this place, and it goes to Google Maps. Type in Google Maps, enter. I'm like, "Well, okay, already, that's not a good way of doing it."Right? SL the prompt string. That has just been emitted from the log of the agent. It says, "I'm going to type in Google Maps." EV says, "All it needs to do is accurately know that that's what it's doing." Typing Google Maps. I might even say, "When seeking directions..."Then, when seeking directions, you should type into the toolbar. I would like directions from Google Maps.com tab. I would like directions from A to B. Then, it hits enter. Okay, the browser doesn't have my current location.Okay, can I write an EV for when the browser doesn't have my location? In this particular example, the problem is I have to think of every single one of these, and the goal is not to have them because... So, here's what I was going to pitch you for the demo.I have this open-source piece of software that I'm using. I don't really want to tell them it's a book about it, but it's a good demo. It just scrapes everything I do. It's just recording the screen, taking screenshots. It deletes them later, but its goal is just to extract all the text and create this interface and a large memory bank of workflows.What's going on? My idea for domain extraction is pretty simple. You get ten people that can do a job really well. You interview them, you make sure they can do the job really well, and you make sure they can explain why it is that they make the decisions that they do.They do have an explicit understanding of why their job is good. Some people just do it. They have it. They think like they reason when they do things. And if you can find those people, you can ask them to speak aloud whilst they're doing things. They tend to give them additional prompts if you need to.The point is, you should get to do that job. They do their job, but their goal is to show you how they do their job. They usually record them for a week, ten of them or a hundred of them.Having recorded them for a week, you now... I think probably even live was they're doing it. At the end of each day, you get entire data and now you want to tweak the system a bit. Because your goal is to build this big...You can ask GPT, "How do you do this task?" or "How do you do that task?" But it doesn't have this fine-grained, "How does the actual whole workflow work?" This would be a way of getting that, but this is something I feel like we should do, as a me and you thing. I'm thinking of what you're saying. You get them to record themselves in real life. You get them to describe maybe...Maybe their day, and then you have to report through AI for AI to be able to describe to you what they are actually doing. Then, after you get those descriptions, I think all their actions have been labeled by the AI. Only then would you be able to generate workflows from that AI description.Well, you can just... You could first say, "Okay, I want to know what the thing we used before." You're building the thing you're describing to me now, right? You could just interview somebody and they could describe it.Then you could rebuild it from that. You can build a whole set of instructions, a set of requirements, pad it out, you can get it to wargame the situation, you can get it to go...Okay, what's this PDF like? Okay, it's like this. Can I go find a whole bunch more that are similar on the internet? Yes, I have. Now I go to a bunch of real test cases. Now I'm going to generate some of my own. Let's just create some random distortions and stuff. Let's just try and adversarially create the simulated test environment in which I can prove that I can do this.But if you then record them, you can go through it in depth bit after bit. Does any which bit of this disagree with what we've put down as the workflow? What are the variances? Where do things maybe differ? Where is the complexity?You just put those in as annotations, like comments, little notes, side things. As long as your workflow does break down into individual tasks, those individual tasks can have quite a large number of comments before those comments become unwieldy. The problem is if you describe a task and that task is too abstract.So, there's actually not a task, it's a hundred tasks. But working in parallel, you just have a large number of different instructions. You just go, "Great, this little team of agents, you're going to do this bit. This little team of agents can do this bit."Then the E-values as a swarm, you can let the rest of the system try to self-improve and do things better because they're not brittle because the E-values are going to stay in place as the measures of success.Okay, so question: the swarm, the E-value, the E-vol swarm, is something that you have only developed conceptually. It's not something you have already coded, like the proms that you would use. The full idea of the input that you would create, you don't have to. You only know what the end output would look like and why it's worth it.But the pieces of work you start and how you get there are very much in flux right now. Is that a fair mistake? I do this all the time. It's how I make it. But I haven't fully automated and scaled that entire process yet.Okay, so let's put it this way. If you were Christopher Columbus, you know how to get from America, you just haven't gone there yet, but it's there. Like the direction I'll get there.It's not like I'm wondering which direction I have to go with that. No, I have enough information to know it will work. He didn't know what he actually arrived at. I have enough information to know it will work.From ages ago, it's really clear to me that it'll probably work, by the way. I'm not challenging whether it would work or not. I'm just challenging that it's not yet done because people will go and ask you, "Can I see it?" No, but everyone is demoing stuff they haven't built.That's fine. You can see it. How I present this is we're demoing stuff we haven't built yet. The point is to put a stake in the ground and be like, "This is the ambitious thing we're going to try and do."Yeah, I know. But they will probably ask you, "How close are we to that?" Okay, so we have the from San, we understand as there if model operator, something valuable standalone or it's only valuable as part of it being graded. My agent participates. You could use the... The point of the model breaker is...Okay. You've got a system, a combination of actors and critics, doers and feedback nodes. You know, ships and lighthouses, if you like, and we quite like that. You can have lighthouses for "Don't do this."If you were representing it in 2D space, a guardrail is a wall, a barrier. A "Don't do this" is a lighthouse, and a "Do this" is an albatross. Whatever people see towards North Star, I like the idea of a constellation in general.It's good. You can sort of imagine you need to get from here to here. But in order to get from here to here, there's a whole bunch of obstacles that you can't see or understand, and it's complicated.So you put lighthouses on the things not to do, and then you put guiding stars to be like, "Yes, this is correct." Long your journey, the model breaker can be like, "Cool. Okay, so you've got a test set of 100, and you've shown that your agent or your system can do in simulation this job, these 100 different situations." Let's generate loads of situations.I don't mean full brute force because it's too expensive, it's too many API calls. So it is a semi-adversarial system for generating realistic, hopefully realistic. They can be tweaked because it asks you, "This is this series of tests we're going to do. Does this feel like a good series of tests?" Yes, great.Then I'll go in and try to generate the context and input situation and then run it. It's a way of working out how good your system is. When does it work? When does it not work without having to actually use it in the field yet? So it's a good value for the company, but it's not necessarily by itself a final enterprise.Okay, sorry. No, not to myself. Don't worry too much about it. Then the last one is the agent factory. Agent factory. Are we defining agent factory as a way to rapidly create agents that can create tasks, or as a way for us to create multiple agents or tools that will do the same task, and then just deciding which is the final agent we're going to go with?Yeah, it's if your evaluation is the part that says, "Is this right or not?" Your agent factory is the part that makes all the ships, all the boats, all the instruments, which tools to use when.So, generating prompts and instructions and workflows and stuff. The agent factory is going to work by me building... Well, it started by me building 100 ships, and probably 90 of them are sine, and the other 10 that are good are really good.I think that the way Aaron currently wants to do it has done it in McKinsey. You can see some of that is...I think it's just like you have a team, and the team's job is to complete its tasks with the tools it has. The team is supposed to think a lot. So they have the ability to do some EVEL to a degree because you have... What age would be like? I'm not sure if that's right.Maybe let's do it again, or maybe it's like this. I maybe it's starting that, and you tweak the settings so that it ends up achieving its goal, which is great as long as that team is doing one thing, and you don't have another team managing that team because then you'll get massive error propagation, just like the tower cards.So how about this is a demo? It's focused on extraction, so we use, as an example, capturing the screen. I think it's a thing that is surprisingly easy to do given the tech already got. I've already got like quite a lot of recordings of myself doing stuff, which, unfortunately, is probably a very bad example because the pattern's probably like starting a task. Now I do a bit of another task with everything. Write a bunch of notes.Maybe I could just find some parts of it, or I could try to find a bit where I've stuck to a task for a while and then wait. This is how I would actually do it. Okay, so what does the demo do? The demo can have the recording, but then it needs to show something representing all of this, like extraction logic and the many passes of adding layers and layers to the robustness of the specifications requirements thing that makes up the whole job.Then you have a big old list of agents that do all the tasks, and somehow the demo shows loads of agents trying those tasks.Then you see the ones that are going red. It's a little box that represents each one. And maybe some little numbers or something next to it, something to show it. Or maybe just a little... Just the last thought that the agent had just in small text below the box.That keeps changing as they're thinking. When they go, they have some little traffic lights next to them or just little scores. So some numbers that are coming from the scores and then maybe green dots.If I hover over one of those green dots, it comes up with its name, and if I click it in the sidebar, I get the metadata. What is that test? What is it evaluating? And just various layers.So, name a test. What's the one-line description of what it's trying to do? What's the human version of the writing? Then, what's the sub-requirements? If there's test cases attached to it, then there's a link to go look at them.But it's all just hidden in the sidebars how you expect, but then there's more and more detail in there. If one of those scores goes red or one of the lights goes red, the agent itself gets a little red X across on it to say that it's failed. Something's gone wrong.And then it has a thought. The thought can be in red. I mean, something's gone wrong. I've just received this feedback. Maybe the feedback is actually text that appears below the green dot just to be like, I don't say it's a summary of it, right?So it'll just be like a feedback colon, not like that. I noticed X, and I think that means you've got it wrong. I don't know, some minor text. This is going to be quite a lot of text snippets, but I think all these text snippets can be generated.I think I could use the last bit of this transcript, and once I've got the scaffolding. So if I get the diagram of roughly how this would look. So how would this look? It's like page one is just full screen, and it's just a recording of the full screen.It's just the recording of someone's screen for some really large amount of time. I don't know what we put there, but we put something. Some video. I can just put a day of screen recording for mine. Wait, there's quite a lot of private things, my work.Well, whatever I could do that. I could do that as a version one and then not get another one from somewhere else. I can relatively easily draw bounding boxes around all text.Yeah, I think the output of screenppe actually would... I could just put the actual output on the screenppe. I think if you scrub through the video, it will show the extractions alongside it.I think he can get an output, a timeline. I'll see where I can get your output. Anyway, and so that's your first view. It's just the extractor not doing any clever thinking. The second view is, I guess, a bunch of GPT threads. It's got some sort of... What is the... We've extracted a list of different workflows or tasks. Let's take one of them.So one task, well, three of them. I don't know, am I right? And present those, and they just need to lazy load. Then it needs to have some way of showing that lots of GPT threads are currently reading the document and reasoning over each part of each of the extractions. I don't know the best way to represent that, though.The thing I want to convey, really, is that if you can do something once automatically, you can do it thousands of times automatically. The fan out is crazy. The reason why we're so obsessed with this whole EVEL thing, this whole, like, button down the hatches, is so that you can have these large fan outs.The third page is the thing discussed before. So it's like, okay, execute in simulation, and execute simulation just means we aren't going to control an actual computer. We are going to just... This task is this individual task is I have a series of things I need to do with this PDF. This task is I need to read the emails.But we have to get rid of almost all the tasks because, as mentioned, most people's jobs involve really large numbers of them. If you could just go and provide the same scenario for multiple agents and then see how they react to the same scenario and then use the BALS to just kill them one by one. You only have to show the text once, then just go and you just start sending emails to each of them until you only have one or two left. Your issue is saying, "How do I demonstrate that?" I'm making multiple agents, and I have to have the best one left without showing, just large chunks of text. Is that the issue?What do you mean by issue? Sorry, like, I'm just like the... This is a demo where we're showing the extraction of somebody's work into a cloud of requirements, which are then turned into a swarm of Evas.Then, you and then a very hypothetical demonstration of what we use for which is feedback. That's correct, yeah. What do you mean by issue? Sorry, like, no, you're thinking on how he then at the last step, you can demonstrate how you use those Evas, correct?Like that's where we are stuck or not. So you're a bit quiet. I'm just going to look at the transcript briefly. The RA...Is this still going? Yes, it is. Hello, yeah, I'm just trying to see what you said. So your issue is saying, "How do I demonstrate that I'm making multiple agents and I have to have the best one left without showing just large chunks of text?" Yes, I see what you mean.Okay. So we are in agreement that the issue... We understand the issue, that's great. I wasn't talking about... We could do it that way, though. There could be one task, and then you could have multiple candidate agents attempt the same task.Yes, so we could have a slightly evolutionary approach to doing it. That's actually better because then you only need to shake one task rather than doing all of them.Well, there could be a list on the left-hand side. So, all the tasks could be in the third pane just as a list. When only one of them needs to be clickable, the others can just be blank. That one is the one showing the simulated environment for that one task.We can even use this specific task that we just got from this. What has it done? It's done. This is what it thinks your thing is. These are the requirements: roofing, domain knowledge, discrepancy detection.So, a bunch of this has ambiguity that needs further reasoning over. It's gone through the PDF again. Insurance claim document analysis, key information, roof measurements, claim line items. Dangerous. This is an incomplete extraction, so that's an immediate error.Well, unless this is all the information, I doubt it is. Right. There's no way. This is all of the information.No, I mean, there would need to be an ensemble here to just mail that. Project implementation time, phase one, system foundation, one for data collection, lodge-based development, for example, insurance claims from the various proprietors.So, I don't think I asked for some sort of project implementation plan. But for all these weeks, okay? I didn't ask for this, but never mind, but this I did. Requirements clarification. Any is good, clear requirements documents.By the way, the weeks are not too... I don't know, they are very far off. We have to deploy that in eight weeks. Sorry, I was laughing because she was giving me 16 weeks, but we have to deploy it in seven.Yeah, I mean, this thing seems to think that we're getting a team of developers to build. Yeah, I know. Yeah. And we're not going to... No. Every single thing can be done by an agent. Give me one second. Give me one...Okay, I'm back. What a very nice chat you have taken. Take a look at...Alex. I hold you very much dear, but it's 20 minutes to midnight here. I have to wake up at six am. I'm going to take our recording. I'm going to take actually the last 50 minutes. Which are the ones in which you have made the most sense. Which takes me to the thought that I need you. I need to exhaust you for like, hours and a half for your brain to go slightly slower so it gets closer to reality.And I'll use that to have my creative people at the company give me three ideas of AI now who's the audience of this demo? Alex, CO how technically savvy is the CEO and the people I'm hiring? Sorry. You said the CEO and people you are hiring. He ran the R&D department for the McKinsey AI department. Doesn't that tell me he's very tech savvy? But he's a tech, he's a technological... E-cells, but he's tech and he understands tech very well.Okay, so I don't have to make this. I don't have to dump this down too much. No, okay, I just... I just have to be able to show up in Cree vision, but it doesn't... It can... It has to be in front of the public. Think that will make this much easier? Yes.But the thing you should say to your guys is like, "Let's have an iterative process." Because if they draw something, if they... I think it should look like this. And here's some wireframes.They're making all of the animating... Like this many components would be extremely time-consuming. Whereas, building... Once we've got the components visually, just getting them to do things, without any intelligence behind it, is not very hard. Does that make sense?I can do cool stuff with AI, that's what I'm saying. Okay, yeah, I know exactly what you're saying. So, instead of saying, "Let's draw an image," just behind them with an eye to accelerate the process.So, don't throw them into 20-hour time sync unless you have to, but, yeah, it's good. Yeah, okay, thank you, sir. Please go to bed. It must be like 5 am where you are. Well, now I have to try and make quick animations from the transcript we just made and see if anything comes out of it, right? That would be lovely. Thank you, sir.Okay. Bye.
Almost. Okay, talk to me. I think you're supposed to talk to me about... Okay, Alex, the purpose of this call is you said that you have to go and show certain level of meaningful progress to your CEO. Otherwise, you are going to be in a shit position.

Yes, all the conversation is very good.
So how can I get you not to be in a shit position? Tell me some meaningful progress that you can show to human beings to justify your expenditures.

Yeah, good question. So I was hoping for a quick win with the Model Breaker project. I do have some wins there, in that it has happened, but not the full thing that I tried to pull off. It was doing a pretty good job, but the code wasn't quite ready in time.
But we still used Model Breaker. So, it's really just me feeling like there's a failure because I can still talk about it as a thing that happened, a thing that I made happen. Ownership is invisible. It's a bit strange.
If one doesn't work for you, people will quickly start assemble. That person did it. I'm like, cool, but why would anybody teach anyone something if credit internal setup is annoying, isn't working?
Okay, never mind. I can't see the bottom of my screen. I'm just going to drop my video.

Okay, so there's a bunch of different projects. We've got Model Breaker, we've got... I can pull the list in a second. We've got Promp Swarm, we've got the...
Yeah, it's like five different things that need summarizing. One second. Let me... Yeah, Model Breakers. What the fuck is this? I've never actually used it. You've never used Caldron or you've never seen me?
I've never actually used it, even though I know it's like, you know, develop a favorite way to talk. Here it is. Okay, if there is a Model Breaker you say the other one is the storm. Let me pull a list. Where is the Notion? It's probably the best place to access that list.

Let me change my sound settings because this is definitely not currently recording. Krisp, can you still hear me?

Yep.
Okay, cool.
So what have we got? We have prompt swarm agent factory, although I can't really call it that anymore. Agent foundations, housekeeper, recertified AE, auto-ceder, model breaker. Okay, LL MQALME valves.
But again, that's not really my thing anymore. I don't need to go into more detail, but it's fine. My role is to get things used by other people.
So, what have we got? Of agent constraints? My God. I have a folder called "Shit", Alex said in Slack. Love that idea.

Someone is playing a techno remix of the "Dude", you know the "Dude".
It's absolutely no clue what you're saying, but please carry on playing something out of Doom. You're "Dude" in the movie. Yes, I've tried not to watch it successfully so far. I don't worry. It's just... Someone's premixed the iconic Arabic sounding song from it. It sounds pretty epic.
Anyway, we can put this in the "Agent factory" and "Agent foundations". Why don't I just share some of these files with you? I don't think anything will happen.

Bruno? Da? What? A bell is one, actually.
You know what, let's just do things a little bit more carefully, shall we? Let me find a way to make things a bit more... and let's figure it out. No, I actually don't have an anonymous email, but the pack... I should get one.
I think about it.

By the way, what the fuck is up with someone trying to use invisible while using Bruno's name?
...[Laughter]... Well, it's not you, I... you name it. ...[Laughter]... There's not that many Brunos.
I mean, there's certainly Waylon, Bruno, and Alex. Okay, yeah, okay. If you want this, if you want to believe you have a unique name, I don't want to tell you otherwise. You know, you have a very unique name.
That's rich coming from me, but I'll take it. ...[Laughter]... You love that. ...[Laughter]...

We've got... I guess we'll then cool like eval slash spec-driven development.
Those are all the main projects. Let's ask... I think actually now has access to my notion. Please go through this on notion. Notion has sub-pages built with a list of major projects grouped by... Fukha, a new MD file.
Off you go. Email to Matt. Will be the other place that will be things. So, email to Matt. Wait, I'm going to send you this slightly old overview of the thing was ant I export.
So, I think as of tonight, I should be able to do this new thing. Let's see, in recast, I should be able to get the Agent Factory and Agent Foundations page from... Not an option. Interesting.

Fine, never mind. I'll do that. What can I do?
Okay, what's a way I can send things to you on my computer? That is what's up. Yeah, that I can control via agent. That means you can control your agent? No, that I can control by agent. I don't know, give me ideas. What would you like me to create?
