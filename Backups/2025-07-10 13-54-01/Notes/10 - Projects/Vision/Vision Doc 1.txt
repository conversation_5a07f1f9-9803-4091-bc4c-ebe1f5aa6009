# Vision Doc 1
Map, Territory, Diagram.  Diagram is the second Half of make no little plans. <PERSON><PERSON><PERSON><PERSON><PERSON> → make maps, not opinions.


+ There were two version. Is this the version we liked and where is the other one?
+ Get the diagrams from ChatGPT for the Slides, link here.
+ <PERSON> → who's the best person in your team to get thinking about how we could position this new tech I'm now really confident we're going to get working?
> What has changed since this version above is that I've started leaning much more heavily on the argument that [getting 70% of the way there (in a success scenario) is plenty to have confidence "this task can be automated". While getting to production grade (say 95% accuracy*) might be a tonne of work, if our system can reliably get to 70% by itself, that's enough to run it on thousands of tasks and give
> A) report on what can be automated now vs next year vs whatever → analysis something something.
> B) impact. "70% working" may well look surprisingly close to done without digging in. The scale of how many tasks this was done for... impact.
> C) Sales. Unique access to a big list of tasks and sub tasks, rich data for further QnA behind each.
> ... if we go with Apollo's idea of the spin-out... could you raise funds for a PE fund? Once we can prove it? Fund financials are excellent, no one else is doing "push for maximum automation using the degree of force that only an acquiring PE fund + <PERSON><PERSON><PERSON>y is really capable of doing". But we have to kind of decide, because if I put the schematics on their VM, they'll have it.
> 70% → reframe as "the secret sauce: the 70% threshold".  x% = x% human parity in quality.
> The value of **THE ONER** . Vibe coding and even engineers iterating ad lib for speed to get to a solution is, I estimate, something like 100x less valuable a skill than Spec Driven Development. See below. This philosophy also applies to Task Automation. We could write a series of white papers over 3-6 months on this if we wanted to drive adoption within the org. Why? A spec that, from which, agents (+graders etc) can 
> Have cats explain it again. Program your own 'as if cats' using the prompt in the code.
> Re Process Mining: "that new sr guy, ex UI Path → yeah it never works, we just had it as a foot-in." → this is a credibility problem. The Proxy (Map|Territory?) should get us in though. Surprising detail and scale _before_ we get in.
+ Steve = expanding Apollo + helping me find the A* engineers in our network. There are many but time + will. Automation Guilds strategy was his idea. None of my strong recc. hires haven't been excellent. Will then need him to find the rare engineers if this grows quickly and we have to scale it.
+ If goes well. Be baller. OK. Serious question Matt. Financially, why shouldn't I go somewhere else? I'm pretty nervous asking this but it's just progressively bugging me more and more. My AGI timelines are short. I know what chaos is coming in the run up and I have my chosen family that I want to ensure are protected if/when that chaos comes. I spent my UHNW money recovering for 1-2 years after a personal tragedy in 2023. I was offered $200k pa in January and I turned it down when you offered the team and head of AI R&D....  Honest question: would you have joined Invisible if we had had circa zero AI tech? Because, and please don't share that I said this as I don't know who's narrative it might not fit with, but there's at least a dozen people who would I think happily admit that that's what you would have found if I hadn't taken on the ridiculously ballsy mission of pushing AI into the org. There was literally nada when I got here despite being told, and many people at all levels believing we had things we didn't. It was crazy challenging, and I had to do it without taking ownership of it.... Now I'm doing all this.... My bonus totals like $15k on $130k base. It just got boosted to $150... I have a *lot* of rough edges. But I think I do genuinely have a very rare knack for finding winning long-odds bets that I can just about pull off, then just about pulling them off.... I don't genuinely feel like real value /impact is what will lead to better comp. I can't not do the most important thing I can think of, it's a compulsion.
	+ Paid same as Will. Set some milestones, reduce it back if I miss them. (If up later, no, it's overdue.)
	+ I need a small exec team. Associate: One Oxbridge grad w 2 years somewhere strong + extremely high dopamine. I really struggle without this, losing 30-50% efficiency.
	+ Exec Prerogative
		+ **Will.** We could have built this in April when Will joined. He strongly felt that we needed to first bring in $$ or we weren't safe. OK so I took us into Apollo. Perfect recon. Will needs to know this is what he should do to be safe. For him, safety is what makes him want to take risks, he's not ADHD, and he's brilliant. Ambition is secondary.
		+ Domac & Marshall etc → I want to put in a rule. No LLM / agentic get's delivered that we didn't automatically create.
		+ Aaron → he'll happily take whatever is useful to him and adapt / improve, I know. Want to know I'm safe to just hand things over and you know we're behind there somewhere. If I take things straight to him, I'm not going to get credit the same way. I think you now have enough evidence to just trust me going forward. I'll keep finding more silver bullets, and this plan has a bunch more to it planned around FM advances as is. 
		+ **R&D Spec-Driven-LLM-Dev only**. David M and Domac are both supporters [check] of the idea to make a wild rule: No deliverable work where each package (module) wasn't a 'oner' - i.e., built in one go by agents following a spec. This skill is SO much more valuable 
	+ Steve → see above.
	+ 3 A* engineers.
	+ David Domac → need to pay him $120k. Insane he's on $70, was promised and we'll lose him eventually.
	+ Agentry Automation Guild Challenge to find more diamonds.
	+ [Wagemaker]([[Vision Doc 1#WAGEMAKER]]) → was made redundant whilst I was trying to poach him. Wasn't delivering enough but [notes on him delivering]... I'm very confident he'll deliver for me, concerned there's some political BS going on that I don't want to wade into / drag his name through the dirt for. We're pretty isolated and I'd have him focused just on our team.
	+ Team isolation. The constant distractions / being asked to do all kinds of random pieces doesn't work. Kills speed by >70%. Urgency just wins over 'hard important thing with minimal positive feedback until nailed it'. Not zero external work, but anchoring to say no. Steve & Wagemaker to protect.
	+ Revi → $1500 a month. Help closing partnership. They can adapt their agentic research machine for the proxy of **Territory**.
	+ **Britton** I've** spent $3k personally on software experiments I didn't have time to get approved. Special permission for R&D.... Also, this is going to spend a lot of tokens. Like, a huge number. Will need some Foundry/similar GPUs.
	+ Andrew Hicks + John Zhu + Ernest. Heavily Split Attention.
	+ David Melville → best engineer for this in the org. Paired with Will (focused), Domac and [JZ&Ernst hopefully] we can crush this.
	+ Should we pitch Alexei/Aaron/Kit to collaborate, and how without sharp elbows. **Do not want** that. Long story but violence and lies ruined a whole chunk of my life. Want to build. Team + tech + strategy.
	+ Junaid + Fine Tuning. Current plan doesn't hold technical water. But we can apply it here.
	+ Data Quality have been pouring through examples since Prompt Swarm in Feb → want them testing this with KPIs to hold accountable. **Need  many more real enterprise examples from SE/Sales. Ask them to get them. Not 'the hardest thing that org can't do' but 'everything they spend a lot of non-comms human hours on.**
	+ Caspar, Marek, Tom down to sell. Jenny will get it.

### Karam 3
+ Get 'Foundations' from Achint and Karam as that was great. 
Instruction →    ←  FeedbackMost people's agentic loops are pure chaos
![image](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f4a1.png)
Record and datamine a human going from A→F on something valuable
Figure out how to get from A → F, automatically*, expensively
Re-run that a load of times and generate more and more feedback, automatically*, to reliably get A→F
Then reverse engineer [A→B, B→C, C→ D, D→E, E→F], automatically*
and use all those feedback signal and test cases to optimise the crap out of it and make it affordable automatically.
So long as A→F was anything approaching what we could describe as 'a human job' → boom. >100 thousand * that salary per annum in software value potential.What I **don't** get is why so few people are managing to achieve this. Bar complexity / whatever reason I keep finding myself re-doing the basics again beyond not having enough consolidated inspiration to hire my A team yet.I'm guessing we could actually pad the above out together, and you guys take whatever part of the dev makes sense.
We'll tee up with the agent factory team and get their latest code to make sure we'll end up compatible
but the POC just needs to demonstrate a system self-improving via building it's own self-improvement mechanisms.Simple, elegant, demonstrative.RL is a challenging to visualise algorithm, but the concept *isn't.*
threaded( loop(actor-instruction (prompts1), critic-feedback(prompts2), go astray, stop, reflect → rewind with slightly edited prompts or restart)
  ↳ multi-thread-reflect(scores, qual-review) → new strategy branches → new threads per branch
  ↳ reflect & prune branches to limit threads*as automatically as possible (edited)


# The Requirement Factory: Automating Task Discovery and Scaffold Implementation at (Serious) Scale
The goal was an enterprise pivot. Leveraging Alexei & Aaron, not duplicating. Concept _was_ Agent Factory, but actually Aaron and Will & my ideas didn't overlap that much. He's building the product.
## What We're Building: The ~Requirement~ Factory 
### Map. Territory. Contours. Scaffold. 
### Kernels → Oners → Gradient → Ascent ^8xbd1v
Map - not a compass. Compass points to north. 1000's of tasks, <two dozen months to adapt.
Terrotitry

> Draft to David (incomplete)
> What do you think of this angle?
> 
> We've discovered America. Invest in ships, ship factories or maps? Maps? Critically valuable, very scalable, but fungible and low willingness to pay
> 
> (1) Map: Knowing is worth much more than Doing per unit effort
> Efficiency from automation is of course very valuable. 1000's of
> But that's a lot of work. Even if 20% of white collar tasks could be automated up to 70% human parity quality (allow gross over-simplification for explanatory purposes)...
> Assuming 'production-usable' is 95% and thousands of task/subtasks + domain knowledge learning... that's a colossal body of work even just for one org.
> 
> 
> (2) Territory: 'The Map' is a massive competitive advantage.
> 
> (3) Contours:
> Scaffold
> 
> Fund
> 
> sketch a diagram of our proposed system (use notes sent to David) and then build in Pi scorers naively. Share these and state they are naive o3 guesses, the point is as an intuition pump. How should we do this? And how? I want us to avoid our previous pitfalls in meetings and keep things grounded in demos. Less 'what could be possible' and more 'how could we pull value from this right now without huge human feedback requirement?'









Domain Knowledge Extractor isn't just another automation tool - it's a methodology that answers the question every C-suite executive should be asking: "Which tasks that I'm paying people to do can be automated with today's technology, and how?"
Instead of waiting for clients to come to us with their automation ideas (which may or may not be feasible), we flip the script. We go in and systematically discover which tasks *should* be automated, break them down into subtasks, and provide a roadmap for implementation.

## The Problem We Solve
The automation industry has a dirty secret: the feedback cycle kills most projects before they deliver value.
```
Traditional Automation Timeline:
Day 1: Initial task description from client
Day 3: Client responds to clarification questions  
Day 7: First prototype delivered
Day 11: Client provides feedback (4 days delay)
Day 18: Second iteration
Day 25: More feedback (another week lost)
...
Multiply by 100 tasks = Project death
```
Scale this across 100 tasks in an enterprise, and you're looking at:
- 6+ months of discovery
- Incomplete data that misses critical edge cases
- Exhausted stakeholders who've lost interest
- Zero actual automation delivered
The core issue: people don't know how they do their jobs until they're doing them. Ask someone to describe their workflow, and you'll get maybe 60% accuracy. The remaining 40% - the edge cases, exceptions, and implicit knowledge - that's where automation fails.

## Our Solution: 
Generate, Don't ExtractWe've learned from the foundation model playbook. OpenAI, Meta, and others don't collect millions of examples - they collect thousands and synthetically extend them. We do the same for task automation.
Domain Knowledge Extractor works in two phases:
**Phase 1: Discovery**
- Deploy lightweight observation tools to capture how work actually happens
- Interview key personnel while they're performing tasks
- Generate comprehensive task maps showing:
  	- Core workflows
	- Decision points
	- Edge cases and exceptions
	- Data dependencies
	- Human judgment requirements
**Phase 2: Validation Through Documents**Instead of asking busy employees to spend 100 hours reviewing test cases (for work that might eliminate their jobs), we:
- Create simple, one-page requirement documents
- Use "if this, then that" declarative logic
- Present scenarios in familiar document format
- Get rapid feedback through document review, not testing
This approach compresses months of discovery into weeks, with higher quality output.

## The Bigger Vision: Requirements Factory
Domain Knowledge Extractor is the entry point to something larger. Over the past year, we've built complementary systems that now form a complete automation pipeline:
```
graph TD    A[Domain Knowledge Extractor] -->|Task Requirements| B[Prompt Swarm]    B -->|Generated Implementations| C[Eval Constellations]    C -->|Performance Metrics| D[Self-Optimizing Prompt Engineering]    D -->|Optimized Models| E[Production Automation]    E -->|Feedback| A
```
**Components Already Built:**
- **Automated Prompt Engineer**: Generates optimal prompts for discovered tasks
- **Prompt Swarm**: Creates diverse implementation approaches
- **Eval Constellations**: Comprehensive testing without human bottlenecks
- **Self-Optimizing Prompt Engineering**: Continuous improvement based on real-world performance (upgrading it even further to reward learning is what Andrew Hicks and David Karam will do for us)

## Why This Works Now
The basic feedback loop in automation is simple:
```
Human describes task → System attempts automation → Human provides feedback → Repeat
```
This loop fails because:
- Humans can't fully articulate their knowledge
- Feedback requires too much time/effort
- Edge cases emerge only in production
- Scale compounds all these problems
Our approach succeeds because:
- Generate comprehensive task understanding upfront
- Validate through efficient document review
- Use AI to simulate edge cases before production
- Create self-improving systems through automated feedback loops

## The Business CaseFor clients:
- **Discovery as a product**: Valuable reconnaissance even without automation
- **Clear ROI projections**: Know what can be automated before investing
- **Reduced risk**: Test automation feasibility before committing resources
For Invisible:
- **Premium positioning**: We find the opportunities, not just execute requests
- **Competitive moat**: Rich task data that improves our automation capabilities
- **Scalable growth**: Each client enriches our task understanding across industries

## The Feedback Factory: 
What Makes This All WorkThe thing missing is the detail around what I've been cooking on for 18 months: the feedback factory. Prompt swarm automatically makes reliable graders based on requirements. Generates NL test case strategy docs (1-2 pages of intelligently grouped "if this → that" assertions) for them automatically, then optimizes the grading prompt so that it fits. I gave it a silly name on purpose but this is very powerful. Originally it required human feedback on the outputs, but we also automated that pretty well…
This is so valuable because a grader factory unlocks a new paradigm of automation. Requirements become intelligent graders, not just that score but that give feedback.
1. Observe + decomposition → Extract hundreds of tasks per job.
2. Enrich via automated confidence-driven hierarchical QnA → infer requirements (dozens per task) and synthetic eval suites. (>100 test cases per requirement)
3. Prompt swarm → Generate graders from requirements. Auto-prompt optimize them to fit their test suites.
4. Automatically iterate prompts (agentic teams) to achieve their requirements. The graders (eval constellations) provide the reward signal, and qualitative feedback.
5. Human feedback is only required to update or add to the test case strategy docs.
Real jobs have hundreds of tasks involved. Enterprises have hundreds of jobs and need north stars to keep everyone sailing in the same direction. But teams need KPIs. It's often cloudy, it's often sunny, and the guys below deck can't even see the sky.
At the base of the pyramid, sure, the individuals need instructions.
The status quo approach to automation seems to imagine that we can train an entire navy's combined skillset, from scratch, by asking the handful of available scribes (who by the way haven't even worked as a deck hand) to manually write not just the job descriptions but the entire training manual. For every role, and every task. No experienced managers, no graduate program.
Insert fan out demo of task explosion for a navy:
- List every role in a navy
- For each role, list every skill and task required
- For each task, list requirements (correct = and incorrect = and "done well" =)
- Imagine you are writing idiot proof instructions for the worlds smartest 5 year old to be able to carry out the task better than a professional on the first try. Do this for every task and requirement.
AI drastically reduces the complexity however. They do not get tired, their cognition is ludicrously cheap, they don't secretly just want power, food and sex, they can communicate at the speed of light, and most importantly they don't require years of training / puberty as they can be copy and pasted.
If we can automate the feedback factory that steers one task to harbour, without cheating, we can do it for thousands.
Goal = destination
Requirements = north stars
Graders = KPIs/feedback
"X% of all work has already become an addressable market"

## Next Steps
Deploy Domain Knowledge Extractor with 2-3 pilot clients to:
- Validate the discovery methodology
- Build initial task corpus
- Demonstrate value of comprehensive task mapping
- Create case studies for broader rollout
The future of work isn't about replacing humans - it's about understanding what humans do so well that we can augment them effectively. Domain Knowledge Extractor is how we build that understanding at scale.


## DKE - features backlog
- [ ]   Ask David: using confidence ensembles, and impact assessment (on narrative, on p(success)), we can surface certain questions live to guild user. Notification, or just pop over text that fades away. They should speak the answer. Microphone reliability required. This allows us to gather exceptionally rich insight -IN THE MOMENT- which is very, very important for getting the reasons behind things. This can also train them to COT the useful stuff whilst demonstrating /working..
- [ ] Redacting will be a thing, not sure how much it matters though.


---
# A quick attempt at a visualisation for driving home the scale point.
# Task Explosion Visualization
## The Perception vs Reality Gap
### How We Think Jobs Work (4 Slices)
```
┌─────────────────────────────────┐
│         Sandra's Job            │
├─────────────────────────────────┤
│  Analysis (45%)  │              │
│                  │  Meetings    │
│                  │    (30%)     │
├──────────────────┼──────────────┤
│   Reports (20%)  │ Admin (5%)   │
└─────────────────────────────────┘
```

### How Jobs Actually Work (200+ Tasks)
```
┌─────────────────────────────────┐
│         Sandra's Job            │
├─────────────────────────────────┤
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
│││││││││││││││││││││││││││││││││││
└─────────────────────────────────┘
Each line = a different micro-task
```

## The Engine Diagram
```
graph TD    subgraph "Input: Perceived Simplicity"        A[Job Description<br/>4-5 Major Categories]    end        subgraph "The Discovery Engine"        B[Observe & Decompose]        C[Task Extraction<br/>100s of micro-tasks]        D[Sequence Mapping<br/>Order matters!]        E[Quality Thresholds<br/>Good enough to proceed?]    end        subgraph "Output: Actual Complexity"        F[Task Map<br/>200+ interconnected pieces]        G[Requirements per Task<br/>Dozens each]        H[Test Cases<br/>100+ per requirement]    end        A --> B    B --> C    C --> D    D --> E    E --> F    F --> G    G --> H        style A fill:#f9f,stroke:#333,stroke-width:2px    style F fill:#9f9,stroke:#333,stroke-width:2px    style G fill:#9f9,stroke:#333,stroke-width:2px    style H fill:#9f9,stroke:#333,stroke-width:2px
```

## The Spectrum ProblemMarshall describes tasks as a spectrum where:
- Tasks aren't discrete categories but blended activities
- "Two reds and a blue, then three pinks" - you're constantly shifting between task types
- Sequential dependencies: Task A at 70% quality might block Task B entirely
- The order matters as much as the execution
```
Morning workflow reality:
[Email-Red] → [Analysis-Blue] → [Email-Red] → [Meeting-Pink] → [Analysis-Blue] → [Report-Pink] → [Email-Red]

Not:
[All Emails] → [All Analysis] → [All Meetings] → [All Reports]
```

## Tasks Support Projects Support Goals
```
graph BT    subgraph "Micro Level"        T1[Task 1: Check inventory]        T2[Task 2: Update spreadsheet]        T3[Task 3: Send notification]        T4[Task 4: Review threshold]    end        subgraph "Project Level"        P1[Project: Inventory Management]    end        subgraph "Goal Level"        G1[Goal: Optimize Supply Chain]    end        T1 --> P1    T2 --> P1    T3 --> P1    T4 --> P1    P1 --> G1        style T1 fill:#ffd,stroke:#333,stroke-width:1px    style T2 fill:#ffd,stroke:#333,stroke-width:1px    style T3 fill:#ffd,stroke:#333,stroke-width:1px    style T4 fill:#ffd,stroke:#333,stroke-width:1px
```

## The Bridgewater ParallelJust as Bridgewater shows asset classes that appear simple but contain massive complexity when decomposed, jobs have the same property:
**Surface Level**: "I manage investments"
**One Level Down**: "I analyze markets, meet with clients, prepare reports"
**Reality**: 200+ specific tasks like "Check Bloomberg for EUR/USD at 9:15am", "Update position sizing spreadsheet cell F47", "Email Jim about Tuesday's deviation", etc.
The key insight: **You can't automate what you can't see, and you can't see the real work until you decompose it to its atomic level.**


We can get pretty far with "good enough" automation.


Seventy percent automation can have a big impact.


This opens up sales opportunities.


We find tasks ripe for automation.


We break them into smaller pieces.


Then, we plan how to automate them.


The old way of finding automation tasks is slow.


It takes forever to get feedback.


Projects often die before they finish.


People don't always know how they do their jobs.


Important details get missed.


Our solution learns like big AI models.


We generate insights, not just collect them.


First, we watch how people work.


We interview them while they work.


We create maps of their tasks.


These maps show everything.


Then, we validate with documents.


These are simple, one-page documents.


They use "if this, then that" logic.


People review familiar documents quickly.


This makes discovery much faster.


We also get better quality results.


This is part of a larger system.


It's a complete automation pipeline.


We have automated prompt generation.


We create different automation approaches.


We test thoroughly without human effort.


Our system constantly improves itself.


The old feedback loop is broken.


People can't fully explain their work.
Image Generation: enabled.

We can get pretty far with "good enough" automation.


Seventy percent automation can have a big impact.


This opens up sales opportunities.


We find tasks ripe for automation.


We break them into smaller pieces.


Then, we plan how to automate them.


The old way of finding automation tasks is slow.


It takes forever to get feedback.


Projects often die before they finish.


People don't always know how they do their jobs.


Important details get missed.


Our solution learns like big AI models.


We generate insights, not just collect them.


First, we watch how people work.


We interview them while they work.


We create maps of their tasks.


These maps show everything.


Then, we validate with documents.


These are simple, one-page documents.


They use "if this, then that" logic.


People review familiar documents quickly.


This makes discovery much faster.


We also get better quality results.


This is part of a larger system.


It's a complete automation pipeline.


We have automated prompt generation.


We create different automation approaches.


We test thoroughly without human effort.


Our system constantly improves itself.


The old feedback loop is broken.


People can't fully explain their work.



## WAGEMAKER …
And am I on track, right? Up until being laid off, there were conversations about me moving into the VP role, doing this, doing that. That's not changed ever. There was a brief conversation around where does Matt sit, where does Victor sit in the DeepMind Google role, how do we pass things are. The only thing I can think of that has probably pissed someone off (being blunt right) is the GeoPay thing because that was they were pissing about about that excuse the language for about six to seven months and people were continuously asking including myself. 


All hands before the announcement of the new pace, late May, early late May. All hands, I know I push a bit too hard, and I know Saoirse got quite annoyed with me. Yeah, yeah, I remember that. 

What was it you had? Yeah, I just turned on my little voice to text, so you had like 3 words. You had 1500 agents, not 1500, um, 2 million revenue. Yeah, we did a couple of million revenue. We went and we scaled a gen take for DeepMind. Did more scoping for DeepMind. Went in person in San Francisco with the PoCs that I kept warm. No one else on DeepMind expanded that as well and then grew those relationships. And then beyond that, we were growing the scope for Boar Crow and Phoenix which were the three projects that we have. Simon's got three more and he asked me if we could take them on, and I said that we could. At no stage did I push back, and essentially, particularly Phoenix which is the agentic stuff, um, we brought back to life or I brought back to life. No one else excuse me, and that could be a couple of mil a month if it was to scale to the full extent. I brought in David to look at automation, I brought in the resources, Trevor, Uli, Theresa, Mayor, and all of them have reported good management. All of them have been in my DMs, and all of them have my back around being managed appropriately, being grown, being coached. We're talking a couple of mil in revenue, PoCs that are happy, expansion on what we're doing, introduction to new parts of DeepMind management that's happy, trainer expansion, working with training. Yeah, I was a bit hard on hiring, I will be honest because the scopes that were being set for European hiring roles were not appropriate, but beyond that. 

So let me think that because I've lost access to everything. I would say $2M, but you know who would have the accurate number at this stage? Trevor Bryant. He would have the most accurate number. But I mean, the most accurate is less is less is less. 

## Madness …
OK → slightly baller play that needs a litle finessing. We stoke Apollo’s idea of the spin-out. We approach Matt. When we inevitably don’t get that much backing, Marshall and Francis would probably really quite like the idea of spinning out a research unit focused on Private Equity automation as JV with Apollo. We could even pitch it BECOMING an experimental fund. We could raise for it. Marek and I also know LPs. It’s a very cool fund concept and could pull some heavy hitting tech along with the idea…. we avoid deal closing just because we don’t want to have a PE partner working with us. We approach loads of PE partners and have them do the deals, and we co-invest. Keep Matt F as involved as possible ofc. Find the right top-tier boutique to be the execution partner.

I don’t think I need to spell out how the comp works if you set up a PE fund.

Wanna start a PE fund? ECARX -style. We do the tech, raise LP capital to co-invest but let other funds compete for us to bring the deals (we have a pre-deep-scan method for discovery, partner with Revi.ai for discovery execution). Get’s us out of not being “PE Partner material”.

### Email to Jason
**From:** Alex Foster <<EMAIL>>
**To:** Jason Mather <<EMAIL>>
**Date:** Monday, June 30 2025 at 12:17 PM EDT
**Subject:** Re Automatic Prompt Engineering

Worth noting that this is just the simplest version of the tech from several weeks back so it could work in Glean.

The full feedback loop agent is WIP.
 
One thing for us to touch base on soon: I've been looking for candidate 'big wins' and the below argument has been the biggest I've been sitting on for the last couple months. 

There's a really interesting question combining automated prompt engineering with another Invisible R&D project: the domain knowledge extractor that extracts workflows by watching people work (essentially agentic process mining but from direct observation vs logs):   

1. Hypothesis: A core driver of PE buy decisions next year will be 'how much we could drive efficiency via LLM orchestration automation'. 2026 will be the year enterprise agentic automation takes off in earnest. Integration will remain challenging and Private Equity will have a unique position to be able to force through large restructurings.
2. Fully automatically designing agentic systems to do entire people's jobs is likely still very hard to get working well enough for production >95% accuracy.
3. Let's say we can get it to automate many workflows to 70% reliably.
4. 70% accuracy might not be production-ready, but it does tell us with quite some confidence, which tasks/workflows _can_ be automated, as we have a first pass. If we can get to '70%' then we can have surprisingly high confidence that it's doable.
5. Doing this automatically and pairing it with systematic task discovery opens up what feels likeit could be a very powerful advantage. A viable answer to the question "how much of this company's operations could be automated?".

How might we go about gathering some feedback around this argument?
Any initial thoughts?
Best,

Alex

---
**From:** Jason Mather <<EMAIL>>
**To:** Alex Foster <<EMAIL>>
**Date:** Monday, June 30 2025 at 10:51 PM EDT
**Subject:** Re: [External] Re Automatic Prompt Engineering

Will read / respond 
 tomorrow. This is a horrible week for me as we have a large go-live next week (version 0.x of the mailbox routing thing), and we've still got a ton to do before then. And we're launching Cursor 
 tomorrow. And I'm dealing with an immigration for my husband 
 tomorrow. And the week ends 
 on Wednesday...
---
**From:** Jason Mather <<EMAIL>>
**To:** Alex Foster <<EMAIL>>
**Date:** Tuesday, July 1 2025 at 10:20 AM EDT
**Subject:** Re: [External] Re Automatic Prompt Engineering

To be clear, this is not around watching folks in our industry, but in other industries as we look to figure out how much efficiency we can add in different companies?

if it’s outside of our company, it’s not clear how we’d get access to observe people / processes?


### **DRAFT**
Exactly.

'Automation Guilds' is one concept I'm playing with.
1. Approach CEO/CFO: 'wouldn't you like to know how much of your core white collar operations are automatabletoday vs next year vs year after? [Very subtle & tactful insinuation of consequences of not knowing and guessing / doing 1-10 automation projects at a time when there's likely thousands of tasks to automate within 1-3 years / 12-36 months].
2. Automation Guild = the product. They literally offer a decent cash incentive for anyone taking part in the Guild, and make it selective. Best people get in + need for explicit verbal understanding of their work. Needs careful PR, but we only need one well designed example of how to execute it. 
3. Incentives:
	4. In Apollo's case I was assuming used as part of DD.
	5. Non-aggregated data doesn't leave their premises.
	6. Massive strategic advantage.
	7. Clear starting point for doing the automation, although the actual prompt systems wouldn't be shared except
8. Alternative: Focus groups of consultants. I started off here, it's how my AI market research startup I sold worked.
We can also proxy this 
 entirely I think via iterative research and our 
 QnA﻿-challenge  , fan-out theorising and systematically acquired feedback on the workflow spec files via e.g., ENS. It won't be anywhere near as good but could be a strong open for discovery.

Redact
+ Partner with Revi for the automation research piece.