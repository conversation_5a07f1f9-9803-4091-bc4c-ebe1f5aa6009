# Constraints for LLM Dev

This looks great. Limited options. Decorator. [GitHub - guidance-ai/guidance: A guidance language for controlling large language models.](https://github.com/guidance-ai/guidance)
- Library of pre-built components to restrict to / choose from.
- Example Notebooks look awesome aswell [guidance/notebooks/testing_lms.ipynb at main · guidance-ai/guidance · GitHub](https://github.com/guidance-ai/guidance/blob/main/notebooks/testing_lms.ipynb)
- 