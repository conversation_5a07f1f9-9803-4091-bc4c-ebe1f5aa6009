# NotePlan Learnings
* bullet as TODO caused issue with copy paste.
* Cmd J for command bar!
* Cmd L for make todo, 
* cmd D to Toggle task "Done"
* '+' is a checklist item, which can still be given a timebox allocation.
* Raycast agent improvements
	* Add our own tools to it as MCPs (Do this by default). [Try out MCP-ify](https://github.com/camel-ai/mcpify)[GitHub - camel-ai/mcpify: MCPify all the projects!](https://github.com/camel-ai/mcpify)
	* Is this even possible? Which of their code is available on GitHub? What can extensions do vs not do? Can the AI chat be edited?
		* Can I access the AI chat @ features via the accessing  of unofficial Raycast API, then pipe that into Excel UI or a chat bot?
	* Better tool-use, mid-chain-of-thought.
	* Add your own Tools

