# Revi - Agentic Discovery
30 Min Meeting between <PERSON> and <PERSON> - Jun 24th 25
**Event:**  ![📅](2025-06-24 19:00:::<EMAIL>:::NA:::30 Min Meeting between <PERSON> and <PERSON>:::#9FE1E7)
**Link:** 
**Attendees:** ✓ [<EMAIL>](mailto:<EMAIL>) (organizer), ✓ [<EMAIL>](mailto:<EMAIL>)
**Event Notes:** 
> What:
30 Min Meeting between <PERSON> and <PERSON>


[**Revi**](https://www.revi.ai)
<PERSON><PERSON> [<PERSON><PERSON>](https://app.krisp.ai/t/30-Min-Meeting-between-<PERSON>-<PERSON>-and-<PERSON>-<PERSON>--6a31f3115f624b6f9c368cad50d1eeb6)
<PERSON> [<PERSON>](https://claude.ai/chat/96a9c072-1b47-42f2-a196-0b6c4a98f220)

# [Revi](https://www.revi.ai/)
Minimal website. Found as mentioned by OpenAI.
**Company Details:**
- 10-12 people (aiming for 12, negotiating comp for 2 senior IC engineers)
- YCombinator, mix of seed and A atm
- Already at Series A revenue benchmarks but didn't need capital
- Will skip A potentially just due to revenue growth, next would be Series B
- Max is co-founder, UCL computer science alum
**Clients:**
- PE Funds: Thoma Bravo, Vista, KKR, Alpine, Apax, Searchlight
- Corporates: Motorola, Tencent (especially for tracking features)
- Partnerships: Deloitte + another consulting firm for transformation projects

## Discovery / Agentic Research Product
**Core Function:**Search companies in private markets, and track them after.
- Add-ons to create value
- Very thesis driven (e.g., "one eDiscovery business that does XYZ")
- Natural language query → reasoning model → search agent with tools
- Platform is extensible (can link internal data, slides, research)
**Process:**
1. Text search inwards: LLM on datasets of companies (constantly refreshed)
2. Search agent looks at each company 1-by-1
3. Profile using search agents (fine-tuned for ownership, revenue estimation, funding proxies)
4. Result: perfect market map
**Geographic Coverage:**
- World-class: North America (US focus) and Europe
- Also available: Australia, Middle East (not as comprehensive)
**Tracking Features:**
- Bloomberg-like interface is the goal
- Live feed of information afterwards
- Track signals: new clients, growth %, recent events
- PE funds use to track portfolio and market evolution

## Key Examples & Capabilities
**Emergency Power Manufacturer Search:**
- PE fund had done 3 expert calls ($1k each) to find 5 companies
- Revi found **900 companies** in US in that specific sector
- Surprised even PE fund with 20 years in industrials
**Data Depth:**
- Look beyond English articles
- Map out physician practices across US (computationally vs manual)
- PE funds benchmark by testing against their portfolio companies
- KKR/Blackstone portfolio companies submit data for market visibility
**Ideation we do manual, improvements come from computation**

## Product Details**Input Types:**
- Company name for deep profiling
- 'Niche market' for discovery
- Generally discovery focused, but "if you put Revi's agent vs 3 associates given 3 days, I bet we'd find more"
**Custom Data Points by Industry:**
- Medical practices: physician count, locations
- Software companies: notable clients, deal size, ACV
- Users can add custom columns dynamically via natural language
**Data Sources:**
- Primarily external/internet data
- Have own DB on every private company in US
- Can incorporate client internal data but not mandatory (most don't due to messy data)
- Not focused on internal data integration initially (slows sales cycle)

## Pricing & Business Model
**Volume-based credit system:**
- $1k/month = 1,500 credits
- 1 credit = 1 good fit company that exactly matches search criteria
- Credits cost 45-80 cents each (100 companies = $45-80)
- Discontinued seat-based pricing (found unintuitive, people rebelled)
- Unlimited seats, unlimited searches on platform
- Search size estimated after clarification process
**Partnership Options:**
- White label: Not ruled out but haven't thought about specifically
- Already doing implementation partnerships (Deloitte for transformation)
- Open to volume-based partnership structures

## Key Differentiators
- Finds companies others miss (many sectors not indexed in typical databases)
- Better than manual due to consistency across 100s of companies
- Synergistic with senior expertise (provides fertile ground for pattern recognition)
- Process: clarification questions → examples → size estimate → run search


ORIGINAL NOTES
---
Minimal website. Found as mentioned by OpenAi.
**Clients: **Apax searchlight Thoma Bravo, Vista, KKR, Alpine, Motorola TenCent
10-12 people, YCombinator, mix of seed and A atm. Will skip A potentially just due to revenue growth.

Discovery / agentic research product:
Search companies in private markets, and tag them after.
- Add ons to create value
- Very thesis driven
- One eDiscovery business
- Text

Search inwards: LLM at our datasets of companies
Search agent looks at each company 1-by-1
Profile using search agent.
Result: perfect market map.

Working with [something] and TenCent [check]
X y z - all of these are signal that we track.

Bloomberg-like interface is the goal.

Search + live feed of information afterwards.

Is this more about finding stuff faster (reactive) 

example: emergency power manufacturer.
- done 3 expert calls to find 5 companies
- We found 9 companies.
- Even though they **are** focused on industrials.

Chain to actually estimate (model) revenue for a private company.
Not going to look at articles not in English (one example)
If you were to map out xxx practices in US, would be very manual.
Ideation we do manual.

**Input = company name vs., 'niche market' **
- Generally discovery, yes. But if you put Revi's agent vs 3 associates given 3 days, I bet we'd find more than them.

Senior memory

**Internal data as well? Or Focused on Internet etc**
- have their own db run on every private company in the US, but largely no we don't do internal data.

Pricing is by volume. Credit system = finding companies and tracking them.
- $1k per month = 1500 creds. 1 cred = 1 good fit company that exactly matches search criteria.
- 

White label?
- Doing something like this for Deloitte for transformation.
- Not writing off but haven't thought about specifically.



Invitee Time Zone:
America/New_York

Who:
Max Nuyen - Organizer
<EMAIL>
Alex Foster
<EMAIL>

Where:
https://us06web.zoom.us/j/81374257335?pwd=mTPE0aEyp4iqmUNEGw936UXCPFz8m5.1

Your company:
Apollo/Invisible
  
Company type:
PE
  
Reason for the meeting:
Building out a PE AI transformation product with Major PE fund, there's easily >1k tasks on the table for automation, open to partnerships.
  

Need to reschedule or cancel? https://cal.com/booking/21jnqX4fVsjvRimzmvoFHp?changes=true
---

## Agenda
- 

## Meeting Minutes
- 
