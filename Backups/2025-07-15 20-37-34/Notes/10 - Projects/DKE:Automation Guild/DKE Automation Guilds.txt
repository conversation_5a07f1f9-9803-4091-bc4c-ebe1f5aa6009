# DKE Automation Guilds
DKE = Domain Knowledge Extraction.
[ChatGPT](https://chatgpt.com/c/685c3b3b-4018-8003-849f-e96f79429602)
 Transcript of <PERSON> Monologue at bottom.[[DKE Automation Guilds#Steve Monologue]]
> Getting to 100% reliable automated task is 10x harder than getting to 70% . We don't have to get 100% if the goal is signal. We could fully automate 100-1000 tasks in one go and use it for sales and streamlining. DKE is the perfect C-level foot-in, and an ideal platform to then sell from.
> LLMs have **very** much changed what's possible for process mining. Massively greater resolution and richness possible, as well as now being re-queryable.
    ##  **Exec TLDR**
- *Yes, it’s genuinely valuable:* Boards already pay eight-figure sums for consultants or process-mining suites that do what you’re proposing (discover task-level automation potential). Market signals—Celonis ($13 B valuation 2022)([celonis.com](https://www.celonis.com/press/crunchbase-celonis-raises-1billion/?utm_source=chatgpt.com)), UiPath ($29 B IPO 2021)([channele2e.com](https://www.channele2e.com/news/uipath-ipo-10-rpa-takeaways-from-s-1-sec-filing?utm_source=chatgpt.com)) and Automation Anywhere buying FortressIQ to add task-mining tech([techcrunch.com](https://techcrunch.com/2021/12/23/automation-anywhere-expands-into-process-discovery-with-fortressiq-acquisition/?utm_source=chatgpt.com))—show C-suites will spend heavily for “where should we automate?” insights.
- *Demand is accelerating:* Generative-AI has lifted McKinsey’s estimate of automatable work from 50 % to 60-70 % of human tasks([mckinsey.com](https://www.mckinsey.com/capabilities/quantumblack/our-insights/the-state-of-ai-in-2023-generative-AIs-breakout-year?utm_source=chatgpt.com)), and the process-mining market alone is forecast to 17× (≈ $2.5 B → $43 B 2024-32)([fortunebusinessinsights.com](https://www.fortunebusinessinsights.com/process-mining-software-market-104792?utm_source=chatgpt.com)).
- *Moats & gaps:* Incumbents focus on system logs or “process” granularity—few give **LLM-grade task-level** coverage of desktop work, so a privacy-safe, cross-app recorder plus LLM classification is still lightly defended white-space.
- *But you’ll need clear differentiation on (i) privacy/ethics, (ii) minimal-overhead agent deployment, and (iii) an actionable scoring model (“Bot-Score”) that drives budget decisions.*

# **Internal Memo v0.2**
**To:** CEO
**From:** Alex Foster, Lead R&D
**Date:** 25 June 2025
**Subject:** Building an Enterprise “Automation Feasibility Layer” (codename: **TaskScope**)
---

### 1. Executive TLDR
- **Boards are already spending eight-figure sums** for consultants or process-mining suites simply to learn *what* to automate. Celonis hit a $13.2 B valuation in 2022 on that promise. ([techcrunch.com](https://techcrunch.com/2022/10/16/with-a-13b-valuation-celonis-defies-current-startup-economics/?utm_source=chatgpt.com))
- **Generative-AI has moved the goalposts:** McKinsey now pegs 60-70 % of work tasks as technically automatable, up from ~50 %. ([mckinsey.com](https://www.mckinsey.com/capabilities/mckinsey-digital/our-insights/the-economic-potential-of-generative-ai-the-next-productivity-frontier?utm_source=chatgpt.com))
- **Hyper-automation budgets are expanding:** forecast $65 B market in 2025 at 17 % CAGR. ([thebusinessresearchcompany.com](https://www.thebusinessresearchcompany.com/report/hyperautomation-global-market-report?utm_source=chatgpt.com))
- Incumbents focus on *process-level* logs; **nobody delivers task-level, LLM-grade analysis with privacy-by-design.**
- By capturing just **one work-week** of real desktop activity from an opt-in “Automation Guild”, we can tell a CFO *exactly* which tasks deserve bots, which can’t be automated (yet), and how much hard cash sits on the table.
- Price at 1 – 2 % of identified savings ⇒ $0.5–3 M ACV per Global 2000 logo; < 200 logos yields > $0.5 B ARR, consistent with Celonis/UiPath comps.
---

### 2. Problem We Solve
| Current option | Pain for the customer | Gap we fill |
| -------------- | --------------------- | ----------- |
| Big-4 & McKinsey interviews ( $2–5 M/function ) | Months of workshops, mostly qualitative | Our recorder collects every click/keystroke/window in days |
| Process-mining suites (Celonis, Signavio) | Needs ERP/SAP logs; blind to Outlook, Slack, Excel “shadow IT” | Cross-app desktop capture + LLM semantic tagging |
| Task-mining bolt-ons (UiPath TM, AA + FortressIQ) | Heavy IT install; limited NLP; PC-only | Lightweight agent, cloud-or on-prem inference, multilingual |
Automation Anywhere bought FortressIQ (price undisclosed) because discovering tasks is becoming table-stakes. ([automationanywhere.com](https://www.automationanywhere.com/company/press-room/automation-anywhere-to-acquire-fortressiq-to-reimagine-intelligent-automation?utm_source=chatgpt.com)) We aim one layer deeper: *actionable feasibility scores* instead of raw screen recordings.
---

### 3. What We’re Proposing
| Component | Brief | Why it matters |
| --------- | ----- | -------------- |
| Tracker (“tractor”) agent | 100 % client-side capture of UI events, window titles, cursor paths. No screenshots stored; on-device embedding + hash to protect PII. | Survives privacy review; sub-2 % CPU load so employees forget it’s running. |
| LLM classifier | Prompt-tuned on a corpus of desktop interaction traces to label each task, map prerequisites/outputs, assign a Bot-Score (hours × wage × automation confidence). | CFO sees a ranked backlog, not a data swamp. |
| Automation Guild programme | Opt-in cohort of “power users” (<top 5% performers) paid a $3k stipend to run the tracker and narrate (chain-of-thought into mic) over edge-cases. | • Eases cultural push-back (“exclusive guild status”).• Supplies high-quality labelled data for continual fine-tuning. |
| Simulation & test-case generator | From recorded traces we auto-generate step-by-step scenarios and expected outputs. | Feeds agent-training pipelines and regression tests—means we can ship bots, not just recommend them. |
---

### 4. Business Model & Economics
| Item | Assumption | Outcome |
| ---- | ---------- | ------- |
| Addressable savings surfaced | 15 % of salaried hours in year 1 (conservative vs McKinsey 60-70 %) | >$30 M/year for a 10 k-FTE division |
| Pricing | 1 – 2 % of first-year savings + subscription for updates | $600 k – 1.2 M ACV |
| Gross margin | > 85 % (compute + onboarding guild payments) | SaaS economics, capital-light |
| Land-and-expand | Division pilot (4 weeks) → multi-function rollout → global license | Upsell path to $5 M+/logo |
---

### 5. Strategic Moats
1. **Proprietary task corpus** from Automation Guild recordings—difficult and expensive for rivals to replicate.
2. **Privacy-first architecture** (hashing, local redaction) turns a regulatory hurdle into a selling point, especially for EU clients.
3. **Closed-loop delivery:** same platform that diagnoses also emits test cases and agent specs ⇒ lower time-to-value than process-mining suites that stop at the PowerPoint.
---

### 6. Key Risks & Mitigations
| Risk | Impact | Mitigation |
| ---- | ------ | ---------- |
| Employee surveillance backlash | Adoption stalls | Opt-in, pay participants, transparent dashboard of captured data, delete raw logs after hashing. |
| Data heterogeneity/noise | Low Bot-Score accuracy | Start with curated guild; iterative fine-tunes; fallback heuristics for unseen apps. |
| Incumbent vendors replicate feature | Competitive pressure | File IP on semantic “Bot-Score” calculation; focus roadmap on simulation/test-case angle where they have no assets. |
---

### 7. Why Act Now
- Market validation: Celonis $13 B (2022) and UiPath $29 B IPO (2021) show executive appetite for automation diagnostics. ([techcrunch.com](https://techcrunch.com/2022/10/16/with-a-13b-valuation-celonis-defies-current-startup-economics/?utm_source=chatgpt.com), [iposcoop.com](https://www.iposcoop.com/the-ipo-buzz-upsized-uipath-in-play/?utm_source=chatgpt.com))
- Generative-AI lifted theoretical automation ceiling to 70 %—boards need a *map* to capture it. ([mckinsey.com](https://www.mckinsey.com/capabilities/mckinsey-digital/our-insights/the-economic-potential-of-generative-ai-the-next-productivity-frontier?utm_source=chatgpt.com))
- Hyper-automation spend still compounding at >17 % CAGR despite tech-valuation pull-backs. ([thebusinessresearchcompany.com](https://www.thebusinessresearchcompany.com/report/hyperautomation-global-market-report?utm_source=chatgpt.com))
- First-mover advantage on task-level, privacy-safe capture could position us as the “MRI scan” before any automation surgery.
---

### 8. Immediate Next Steps
1. **Green-light 6-week pilot** with a willing business unit (≤ 30 PCs).
2. Allocate **$150 k** budget: tracker hardening, Guild stipends, cloud costs, and risk counsel.
3. After pilot, present CFO with **Bot-Score heat-map** and ROI model; secure enterprise-wide roll-out commitment.
4. Begin patent filing on classifier + simulation pipeline (target filing Q3 2025).
---
Building this diagnostic layer positions us as the *source of truth* on what work can and cannot be automated—*before* the RPA, LLM agents, or consultants swoop in. The data, the trust, and the timing are all on our side.
### Bottom line
There’s proven, billion-dollar appetite for software that quantifies what work can be handed to bots. If you can offer **finer-grained insights faster and more ethically** than the current process-mining incumbents, CEOs will listen—and budget accordingly.







---
---
# APPENDIX / Rough Notes


## Steve Monologue
**Pitch**
Yeah says do tractor captures and tons and tons of detail about what somebody does now that could be an employee at the company paying us but it also could just be some consultants who do the jobs that that if you wanna get around some redacting issues whatever but to be honest, I think good with CEO and you like I can tell you What Will won’t be automated right now and like you can know what could be automated and all the tasks that need to be automated and the ones that can’t you don’t know we can tell you that’s extremely valuable information and then also from that we can sell but I think CEO being like I have to get that You I think they will just get they’ll just I’ll be like we just pay them pay them $3000 each if you like to call them for a year
Who he worked out that I was the person who could get him the information he needed the data from this place the thing that you could get consultant and very easily what do they do and basically reverse engine engineer into a training thing? Do you mean like once you if you’ve got a company that’s a sufficiently large amount of scanner employees has to be building the building and you paid everybody who took part so they were willing participants and they were informed that no one was going to use it in anyway that was going to affect their job.
Lady development do it all the time they take the best people and they try to work out how it is that they do what they do and they package that up into something something and then they use that to try and train other employees so it’s the same thing, but for agents you speak to Kieran about what he did with videos. I think ignore that just your job is train agent so the goal would be The agent as you get your system that is automatically building agent to achieve a task the things you need what are the requirements of that task? This is non-trivial and a lot of the reason why it’s hard to build things but then also you need guidance on intermediary steps and those intermediary steps often will also be unintuitive outside of the System And with the other thing that you can get out of it directly is test cases so you can in simulation training not in a real environment you can you can build run through the simulation based off of what you captured so you basically say you’re gonna be in this situation Mr Mr LM agent machine and you need to have this output at this time this kind of at this time and you just simply said did you do roughly that because you get there and if you have like 10 of them then you have 10 examples to not train against cause training requires thousands of examples but to test against Which means that you automatically building the system does the whole thing do you Gil and he is the guy for the Leads and we got the guild Guild maintain the standard of engineering organisation
Automation gills. So the way I was pitching this before was very much who is the function ID here’s how it could be useful which is classic engineering dumb you pitch the value and you say why you can do it already or you’re not that far from it that’s always the way to tell the story automation guild Directly you start off from the pitch which is you have this lump to get over later the biggest lump which is how do you get people to record themselves and automation guild is something you pitch two companies that you’re doing transformation with or you’re trying to use and the software that can tell you what can be automated and what Cunt is not what’s your pitching you’re pitching your automation guild which contains software and now you’re pricing at a human intervention price point rather than a I have some soft price point Your guild is the group of people that you pull in only given company who are the people who can do really well, but also that know how to verbalise how they do it then how to talk they have an explicit understanding rather than entirely understand what they do and you pay them well not us you you’re going to do it because logically You just you need them. Need your best people to do it and you just should pay them like they’re going to automate them you are going to automate them. You want to be either cutting them into it or paying them like quite handsomely but with the option to retract it maybe give them an equity in exchange for it and then the people who join the automation guild you have the trackers they’re almost certainly if you call it automation guild be a certain amount of cultural backlash against anybody doing it because of the automation of the jobs so solve that somehow but yeah maybe rephrase the Gil a little bit because the guild needs to be something that the other alternative coming from you need to know what time the automated can’t be automated Think in a second and then like really early on your pitch act effectively say how are you going to get people to be okay with doing this? Firstly keep it on site? Secondly you pay them? Is that simple? You absolutely can’t make it happen it if people literally get paid to do it in the opt in you’re not gonna have like you can’t have like this is unfair Because you’re paying people to just do their own job normally and you should pay them well for it and give them some reason to send before make a status thing make it cool only the best people and enhancement.  Workflow enhancement. How do you do your job? How do you automate parts of it away?