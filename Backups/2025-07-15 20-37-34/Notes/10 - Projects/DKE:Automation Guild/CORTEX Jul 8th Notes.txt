# CORTEX Jul 8th Notes
### Quick Tasks for <PERSON>
+ Feedback from <PERSON><PERSON>
	+ And Sorcha
	+ 
+ Glossary of terms and components
	+ Cross-link (hyperlink) all terms like a wiki.
	+ Auto-generate a map of the full markdown tree. Make sure 'back' works and that you don't easily get lost via a hierarchical sidebar and hyper-linked diagrams
	+ use the core diagrams as the central 'index'.
### Additional Asks
1. Fireworks Upgrade / Big Model Cheap Token solution
2. Permission to spend a LOT of tokens on experimentation
3. People to Record

### o3-pro instructions + text based OG diagram …
Each separate. They should agree with each other on the 'truth' but say it different ways. They don't each need to be exhaustive but none of them should leave any high-level detail out.

ORIGINAL DIAGRAM = 

IDEA (problem, toplevel, task, brief, example, self-generated or client-provided)

then

1 MasterPrompt / Instructions
2 ReqDoc (Sucess looks like, failure looks like, sub-requirements, two tier, human-readable first)
3 TC Doc / SimulatorRef / ScenarioDoc (IFTTT, declarative, single lines, human readable-first)

then

1 def prompt_feedback, rubric, size, requirements, ITERATIONMACHINE_A_PROMPT_FEEDBACK_ONLY (the idea of 'a good prompt)
2. Graders (fan-out, pass/fail or score+threshold for pass, + reason for pass/fail)
3. GenerateTestCases

then

ITERATIONMACHINE_B_OUTPUTFEEDBACK (per test case output, is the output in-line with what was declaratively expected?)

then ITERATIONMACHINE_C_GRADERFEEDBACK (per test case per grader - the quasi-reinforcement-learning that relies on progressively better graders to lead the system to awesomeness)

then

REPEAT (take all the logs, create [run_2] dir, ScenarioDoc2, ReqDoc2, MasterPrompt2 → o3 is going to re-write / edit those three initial docs or a human is going to do it, then the whole system re-runs again. ITERATIONMACHINE_D_WHOLESYSTEMFEEDBACK

Note that the DKE is missing from the original diagram, not-purposeful.

### next


## Opus Recommended Approach: 
Layered Complexity with Visual FlowI recommend combining strategies 1 and 5, with visual elements. 
Here's why:
**Strengths:**
- Accommodates different audiences (executives, engineers, partners)
- Preserves all technical detail without overwhelming newcomers
- Visual flow diagram acts as a "map" readers can reference
- Maintains the explanatory power while drastically improving accessibility
**Structure:**
1. **Executive Brief** (1 page): What PFC does, why it's revolutionary, business impact
2. **System Overview** (3-5 pages): Visual architecture diagram, component descriptions, workflow
3. **Technical Deep Dive**: Detailed documentation of each component with the rich details from the original
4. **Implementation Playbook**: How to deploy, automation guild setup, success metrics

### Avoiding Toe-Treading
For simplicity, these docs do not build-in what Kit/Aaron/Alexei's teams and products are doing within the system.
We roughly would guess something like:
1. AgentFactory → Aaron's team are building the platform the automation operates off of, the below only feeds into / enhances it. Once their agentic platform is ready, the machine is not drafting prompts, but **Fleets**. Fleets are just YAML, aka text. Prompts are text, Fleets are text. David Melville's original idea for how to synergise cleanly.
2. DKE / Automation Guilds → we prototype, Alexei / DataPlatform own it. This is Data + Sales.
2. RequirementsFactory → we prototype then own/co-own the continuous R&D for enhancing the feedback loops. Specifically talking about **the feedback system** here.

However, this is just a rough guess.
We're showing this to you (Matt) first (* recent check-in with Alexei and Aaron gave a 🟢 on the DKE and feedback systems in Feb) so that you are the one who orchestrates what happens next. What you want to happen before we take this to Kit/Alexei/Aaron etc.
* What do we need to validate before R&D commit to pursuing this strategy full force? Technically? Business Value -wise? 
* Or should we just run at this thing immediately?
* Or are we missing some crucial consideration, off-the-mark and need to backup, and rethink the whole thing?

### Cortex
We liked this placeholder name because:
* The other components are all intelligence/mind -themed.
* This system is –not– pre-training or post-training or fine tuning. It's not maths. It's out-loud, chain of thought. Tonnes of it.
* A rough model of how human's creatively problem solve:
	* (1) Semi-random ideas + slow pre-frontal-cortext evaluations on which ideas are good/bad and (crucially) **why**. Human teams talk to each other and very slowly, painfully and frustratingly, make progress.
	* (2) Then it get's easier, we sleep, we compress, we develop language short-hands for the problems, and our evaluations (graders) for how good our 'ideas of solutions' are get much faster, and sub-conscious, but often still with slow-reasoning as well, and often still with justifications.
	* (3) Then... it consolidates. We wake up one morning, we're in the shower, and our mind is doing the whole [generate ideas and evaluate them] process subconsciously. The solution, the idea of it as a concept and often even just a few sentences or words that abstract it, appear in our head as if by magic.
* The system we are proposing is analogous to (1). Entirely out-loud thinking+feedback. Just like the above model of the pre-frontal-cortex part of the above model.
* Hence: **Cortex**.
**Is it financially viable to run 'Cortex' at scale in operational production? i.e., to actually get each and every task –done–**
Likely not. It's probably very expensive for most tasks.
**So doesn't that mean this has no sellable value?**
No, because if we can get Cortex to work, it almost doesn't matter how expensive it is to run.
If we can get it working, even just to the '70%' mark for, say 1/3, of the core tasks an organisation's workforce has to execute, that's already extremely valuable.
1. The hypothetical CEO/CFO now has this, frankly, **crazy valuable** piece of intel.
2. The actual production execution can be done by a system doing >100x less 'thought'. Agentic Platform + human FDEs, external specialist constractors / specialist agent providers, our own pre/post-training solution for compressing the solutions generated by Cortex, and other runtime solutions such as dynamic few shot for automated continual self-improvement.
**But this sounds really complex and hard to sell...**
The foot in the door can just be the intel.
We don't need to convince them we can pull-off automating some large portion of it in the first meeting.
We just need to convince them how valuable this intel will be, and that they should **convince us** that they should be one of the first 10 orgs we set up Automation Guilds for.

### Micro Overview
Capture → Abstract → Generate → Test → Evaluate → Deploy → Improve
                          ↑                                    ↓
                          ←────────── Feedback Loop ──────────←
                         