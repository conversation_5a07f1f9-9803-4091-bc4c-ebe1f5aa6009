# mac_max_windows

+ Prevent 'putting off to the side' → have reboot to centre after 3mins... or add 3min snooze button for emergencies.
	+ Snooze button + "I've done it" button, re-runs check and re-appears if not true. Strict.
+ Remove top-level windows that aren't there. Cursor
+ Browser tabs
+ **Remove non-optional windows (zoom, finder, forklift, WisprFlow status, anything with *notification* Typora = 1 window?)**
+ Put by app / by window on first alert pane (not second)
	+ First alert has no info, pressing any key / enter takes to second alert which is the previous 'main window'. The primary alert.
	+ Ideally show list of apps to close and list of windows to close on that primary alert.
	+ Ideal world = app selector with (n) for how many windows for that app, and second col with all windows shown. then pressing left & 'right' key navs between them. For app with eg 3 windows, can repeat app name in first col but cleaner if it's just one cell and lines up with 3 cells in the second col.
+ Add multi-select (click to select, click to unselect)
+ have 'close app/window' button not close the alert / re-open it so we can run it a few times. Disappears once condition met.
	+ Add finish button. Dismiss probably fine as is.
+ Ensure keyboard shortcuts can be used to navigate. Up down left right, tab, and del / bckspc closes selected apps / windows.
+ Add 'do nothing' button and make it the default button that is highlighted. It does nothing (doesn't dismiss window either). May not be possible as click may = form submission. Have it re-open same alert? Should work.

+ just make the closer double click / opt click to close (requires a hover effect for precision feedback). Lowers cost of doing the exercise. Should remove from list instantly and delegate task to background so view doesn't change and user can close, close close fluidly.