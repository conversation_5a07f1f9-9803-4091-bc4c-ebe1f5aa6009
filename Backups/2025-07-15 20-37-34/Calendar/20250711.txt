Okay spend on this idea, using no plan for adding notes and ideas of Projects suspects and then having those automatically extracted and expand it into requirement documents or mathematics test cases et cetera pipeline system is stored in the same structure. The generator is generated and run for testing and relatively progress upon to move towards the requirements and the success criteria but then once achieved or failed, the summaries is written marked down with Python snippet, which I think is the only file which no plan will show if it is in the folder.

is it possible to run claude code as a background agent yet? are there any competitors? Devin is crap now right? info from last 1 month only. 

