# Code Architecture Kedro Pipeline?

Specifically kedro viz looks promising.
[Live demo here](https://kedro.org/#features)
Lo<PERSON>, it was made by QuantumBlack, thought I'd seen it before.
### Benefits
Very cool and useful-looking interactive visualiser.

### Cons
Have to define everything in the Kedro format... Could potentially make an adapter that just programmatically refactors our agentic structure into the Kedro format for the viz though... unclear on complexity.
- [ ] Research feasibility.

### Preview your datasets in the metadata panel
View a small preview of datasets by clicking on a node and opening the metadata panel. The ability to expand to view a larger preview is also possible when the panel is open. Along with a preview, there will also be information about your dataset, such as the file path and the dataset type.
[Learn more](https://docs.kedro.org/projects/kedro-viz/en/stable/preview_datasets.html)
BackNext