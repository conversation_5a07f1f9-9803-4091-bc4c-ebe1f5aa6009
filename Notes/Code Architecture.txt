# Code Architecture v0.1 Jul9th
+ Add Initial Cursor.rules based on <PERSON> and Cursor existing and the below.
+ Extension that reminds you to commit after X 
+ Main protection
+ Create 'COMPLAINER" that escalates PR's that aren't reviewed within 24 hours.
+ Even more aggressive Linting, ideally covering everything below.

OK let's work out the best code architecture for this.
Talk through options.
Non-negotiable:
- Pydantic (strict) + TypeHints (strict+mypy) + Black + Ruff + Trunk.io. Shared .cursor.rules and .trunk and pyproject.toml linting.
	- Aggressive Linting.
- Minimalistic, explanatory and brittle code. No Exception Handling. Like code from a blog tutorial. Breaks the second something goes wrong. Not later.
- "Duplo Blocks" → strict style definition for maximum code similarity.
- **Business Logic is Sacred**. Business Logic Readability is Paramount.
	- Keep business logic pure. **FCIS**, **DRY** and **brittle explanatory minimalistic code**. Code in the style of [**PICK FAMOUS CODER**]
	- Display logic always abstracted into util scripts 
	- Non-critical transforms etc always abstracted into _helpers SO LONG as the _function_name already tells us exactly what it does. Reader must not have to jump to the _helper to understand. If the _func name +EOL comment can't easily achieve this, don't abstract it. Examples of good things to use _helpers for:
		- Routers (IF statements)
		- JSON serialisation / de-serialisation
		- Regex-y bits.
	- Main()'s core logic _can_ be split up, so long as it's in order, and a pipeline. You can read the pipeline step functions without going back to Main().
	- Functional Core, Imperative Shell (FCIS / FCDS)
	- Business Logic is Sacred → must be maximally readable. Every Script's main() should ........ **FINISH**
	- **DIAGRAMMATIC SYNC** Clear** Abstraction and linkage between code to core visual diagrams is mandatory.
		- Every purple box in Diagram IS a python script. (Not pink)
	- **File DocStrings increase glanceability** → every script should have a thorough docstring (Oner, PP, Req, see below)
	- **Code Justifies itself** so we can cull & refactor regularly. Add end-of-line concise comments that explain why each thing is as it is and not some other way / why it's necessary vs we could get rid of it, what NEEDS it?
- 
- /core, cli, display and util funcs
	- core = shared utils
	- Logging - temporarily use rich prints with color
		- use _display_method utils (/shared/display) for coloured prints. e.g, rprint.dim(f"xxx {abc}")
		- dim for debug / unimportant steps
		- white for info
		- yellow for 'of note', red = fail/warning, green = success. Etc
		- Use Rich Tables via _display_func utils (in /display) 
	- Async Logging - dagger? [**PROPERLY RESEARCH THIS**]
- PIPELINES vs DAG
	- [WRITE SOMETHING ABOUT DAG]
	- Component = step / stage in pipeline
	- Node = data object passed between components
	- In Diagram: 
	- Stick to Pipeline. Shareable Retry logic with property for the prompt that enables it.
	- Don't use handoffs. If required, use a router function and a new pipeline.
	- All loopbacks except for 'retry-esque' logic must be its own pipeline.
	- Every 
- Script Boilerplate - every script should have:
	- Doctstring
		- Whole doctoring should be indented for folding.
		- Oner → should cover why and how.
		- Pyramid Principle 3-4 bullets Oner (elevator Pitch, 'why' is the focus)
		- Repeat Pyramid bullets, adding supporting evidence to each (why, not how)
		- Requirements & sub-requirements (indented). Emoji bullets: ⏳backlog 🟠 for planned ☑️ implemented ✅ Verified by human (AI never writes green tick)
		- Control Flow dia - vertically compressed, ASCII.
	- _ _ all _ _ = _____ for anything exported out of that file.
- zTasks
	- This is an ongoing log per app dir for AI agents to continuously update. **See Instructions (TBD)**
- File Structure
	- 
- File Naming
	- aa_cat_filename.** → all pipeline scripts should appear in order in the file explorer alphabetically. 'cat' is an optional tag for grouping similar scripts.
	- Leave a space (slack) for new items in pipeline. e.g, aa then cc then ee, the second letter is for secondary sorting. Once we are totally finished, we can rename files to be a,b,c,d,e,f,g.
	- Eg aa_init.py, 
- Prompts
	- Oner + pyramid → PromptObjects must have these, purely for documentation
	- PromptOutput base class forces Oner+pyramid but self-generates with 4.1-nano 
	- a
	- b
	- c
	- Use Implicit Concatenations (prompt = ("xxx\n", next line etc ))
	- Partials + boilerplate allowed but ONLY for regularly used




+ only schema + validation in pydantic classes.
+ Minimised side effects, maximum functional. Lean towards immutability.

Functions
1. Full-line vs EOL comments.
2. Numbered steps repeated in full-line comments.
3. Numbered inline comments show reasoning steps (= FCIS).
4. Failures (assert) immediately bubble to the orchestrator.
5. No logging library—just let exceptions halt the run (brittle).

```
# Declare numbered steps, re-use same numbers
def generate_prompt(inp: PromptInput) → PromptOutput:
    """gen-000  – literal Duplo block  
    1. craft minimal prompt  
    2. count tokens  
    3. return validated model  
    """
    # 1. naive embed of prob-desc
    body = f"### Task\n{inp.problem_desc}\nConstraints: {', '.join(inp.constraints)}"
    # 2. toy token count (replace w/ tiktoken)
    tokens = len(body.split())
    # 3. build output → Pydantic will raise if invalid
    return PromptOutput(
        initial_prompt=body,
        prompt_tokens=tokens,
        prompt_version=0,
        token_limits=inp.token_limits,  # pulled through for validator
    )
```

### Prompt Implicit Concatenations → enables mid-prompt comments.
```
# Multiline prompt examples

# 1. Define a prompt using triple quotes
prompt_triple = """Enter your text here.
Second line.
Third line."""  # 1: define prompt with triple quotes

# 2. Define a prompt using implicit concatenation in parentheses
prompt_concat = (
    "Enter your text here.\n"  # 2: first line with explicit newline
    "Second line.\n"          # 2: second line with explicit newline
    "Third line."             # 2: third line without trailing newline
)
```

## Pydantic Law
- If a schema is used **only inside one stage**, declare it right in that stage’s file (locality > DRY).
- If a schema is shared across stages or adapters, re-export it in `models.py` so imports stay single-hop.
### Critical “never bend” rules for Pydantic in this repo
1. **Strict mode everywhere**
	```
classConfigDict: strict = True
```
2. **No optional fields in intermediate models**Optional ➞ ambiguity ➞ hidden failures. Optional is allowed **only** at system boundaries (CLI, HTTP).
3. **Validate on assignment**
	```
model_config = {"validate_assignment": True}
```
4. **Forbid extra keys** (`extra="forbid"`)Helps catch upstream schema drift instantly.
5. **Custom validators over assert when field-specific**Makes error message actionable and keeps asserts for invariants spanning multiple fields.
6. **Never call .model_dump() without mode="json" or by_alias=True**Reduces accidental type coercion when someone dumps to native Python.
7. **Immutable config for inbound config objects** (`frozen=True`)Guards “Business Logic is Sacred” → once something is stamped valid it can’t mutate unless cloned.
8. **Version every major schema** (`__schema_version__ = "v1"` attribute)Lets you diff & migrate: only break pipeline when version bump is intentional.
9. **DocTests in each model’s docstring** that show one happy path & one failure; doubles as spec.
10. **Keep each model ≤ 25 lines**Long models hide complexity that likely belongs in another Duplo block.

## Logical Symbols
Here are the logical symbols with Python prompt engineering examples. Still missing that chain language AF came up with but this seems solid for now.
We can also show pipelines with shorthand just by defining minimal Pydantic Classes and a MicroPrompts singleton, then do e.g.,
```
def gen_synth_TC1s(ScenarioDoc: List[Scenario], PROMPTS) → TC1s: List[TC1]:
def gen_graders(ReqDoc: List[Req], PROMPTS) → Graders: List[Grader]:
def proc_TC1s(TC1s: List[TC1], MasterPrompt: (Sys,Usr)) → TC2s: List[TC2]:

```


**Basic Logical Connectives:**
∧ - AND (conjunction)     // "Generate code that is fast ∧ readable"
∨ - OR (disjunction)     // "Use pandas ∨ numpy for data processing"
¬ - NOT (negation)     // "¬ include deprecated functions in examples"
→ - IF...THEN (implication)     // "If user input invalid → return error message"
↔ - IF AND ONLY IF (biconditional)     // "Use async ↔ handling I/O operations"

**Quantifiers:**
∀ - FOR ALL (universal quantifier)     // "∀ functions must have docstrings"
∃ - THERE EXISTS (existential quantifier)     // "∃ at least one test case per function"
∃! - THERE EXISTS EXACTLY ONE     // "∃! main() function in the script"

**Inference and Proof:**
∴ - THEREFORE     // "Code fails tests ∴ needs debugging"
∵ - BECAUSE     // "Use list comprehension ∵ more Pythonic"
⊢ - PROVES/ENTAILS     // "Type hints ⊢ better code documentation"
⊨ - SATISFIES/MODELS     // "Implementation ⊨ the specified requirements"
⊥ - CONTRADICTION/FALSE     // "Infinite loop ⊥ program termination"
⊤ - TAUTOLOGY/TRUE     // "Python is interpreted ⊤"

**Set Theory:**
∈ - IS A MEMBER OF     // "'append' ∈ list methods"
∉ - IS NOT A MEMBER OF     // "'sort' ∉ tuple methods"
⊆ - IS A SUBSET OF     // "Built-in functions ⊆ Python standard library"
∪ - UNION     // "Import statements ∪ function definitions"
∩ - INTERSECTION     // "Common methods ∩ {list, tuple, str}"
∅ - EMPTY SET     // "Return ∅ when no matches found"

**Systems Architecture Relations:**
→ - USES/DEPENDS ON     // "Flask app → SQLAlchemy for database"
⇒ - IMPLIES/LEADS TO     // "High traffic ⇒ implement caching"
↗ - CONNECTS TO     // "API endpoint ↗ database query"
⟶ - FLOWS TO     // "User input ⟶ validation ⟶ processing"
⊃ - CONTAINS/INCLUDES     // "Django project ⊃ multiple apps"
≺ - PRECEDES     // "Authentication ≺ authorization"
≻ - FOLLOWS     // "Data processing ≻ input validation"
⟷ - BIDIRECTIONAL RELATIONSHIP     // "Client ⟷ server communication"
⟸ - RECEIVES FROM     // "Model ⟸ data from controller"
⟹ - SENDS TO     // "View ⟹ response to client"


## Notes to Will
Keep business logic pure. FCIS, DRY and brittle explanatory minimalistic code. Display logic, non-critical transforms that can be abstracted but the function name already tells us exactly what it does etc - should be abstracted.
FCIS, class schema-first, and how clear the business logic is reinforces a lot of things.
I'm doubling down on FCIS, functional core imperative shell (or declarative shell). Keeping helper functions as small as possible, max two tears deep away from main(), only abstract when the function name itself plus a short end of line comment can be self-explanatory in terms of what is going on within it, and if that doesn't feel possible, the structure of main() probably needs revisiting.
Readability is queen, even over elegance, list, comprehensions, and set comprehensions, everything in that direction, it's the same rule as for a helper function, if it's simple, great, if it's obvious what it is doing even if it's quite complex, great, but as soon as it takes more than 30 seconds to work it out, or more, it should've been nested for loops. If those end up being more than three levels deep, step back and rethink as it's unlikely this is now a clear and easy to understand implementation.
Every file should essentially declare how it is supposed to work in its guard main, which is basically free code. No matter how good your regression tests are nothing shows you whether something works the way you expect better than running the script and seeing if the explanatory print statements lineup with the outputs.
Religiously right regression test as you go, it's extremely easy now, non-blocking, and even if your next change causes 20 of them to break, it doesn't take very long at all to check them and send check whether any of those brakes are unexpected. LLM agents clearly find the same to be true. Generate them, forget them, have the agent test regularly and test yourself regularly.
Demand minimalistic and brittle code mimicking the explanatory code you get in blog posts to reduce bloat, but also prevent non-fatal failures. The second changing something breaks something we want to know about it and LLMs are no different. They can miss a warning log but never miss a trace back.
I intend to add a linting flag for any exception handling outside of Main() to prevent agents from falling back to it. No matter how much I reinforce this point, they won't stop doing it. If they want to add them for debug testing, they should be duplicating the script into a .temp file. So the code they use to figure something out never remains in the business logic.
Protect the clarity of the core business logic and class structure at all costs.
Almost all fixes or adjustments or new features should be done via a new function. Whilst it may not need to be there in the future, it's tritely easy to fold them back into the main business logic, and temporarily self-documents every quick fix or feature or patch.
Ruthlessly cull code.
I feel like I might not be too far away from being able to draw up a set of specifications requirements and test case templates so we can formalise a refactor agent. Which I think would be very powerful.
Tracking requirements, listing every task and sub tasks done by agents makes it far easier to generate a list of user stories, check that none are missing, and then, having made a reflector plan, generates a whole set of unit tests that should be expected to pass given the new structure.

Use DocTests for Docstring regression tests

I think we've pretty much agreed we should use a PIPELINE ARCHITECTURE primarily now. Yes, agents can loop around so in some ways not DAG but given no loop is allowed unlimited (generally <3 or <10 iterations max), DAG still works just fine, just need an abstraction for easily handling that same as for disagreement / confidence and best-of-n ensembles.

Regression Tests should be named with my own minimalistic very clear IFTTT/user story language. 
Generally 'if not x → y then broken as ' e.g, "as usr: if api → '' then broken as 

FCIS/D → Lean functional, always. Classes should be objects passed around, with methods if that script is otherwise unimportant / it's an obvious helper for that class.
  - but largely, methods and using self.xxx will obfuscate where the data came from. Exeption is when you're several nested functions deep (find extension for counting this?).
  - Keep _helpers flat where possible. Pipeline attitude → keep it easy.
  - helpers → name should fully explain what it does, otherwise re-think entirely. Shouldnt have to break flow to go check it.
  - HANDLERS - non-core domain logic can be abstracted into HANDLERS
  - PROCESSORS - more complex transforms etc can abstract into procs. Often should be methods as unimportant and attached to dataType.

  ⏰ MUTABILITY → Another issue with Classes. TODO → change values in Class... when to / not to? Save to new Class? Pipeline? Lost on this.
  



### Use Pyramid Principle for Coding / self-documenting code.

The McKinsey Pyramid Principle is a structured communication and problem-solving framework developed by Barbara Minto during her time at McKinsey & Company. It's widely used in consulting and business communication to organize thoughts and present ideas clearly.
Core Structure
The pyramid follows a top-down approach with three key levels:
	1.	Top Level: Main conclusion or recommendation	2.	Middle Level: Supporting arguments (usually 3-4 key points)	3.	Bottom Level: Supporting data, facts, and evidence
Key Principles
MECE Framework
	•	Mutually Exclusive: No overlap between categories	•	Collectively Exhaustive: All possibilities are covered
Logical Flow
	•	Deductive reasoning: If A and B are true, then C follows	•	Inductive reasoning: Similar facts lead to a general conclusion
The SCQA Method
Used for structuring presentations and documents:
	•	Situation: Context and background	•	Complication: Problem or challenge	•	Question: Key question to be answered	•	Answer: Your recommendation or solution
Benefits
	•	Clarity: Forces clear thinking and communication	•	Efficiency: Saves time by presenting conclusions first	•	Persuasiveness: Logical structure builds compelling arguments	•	Memorability: Hierarchical structure aids retention