# Agentic 'Sight'
Visual feedback can be very useful to agents but often they are given text or a spreadsheet or similar and so miss something they wouldn't have if they were 'looking' at it. Just like a human if they were looking at a spreadsheet in JSON format where patterns are less visually obvious.

## Simple Example: ASCII flow chart fixer.
Here's a thread of CC working out how to make an ASCII flow chart fixer.
Problem: LLMs can't count so they often get misalignment in ASCII charts they generate. This said, they are often better at making elegant ASCII flow charts than they are at generating mermaid diagrams.
Solution: fix pipe alignment issues (primarily vertical) programmatically.

The LLM agent could _not_ handle this. It can't generate correct ASCII charts reliably, or fix them reliably, so it also can't **tell** that an output is incorrect. Using Pillow to save the ASCII chart output as an image however, it **can** tell that that's broken. 
### Interesting Moment 1
Not bad strategy but I think it's going to miss the elegant fix of 'these x characters are used for connections between horizontal and vertical bars. Detect those, then align vertically downwards after that, final repairs afterwards. This kind of insight is a 'rabbit hole' -type problem. Insufficient reasoning and experimentation up front to understand the problem paints it into fixing the problem the way it originally set out to.
> Can we detect 'rabbit holing' via secondary threads looking for more elegant solutions and kicking off new CC instances when it thinks it has found them? Why not have reasoners planning and re-planning whilst the main agent/s are already trying to fix the problem? If this 'more elegant' solution is indeed more elegant, the solution that started later may even finish first. Graders assessing which solution is best even if 5 solutions seem to work will not struggle to pick which is more elegant, although detecting which solution is less overfitted will be more challenging.
![image](Agentic%20'Sight'_attachments/B9E95B2E-E759-41EF-A967-B0031A6BA7EE.png)

### Interesting moment 2
This is great. It came up with a series of simple test cases for aligning arrows correctly and is running them out of the guard main.
![image](Agentic%20'Sight'_attachments/CleanShot%202025-07-02%20at%2017.53.50.png)

