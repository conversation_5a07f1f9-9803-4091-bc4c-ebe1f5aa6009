# Trae-ToDo
Concept
*First do this with four cols + four cc terminal cols - do it manually. Learn.*
1. Markdown Note as Command Center. You type, take things from pad-out / research col if you like, 1st pane is yours. Nesting info is easy like how images work (needs shortcut). 2nd /3rd pane is ResearchBot → answers open questions, get's API snippets from Context7 / Web, looks for issues that might have been missed. Pane 1. 
2. 1 model call checking for changes / running diffs and logging them.
3. Condition for triggering anthropic editor / CC to update the second pane (the zTask list, see formatting, tasks, sub-tasks etc.). Ideally include verbatim quotes from source and v03 etc to show edits made. Always track diff on this w Cursor?
4. ResearchBot → List of researches done. Agentic. Option to browser-use use true o3.
4. When to kick off CC on that task list?
	5. How many to kick off? One batch?
	6. Have a Dependency prompt maybe that outputs a mermaid for sense check and 