# NotePlanBot Justfile - Quick command reference for developers
# Run 'just' to see available commands
# 
# Monitor variants:
#   just run           - Basic monitoring, no UI
#   just monitor-simple - Simple status display
#   just monitor       - Full three-pane UI (recommended)

# Default recipe - show help
default:
    @just --list

# Run the bot (basic monitoring, no UI)
run:
    python NotePlanBot/run.py

# Run with custom bridge file
run-bridge FILE:
    python NotePlanBot/run.py {{FILE}}

# Run monitor with simple status UI
monitor-simple:
    python NotePlanBot/monitor_terminal.py

# Run monitor with full three-pane UI (recommended)
monitor-ui:
    python -m NotePlanBot.visualizer

# Aliases for common use
monitor: monitor-ui
visualizer: monitor-ui

# Test NotePlan reader
test-reader:
    python -m NotePlanBot.reader.noteplan_reader

# Test NotePlan editor
test-editor:
    python -m NotePlanBot.editor.noteplan_editor

# Test bridge model
test-bridge:
    python -m NotePlanBot.files.bridge.bridge_model

# Show configuration
config:
    python -m NotePlanBot.config

# Install dependencies
install:
    pip install -e NotePlanBot/

# Install dev dependencies
install-dev:
    pip install -e "NotePlanBot/.[dev]"

# Run tests
test:
    pytest NotePlanBot/tests/ 2>/dev/null || python NotePlanBot/tests/test_regression.py

# Format code
format:
    black NotePlanBot/src/ NotePlanBot/tests/
    ruff format NotePlanBot/src/ NotePlanBot/tests/

# Lint code
lint:
    ruff check NotePlanBot/src/ NotePlanBot/tests/

# Clean up
clean:
    find NotePlanBot/ -type d -name __pycache__ -exec rm -rf {} +
    find NotePlanBot/ -type f -name "*.pyc" -delete
    rm -rf NotePlanBot/.pytest_cache
    rm -rf NotePlanBot/.ruff_cache

# Create virtual environment
venv:
    python -m venv .venv
    @echo "Run 'source .venv/bin/activate' to activate"

# Update CLAUDE.md with current status
update-claude:
    @echo "Remember to update CLAUDE.md with implementation details!"
