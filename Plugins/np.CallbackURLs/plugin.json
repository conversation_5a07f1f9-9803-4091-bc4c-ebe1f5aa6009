{"COMMENT": "Details on these fields: https://help.noteplan.co/article/67-create-command-bar-plugins", "macOS.minVersion": "10.13.0", "noteplan.minAppVersion": "3.4.0", "plugin.id": "np.<PERSON>backURLs", "plugin.name": "🔗 Link Creator", "plugin.version": "1.8.0", "plugin.lastUpdateInfo": "1.8.0: Added templating specific commands to wizard to reduce confusion", "plugin.description": "Interactively helps you form links/x-callback-urls (and also Template Tags with runPlugin commands) to perform actions from within NotePlan or between other applications and NotePlan.", "plugin.author": "dwer<PERSON><PERSON><PERSON>", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.url": "https://github.com/NotePlan/plugins/blob/main/np.CallbackURLs/README.md", "plugin.changelog": "https://github.com/NotePlan/plugins/blob/main/np.CallbackURLs/CHANGELOG.md", "plugin.commands": [{"name": "Get X-Callback-URL", "description": "Run <PERSON> to get X-Callback-URL", "jsFunction": "xCallbackWizard", "alias": ["xcallback", "url", "link"], "arguments": ["Command Type", "Pass Back Results (e.g. if calling from another plugin)"]}, {"name": "Create Link to Current Note+Heading", "description": "External link to open this note", "jsFunction": "headingLink", "alias": ["headinglink", "notelink"]}, {"name": "Create Link to Current Line", "description": "External link to open note to this line", "jsFunction": "lineLink", "alias": ["lineLink"]}, {"name": "open todos containing links in browser", "alias": [], "description": "Open URLs in all open todo items on the page", "jsFunction": "openIncompleteLinksInNote"}, {"name": "open URL on this line", "alias": ["<PERSON><PERSON>l", "launch"], "description": "Open the URL on the current line in the default browser", "jsFunction": "openURLOnLine"}], "plugin.settings": [{"COMMENT": "Plugin settings documentation: https://help.noteplan.co/article/123-plugin-configuration", "type": "heading", "title": "X-Callback-URL Creator <PERSON><PERSON><PERSON>"}, {"key": "showXSuccess", "title": "Ask in <PERSON> if you want X-Success field", "description": "X-Callbacks have an option of including an X-Success field. This allows NotePlan to return information back to a calling application. It's only used rarely, so by default, the wizard will not ask you if you want it. If you do want to use X-Success parameters, check this box and the wizard will ask you in the flow.", "type": "bool", "default": false, "required": true}, {"note": "================== DEBUGGING SETTINGS ========================"}, {"NOTE": "DO NOT CHANGE THE FOLLOWING SETTINGS; ADD YOUR SETTINGS ABOVE ^^^", "type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "type": "string", "title": "Log Level", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "description": "Set how much logging output will be displayed when executing Task Sorting commands in NotePlan Plugin Console Logs (NotePlan -> Help -> Plugin Console)\n\n - DEBUG: Show All Logs\n - INFO: Only Show Info, Warnings, and Errors\n - WARN: Only Show Errors or Warnings\n - ERROR: Only Show Errors\n - none: Don't show any logs", "default": "INFO", "required": true}]}