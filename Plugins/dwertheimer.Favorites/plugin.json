{"COMMENT1": "Note If you are not going to use the `npm run autowatch` command to compile, then delete the macOS.minVersion line below", "macOS.minVersion": "10.13.0", "noteplan.minAppVersion": "3.0.21", "plugin.id": "dwertheimer.Favorites", "plugin.name": "⭐️ Favorites", "plugin.description": "Get fast access to commonly-used notes. Set any Project Note(s) as a Favorites and have quick access to choose/open the file", "plugin.author": "@dwertheimer", "plugin.version": "1.2.10", "plugin.lastUpdateInfo": "1.2.10: Bug fix for frontmatter", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.url": "Put a link to a web page or your github readme file here, and it will be displayed as the 'website' in the plugin preference pane to give users more info on the plugin before/after install, e.g.  https://github.com/dwertheimer/Noteplan-plugins/blob/main/np.plugin-flow-skeleton/readme.md", "plugin.commands": [{"name": "faves - Choose+Open Favorite (⭐️) Note", "description": "Open one of the saved Favorites", "jsFunction": "openFavorite", "alias": ["faves", "favorites"]}, {"name": "fave - Make this Note a Favorite (⭐️)", "description": "Set open Note to be a Favorite (add ⭐️)", "jsFunction": "setFavorite", "alias": ["fave"]}, {"name": "unfave - Remove this Note from Favorites List (⭐️)", "description": "Remove this Note from saved Favorites (remove ⭐️ from name)", "jsFunction": "removeFavorite", "alias": ["unfave"]}, {"note": "****************************************************", "not1": "**********  PRESETS BELOW THIS LINE      ***********", "not2": "****************************************************"}, {"name": "Set/Change/Rename Preset Action", "description": "Add or edit a preset to run a favorite URL/X-Callback", "jsFunction": "changePreset"}, {"name": "x-Test Preset Settings Migration", "description": "Test auto migration of presets; use this URL to test:", "URL": "noteplan://x-callback-url/runPlugin?pluginID=dwertheimer.Favorites&command=x-Test%20Preset%20Settings%20Migration", "jsFunction": "onUpdateOrInstall", "hidden": true}, {"name": "Favorites: Set Preset 01", "description": "Run Favorite Action", "jsFunction": "runPreset01", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 02", "description": "Run Favorite Action", "jsFunction": "runPreset02", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 03", "description": "Run Favorite Action", "jsFunction": "runPreset03", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 04", "description": "Run Favorite Action", "jsFunction": "runPreset04", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 05", "description": "Run Favorite Action", "jsFunction": "runPreset05", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 06", "description": "Run Favorite Action", "jsFunction": "runPreset06", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 07", "description": "Run Favorite Action", "jsFunction": "runPreset07", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 08", "description": "Run Favorite Action", "jsFunction": "runPreset08", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 09", "description": "Run Favorite Action", "jsFunction": "runPreset09", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 10", "description": "Run Favorite Action", "jsFunction": "runPreset10", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 11", "description": "Run Favorite Action", "jsFunction": "runPreset11", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 12", "description": "Run Favorite Action", "jsFunction": "runPreset12", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 13", "description": "Run Favorite Action", "jsFunction": "runPreset13", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 14", "description": "Run Favorite Action", "jsFunction": "runPreset14", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 15", "description": "Run Favorite Action", "jsFunction": "runPreset15", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 16", "description": "Run Favorite Action", "jsFunction": "runPreset16", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 17", "description": "Run Favorite Action", "jsFunction": "runPreset17", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 18", "description": "Run Favorite Action", "jsFunction": "runPreset18", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 19", "description": "Run Favorite Action", "jsFunction": "runPreset19", "isPreset": true, "hidden": true}, {"name": "Favorites: Set Preset 20", "description": "Run Favorite Action", "jsFunction": "runPreset20", "isPreset": true, "hidden": true}, {"NOTE": "DO NOT EDIT THIS COMMAND/TRIGGER", "name": "Favorites: Update P<PERSON><PERSON>", "description": "Preferences", "jsFunction": "editSettings"}], "notes": {"note": "****************************************************", "not1": "**********  SETTINGS BELOW THIS LINE      **********", "not2": "****************************************************"}, "plugin.settings": [{"COMMENT": "Plugin settings documentation: https://help.noteplan.co/article/123-plugin-configuration", "type": "heading", "title": "Favorites Settings"}, {"key": "charsToPrepend", "title": "Characters to Prepend to Command", "description": "If there are characters like “-<PERSON>” in front of each command, it will keep them together and float them to the top of the menu. Whatever text you put here will be prepended to any command name you set. Blank this field out to not prepend any characters. No spaces will be added after text you put in this box, so if you want a space, add it to the text box.", "type": "string", "default": "-", "required": false}, {"key": "favoriteIdentifier", "title": "How should favorite notes be identified?", "description": "Favorite notes can be identified by:\n- A star (⭐️) in the title\n- A frontmatter field like `favorite: <any text>`\n- both ⭐️ in title and `favorite: <any text>` in frontmatter (both are required)\n- either ⭐️ in title or `favorite: <any text>` in frontmatter (either will do, but only one is required). (default)", "type": "string", "default": "Star or Frontmatter (either)", "required": true, "choices": ["Star in title", "Frontmatter only", "Star and Frontmatter (both)", "Star or Frontmatter (either)"]}, {"type": "string", "title": "Favorite key/label to use in frontmatter to designate a favorite note", "key": "<PERSON><PERSON><PERSON>", "default": "favorite", "required": true, "description": "If you set the favorite notes identifer to any setting which includes frontmatter, this setting allows you to change the name of the frontmatter key used for the favorite field. De<PERSON><PERSON> is 'favorite' but you can change it to 'favourite' or 'fav' or whatever you want. Note: cannot include spaces, tabs, or special characters (e.g. #, @ etc.) in the key name."}, {"type": "hidden", "key": "pluginID", "default": "dwertheimer.Favorites", "COMMENT": "This is for use by the editSettings helper function. PluginID must match the plugin.id in the top of this file"}, {"key": "runPreset01", "type": "hidden", "default": "", "title": "Preset 01", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset02", "type": "hidden", "default": "", "title": "Preset 02", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset03", "type": "hidden", "default": "", "title": "Preset 03", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset04", "type": "hidden", "default": "", "title": "Preset 04", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset05", "type": "hidden", "default": "", "title": "Preset 05", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset06", "type": "hidden", "default": "", "title": "Preset 06", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset07", "type": "hidden", "default": "", "title": "Preset 07", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset08", "type": "hidden", "default": "", "title": "Preset 08", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset09", "type": "hidden", "default": "", "title": "Preset 09", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset10", "type": "hidden", "default": "", "title": "Preset 10", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset11", "type": "hidden", "default": "", "title": "Preset 11", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset12", "type": "hidden", "default": "", "title": "Preset 12", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset13", "type": "hidden", "default": "", "title": "Preset 13", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset14", "type": "hidden", "default": "", "title": "Preset 14", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset15", "type": "hidden", "default": "", "title": "Preset 15", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset16", "type": "hidden", "default": "", "title": "Preset 16", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset17", "type": "hidden", "default": "", "title": "Preset 17", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset18", "type": "hidden", "default": "", "title": "Preset 18", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset19", "type": "hidden", "default": "", "title": "Preset 19", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"key": "runPreset20", "type": "hidden", "default": "", "title": "Preset 20", "description": "Do not change this setting manually. Use the \"/Change Favorite Preset\" command."}, {"note": "================== DEBUGGING SETTINGS ========================"}, {"NOTE": "DO NOT CHANGE THE FOLLOWING SETTINGS; ADD YOUR SETTINGS ABOVE ^^^", "type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "type": "string", "title": "Log Level", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "description": "Set how much logging output will be displayed when executing PLUGIN DOWNLOAD TEST commands in NotePlan Plugin Console Logs (NotePlan -> Help -> Plugin Console)\n\n - DEBUG: Show All Logs\n - INFO: Only Show Info, Warnings, and Errors\n - WARN: Only Show Errors or Warnings\n - ERROR: Only Show Errors\n - none: Don't show any logs", "default": "INFO", "required": true}]}