{"noteplan.minAppVersion": "3.0.23", "macOS.minVersion": "10.13.0", "plugin.id": "jgclark.NoteHelpers", "plugin.name": "📙 Note Helpers", "plugin.description": "Commands to quickly jump around and manage notes", "plugin.author": "<PERSON> & Eduard <PERSON>", "plugin.url": "https://github.com/NotePlan/plugins/tree/main/jgclark.NoteHelpers/", "plugin.changelog": "https://github.com/NotePlan/plugins/blob/main/jgclark.NoteHelpers/CHANGELOG.md", "plugin.version": "1.1.1", "plugin.lastUpdateInfo": "1.1.1: bug fix.\n1.1.0: new 'list published notes' command.\n1.1.0: added 'write modified' command to write the modified date to frontmatter (on each save). Can be run by hand or can be included as a trigger using the 'add trigger to note' command. Small improvements to \"new note from selection\".\n1.0.0: move '/new note from ...' commands here from Filer plugin, and revived '/new note' command.\n0.20.3: New 'printEditorDetailed' command to log all paragraph details as well.\n0.20.2: 'printNote' extended to cover backlinks.\n0.20.1: new command 'printNote' for debugging purposes.\n0.20.0: new commands \"unlinked note finder\" and \"delete note\". Bug fix to \"rename note filename\" command.\n0.19.2: fix edge cases with \"add trigger to note\".\n0.19.1: \"add trigger to note\" command can now be run from template. Added migration message about 'open note' commands.\n0.19.0: fix to '/rename inconsistent filename' command. Moved 'open note' functions to WindowTools plugin. Updated the display of the 'index folders' command. Removed 'Show month/quarter/year' commands as they are now in the main NP menus.", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.commands": [{"name": "add number of days to dates", "alias": ["count", "add", "days"], "description": "Look for bullets mentioning dates and add number of days till that date", "jsFunction": "countAndAddDays"}, {"name": "add trigger to note", "alias": ["trigger", "add"], "description": "Select from a list of available triggers to add to the current note", "jsFunction": "addTriggerToNote", "parameters": ["trigger string (e.g. 'onEditorWillSave => jgclark.DashboardReact.decideWhetherToUpdateDashboard')"]}, {"name": "convert to frontmatter", "alias": ["frontmatter"], "description": "Convert the current note to use frontmatter syntax, including some default text that can be added in the Plugin's settings", "jsFunction": "addFrontmatterToNote"}, {"name": "delete note", "alias": ["dn"], "description": "Delete the current note (moves to Trash)", "jsFunction": "trashNote", "parameters": []}, {"name": "enable heading links", "alias": ["local links", "hash links", "links to headings"], "description": "Look for Links to headings and make them work by converting them to plugin command calls", "jsFunction": "convertLocalLinksToPluginLinks"}, {"name": "find and link unlinked notes in current note", "alias": ["unlinked"], "description": "Find and create links to unlinked notes", "jsFunction": "findUnlinkedNotesInCurrentNote"}, {"name": "find and link unlinked notes in all notes", "alias": ["allunlinked"], "description": "Find and create links to all unlinked notes", "jsFunction": "findUnlinkedNotesInAllNotes"}, {"name": "index folders", "alias": ["index"], "description": "Make/Update indexes for all notes in a folder (and sub-folders if wanted)", "jsFunction": "indexFolders", "parameters": ["folder", "string: displayOrder=(key);dateDisplayType=(key);includeSubfolders=(key)"]}, {"name": "jump to heading", "alias": ["jh", "jump", "heading"], "description": "Jumps to the heading that the user selects. (Currently only works in main window)", "jsFunction": "jumpToHeading", "parameters": ["heading"]}, {"name": "jump to note's heading", "alias": ["jn", "jump", "note"], "description": "Jump to a different note, and then selected heading. (Currently only works in main window)", "jsFunction": "jumpToNoteHeading"}, {"name": "jump to done", "alias": ["jd", "jump", "done"], "description": "Jump to the '## Done' section. (Currently only works in main window)", "jsFunction": "jumpToDone"}, {"name": "list published notes", "alias": ["lpn"], "description": "writes a note 'Published Notes' with links to all published notes'", "jsFunction": "listPublishedNotes"}, {"name": "move note", "alias": ["mn", "move", "note"], "description": "Moves the currently opened (non-calendar) note to a folder you select", "jsFunction": "moveNote"}, {"name": "new note", "alias": ["nn"], "description": "Make New note with choice of folder", "jsFunction": "newNote"}, {"name": "new note from clipboard", "alias": ["nnc", "new"], "description": "New note from clipboard", "jsFunction": "newNoteFromClipboard"}, {"name": "new note from selection", "alias": ["nns", "new"], "description": "New note from selection (and leave link to it in its place)", "jsFunction": "newNoteFromSelection"}, {"name": "open URL from a note", "alias": ["oun", "open", "URL"], "description": "Open a chosen URL from a chosen note", "jsFunction": "openURLFromANote"}, {"name": "reset caches", "alias": ["reset", "cache"], "description": "Reset NotePlan caches", "jsFunction": "resetCaches"}, {"name": "list inconsistent note filenames", "alias": ["listInconsistentNames"], "description": "Lists the names of notes whose filenames are inconsistent with their titles.", "jsFunction": "listInconsistentNames"}, {"name": "rename filename to title", "alias": ["titleToFilename"], "description": "Renames the current filename to the title of the note.", "jsFunction": "titleToFilename"}, {"name": "rename note filename", "alias": ["rename"], "description": "Rename the current note's filename to one you specify.", "jsFunction": "renameNoteFile"}, {"name": "reset title to match filename", "alias": ["filenameToTitle"], "description": "Resets the current note title to match its filename.", "jsFunction": "filenameToTitle"}, {"name": "rename inconsistent note filenames", "alias": ["renameInconsistentNames"], "description": "Renames the files of notes whose filenames are inconsistent with their titles.", "jsFunction": "renameInconsistentNames"}, {"name": "update all indexes", "alias": ["uai", "index"], "description": "Update all folder index notes", "jsFunction": "updateAllIndexes"}, {"name": "NoteHelpers: update plugin settings", "description": "Settings interface (even for iOS)", "jsFunction": "updateSettings"}, {"name": "log Editor Note", "alias": ["printNote", "logNote", "lnd"], "description": "Log main details about note to console", "jsFunction": "printNote"}, {"name": "log Editor Note (detailed)", "alias": ["lend"], "description": "Log full details about current Editor note to console", "jsFunction": "logEditorNoteDetailed"}, {"name": "triggerFindUnlinkedNotes", "description": "onEditorWillSave", "jsFunction": "triggerFindUnlinkedNotes", "hidden": true}, {"name": "Write changed/modified date to frontmatter", "description": "Write the modified date to frontmatter (on each save). Writes to 'modified' key.", "jsFunction": "writeModified", "alias": ["modified"], "arguments": []}, {"name": "writeModified", "description": "Write the modified date to frontmatter (on each save). Writes to 'modified' key. Can be run by hand or can be included as a trigger using the 'add trigger to note' command.", "jsFunction": "writeModified", "alias": [], "arguments": [], "hidden": true}], "offerToDownloadPlugin": {"id": "jgclark.WindowTools", "minVersion": "1.0.0"}, "commandMigrationMessage": "Note: the 'open ... note in new ...' commands have been migrated to the WindowTools plugin.", "plugin.disabled_commands": [{"name": "Show This Month", "alias": ["stm", "month"], "description": "Open the current Month note", "jsFunction": "showMonth"}, {"name": "Show This Quarter", "alias": ["stq", "quarter"], "description": "Open the current Quarter note", "jsFunction": "showQuarter"}, {"name": "Show This Year", "alias": ["sty", "year"], "description": "Open the current Year note", "jsFunction": "showYear"}], "plugin.settings": [{"type": "separator"}, {"type": "heading", "title": "'convert to frontmatter' command setting"}, {"key": "defaultFMText", "title": "Default Text to add to frontmatter", "description": "The standard text to add after the title in the frontmatter. Can include line breaks by typing return.", "type": "string", "default": "", "required": false}, {"type": "separator"}, {"type": "heading", "title": "'indexFolders' command settings"}, {"key": "displayOrder", "title": "Sort order for index items", "description": "Whether index entries are sorted alphabetically by title (the default), by created date, or by last updated date", "type": "string", "choices": ["alphabetical", "createdDate", "updatedDate"], "default": "alphabetical", "required": true}, {"key": "dateDisplayType", "title": "What type of date suffix to add?", "description": "What type of date/time period to add to the end of note links in the index. 'timeSince' is time since the note was last updated; 'updatedDate' shows the date the note was last updated.", "type": "string", "choices": ["none", "timeSince", "updatedDate"], "default": "none", "required": true}, {"key": "includeSubfolders", "title": "Include Subfolders when making an index?", "description": "If set, then all subfolders will be indexed in the same name as the folder.", "type": "bool", "default": true, "required": true}, {"key": "indexTitle", "title": "Title to use for Index notes", "description": "This can include a placeholder `{{folder}}` or `{{full_folder_path}}` which will be replaced by the folder's name or full path.", "type": "string", "default": "_{{folder}} index", "required": true}, {"type": "separator"}, {"type": "heading", "title": "'open URL from a note' command setting"}, {"key": "ignoreCompletedItems", "title": "Ignore URLs in completed items?", "description": "Whether to ignore URLs found in completed (or cancelled) tasks or checklists.", "type": "bool", "default": true, "required": true}, {"type": "separator"}, {"type": "heading", "title": "'Write changed/modified date to frontmatter' command settings"}, {"key": "dateFormat", "title": "Date format", "description": "The format of the date to write to frontmatter. Use 'ISO' for ISO 8601 format (YYYY-MM-DDTHH:MM:SS.SSSZ), or 'Local' to use your local time settings format.", "type": "string", "default": "ISO", "required": false, "choices": ["ISO", "Local"]}, {"key": "authorID", "title": "Author ID", "description": "Your intiials or ID to use in command 'Write changed/modified date to frontmatter'. Leave blank to not include an author ID next to the date.", "type": "string", "default": "", "required": false}, {"type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "title": "Log Level", "description": "Set how much output will be displayed for this plugin in the NotePlan > Help > Plugin Console. DEBUG is the most verbose; NONE is the least (silent).", "type": "string", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "default": "INFO", "required": true}]}