{"noteplan.minAppVersion": "3.9.2", "macOS.minVersion": "10.13.0", "plugin.id": "np.Preview", "plugin.name": "🖥️ Preview", "plugin.description": "Shows HTML preview of the current note, including Mermaid diagrams and MathJax equations. Requires NP v3.9.2.", "plugin.icon": "", "plugin.author": "<PERSON>", "plugin.url": "https://github.com/NotePlan/plugins/tree/main/np.Preview/", "plugin.version": "0.4.5", "plugin.lastUpdateInfo": "v0.4.5: updated to use Mermaid v11.\nv0.4.4: added embed images to preview, fixed some bugs", "plugin.changelog": "https://github.com/NotePlan/plugins/blob/main/np.Preview/CHANGELOG.md", "plugin.dependencies": [], "plugin.requiredFiles": ["<EMAIL>", "tex-chtml.js"], "plugin.requiredSharedFiles": [], "plugin.script": "script.js", "plugin.isRemote": "false", "plugin.commands": [{"name": "preview note", "alias": ["pn"], "description": "Preview note in HTML window", "jsFunction": "previewNote"}, {"hidden": true, "name": "open preview in browser", "alias": ["opib"], "description": "Open note preview in browser ready to print", "jsFunction": "openPreviewNoteInBrowser"}, {"hidden": true, "name": "updatePreview", "description": "onEditorWillSave", "jsFunction": "updatePreview"}, {"name": "start live preview", "description": "Preview note in HTML window, and add a trigger to enable near-live update to it", "jsFunction": "addTriggerAndStartPreview"}], "plugin.commands_disabled": [{"name": "test:checkboxes", "description": "Test Checkboxes", "jsFunction": "testCheckboxes"}, {"hidden": true, "name": "toggle", "description": "Toggle Test Checkboxes", "jsFunction": "toggle"}, {"name": "test:MathML1", "description": "Test MathML 1", "jsFunction": "testMathML1"}, {"name": "test:MathML2", "description": "Test MathML 2", "jsFunction": "testMathML2"}, {"name": "test:MathML3", "description": "Test MathML 3", "jsFunction": "testMathML3"}, {"name": "test:MathML4", "description": "Test MathML 4", "jsFunction": "testMathML4"}, {"name": "test:MathJax1", "description": "Test MathJax 1", "jsFunction": "testMathJax1"}, {"name": "test:MathJax2", "description": "Test MathJax 2", "jsFunction": "testMathJax2"}, {"name": "test:mermaid3", "description": "Test Mermaid 3", "jsFunction": "testMermaid3"}, {"name": "test:mermaid4", "description": "Test Mermaid 4", "jsFunction": "testMermaid4"}], "plugin.settings": [{"type": "heading", "title": "Dashboard settings"}, {"type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "type": "string", "title": "Log Level", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "description": "Set how much logging output will be displayed when executing Tidy commands in NotePlan Plugin Console Logs (NotePlan -> Help -> Plugin Console)\n\n - DEBUG: Show All Logs\n - INFO: Only Show Info, Warnings, and Errors\n - WARN: Only Show Errors or Warnings\n - ERROR: Only Show Errors\n - none: Don't show any logs", "default": "WARN", "required": true}]}