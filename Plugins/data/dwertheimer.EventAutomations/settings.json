{"createSyncedCopies": "", "durationMarker": "'", "workDayEnd": "23:59", "linkText": "🔗", "eventLength": "30", "intervalMins": 5, "removeDuration": true, "allowEventSplits": false, "confirm": true, "removeDateText": false, "defaultDuration": 15, "showResultingTimeDate": true, "todoChar": "+", "foldSyncedCopiesHeading": false, "includeLinks": "Pretty Links", "presets": [{"label": "Limit Time Blocks to Work Hours", "workDayStart": "08:00", "workDayEnd": "17:59"}, {"label": "Create Timeblocks on Calendar", "todoChar": "*"}], "_logLevel": "LOG", "includeTasksWithText": [], "checkedItemChecksOriginal": false, "foldTimeBlockHeading": false, "excludeTasksWithText": [], "timeframes": {"early": ["07:00", "08:30"], "evening": ["18:00", "22:00"], "afternoon": ["12:00", "18:00"], "late": ["22:00", "23:00"], "morning": ["08:30", "12:00"]}, "timeBlockTag": "#🕑", "workDayStart": "00:00", "syncedCopiesTitle": " [Today's Synced Tasks](noteplan://x-callback-url/runPlugin?pluginID=dwertheimer.EventAutomations&command=Insert%20Synced%20Todos%20for%20Open%20Calendar%20Note)", "orphanTagggedTasks": "OUTPUT_FOR_INFO (but don't schedule them)", "mode": "PRIORITY_FIRST", "plugin_ID": "dwertheimer.EventAutomations", "includeAllTodos": true, "timeBlockHeading": "[Time Blocks](noteplan://runPlugin?pluginID=dwertheimer.EventAutomations&command=atb%20-%20Create%20AutoTimeBlocks%20for%20%3Etoday%27s%20Tasks)"}