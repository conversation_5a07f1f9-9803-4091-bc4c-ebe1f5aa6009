{"noteplan.minAppVersion": "3.7", "macOS.minVersion": "10.13.0", "plugin.id": "jgclark.Dashboard", "plugin.name": "🎛 Dashboard", "plugin.description": "A Dashboard for NotePlan, that in one place shows\n- a compact list of open tasks and checklists from today's note\n- scheduled open tasks and checklists from other notes.\n- Similarly for yesterday's note, tomorrow's note, and the weekly, monthly and quarterly notes too (if used)\n- all overdue tasks\n- all open tasks and checklists that contain particular @tags or #mentions of your choosing\n- the next notes ready to review (if you use the 'Projects and Reviews' plugin).", "plugin.author": "@jgclark", "plugin.version": "2.2.1", "plugin.hidden": false, "plugin.lastUpdateInfo": "2.2.1: Add new sorting option for Tag and Overdue sections.\n2.2.0: Add 'Search' section. New keyboard shortcuts. Plus many small improvements, bug fixes and performance improvements. See documentation for details.\n2.1.10: More move-under-heading options. Bug fixes and performance improvements.\n2.1.9: performance improvements and better UI for iPhone users.\n2.1.8: various fixes and small improvements.\n2.1.7: various fixes and small improvements.\n2.1.6: allow all current timeblocks to be shown, not just the first. Add new @repeat()s if using the extended syntax from the Repeat Extensions plugin. Bug fixes.\n2.1.5: fixes to time blocks and scheduling items.\n2.1.4: fix to Interactive Processing, and Edit All Perspectives dialog now shows unsaved changes.\n2.1.3: fixes to display of timeblocks, and checklist counts being wrong. Slightly smarter task dialog.\n2.1.2: fix bug - Today section not showing up; fix settings description for max items to show.\n2.1.1: fix bug - Tomorrow section not showing up.\n2.1.0: Add Perspectives; new 'Current Time Block' section; new 'Last Week' section; and many other improvements and fixes.\n2.0.0: major new release with many new features, following major re-write.", "plugin.dependencies": [], "plugin.requiredFiles": ["react.c.WebView.bundle.dev.js"], "plugin.requiredSharedFiles": ["fontawesome.css", "light.min.flat4NP.css", "regular.min.flat4NP.css", "solid.min.flat4NP.css", "fa-light-300.woff2", "fa-regular-400.woff2", "fa-solid-900.woff2"], "plugin.__requiredSharedFiles": ["pluginToHTMLCommsBridge.js"], "plugin.script": "script.js", "plugin.url": "https://github.com/NotePlan/plugins/blob/main/jgclark.Dashboard/README.md", "plugin.commands": [{"name": "Show Dashboard", "description": "Show Dashboard", "jsFunction": "showDashboardReact", "hidden": false, "alias": ["sd"], "arguments": ["Sections to load (sectionCodes, comma-separated -- see README for list)", "perspectiveName"]}, {"hidden": true, "name": "showDashboard", "description": "alternative x-callback to show Dashboard", "jsFunction": "showDashboardReact", "arguments": ["Sections to load (sectionCodes, comma-separated -- see README for list)", "perspectiveName"]}, {"name": "Show Demo Dashboard", "description": "Show Demo Dashboard", "jsFunction": "showDemoDashboard", "hidden": false, "comment": "TODO: hide me in time", "alias": ["sdd"], "arguments": []}, {"hidden": false, "name": "Generate Diagnostics file", "description": "Generate Diagnostic Settings File", "jsFunction": "generateDiagnosticsFile"}, {"hidden": true, "name": "showSections", "description": "Show Dashboard Sections", "jsFunction": "showSections", "arguments": ["Sections to load (sectionCodes, comma-separated -- see README for list)"]}, {"hidden": true, "name": "showPerspective", "description": "Show (or switch to) a named Perspective", "jsFunction": "showPerspective", "arguments": ["perspectiveName"]}, {"hidden": true, "name": "setSetting", "description": "Set a single key:value setting", "jsFunction": "setSetting", "arguments": ["key", "value"]}, {"hidden": true, "name": "setSettings", "description": "Set multiple key:value settings", "jsFunction": "setSettings", "arguments": ["params as concatentaed list of key=value;"]}, {"hidden": false, "name": "generateTagMentionCache", "description": "generate tag cache for Dashboard", "jsFunction": "generateTagMentionCache", "alias": ["utc"]}, {"name": "Make Callback from Current Settings", "description": "Make a callback url or link for the current settings, and copy to the clipboard.", "jsFunction": "makeSettingsAsCallback", "hidden": false}, {"hidden": true, "name": "decideWhetherToUpdateDashboard", "description": "onEditorWillSave", "jsFunction": "decideWhetherToUpdateDashboard"}, {"hidden": true, "name": "refreshSectionByCode", "description": "Refresh section with section code", "jsFunction": "refreshSectionByCode", "arguments": ["section code to refresh"]}, {"hidden": true, "name": "onMessageFromHTMLView", "description": "React Window calling back to plugin", "jsFunction": "onMessageFromHTMLView"}, {"hidden": true, "name": "reactWindowInitialised", "description": "React Window sends this message from Dashboard.jsx when it has initialised", "jsFunction": "reactWindowInitialised"}, {"hidden": false, "comment": "TODO: remove me in time", "name": "onUpdateOrInstall", "description": "test: onUpdateOrInstall", "jsFunction": "onUpdateOrInstall"}, {"hidden": true, "name": "Update plugin settings", "description": "Settings interface (for iOS)", "jsFunction": "editSettings"}, {"hidden": true, "name": "Delete Perspective", "description": "Delete an existing Perspective.", "jsFunction": "deletePerspective"}, {"hidden": true, "name": "Delete all Perspective Settings", "description": "Delete all Perspective Settings.", "jsFunction": "deleteAllNamedPerspectiveSettings"}, {"hidden": true, "name": "externallyStartSearch", "description": "Start a new search and open its section. For use by x-callbacks or other plugins.", "jsFunction": "externallyStartSearch", "arguments": ["search terms string", "search over 'calendar', 'project', or 'both' (optional, default is 'both')", "ISO start date for calendar notes (optional, default is empty)", "ISO end date for calendar notes (optional, default is empty)"]}, {"hidden": false, "name": "updateTagMentionCache", "description": "update tag cache for Dashboard", "jsFunction": "updateTagMentionCache", "alias": ["utc"]}, {"hidden": false, "name": "testTagCache", "description": "test tag cache for Dashboard", "jsFunction": "testTagCache", "alias": ["ttc"]}, {"comment": "TODO: remove me in time", "name": "test Perspective Filter", "description": "show filtering results for a note", "jsFunction": "logPerspectiveFiltering", "hidden": false}], "plugin.commands.unused": [{"name": "Add new Perspective", "description": "Add new Perspective from current settings.", "jsFunction": "addNewPerspective", "hidden": false}, {"name": "Update current Perspective", "description": "Update the current Perspective defintion", "jsFunction": "updateCurrentPerspectiveDef", "hidden": false}, {"name": "buildListOfDoneTasksToday", "description": "test: buildListOfDoneTasksToday", "jsFunction": "buildListOfDoneTasksToday", "hidden": false}, {"name": "Dashboard: update plugin settings", "description": "Settings interface (even for iOS)", "jsFunction": "editSettings"}], "plugin.settings": [{"type": "hidden", "key": "pluginID", "default": "jgclark.Dashboard", "COMMENT": "This is for use by the editSettings helper function. PluginID must match the plugin.id in the top of this file"}, {"type": "hidden", "key": "dashboardSettings", "description": "Saves last state of dashboardSettings as JSON string.", "required": true, "default": "{}"}, {"type": "hidden", "key": "perspectiveSettings", "description": "Saves current state of Perspective definitions as JSON string.", "required": true, "default": "[]"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "type": "string", "title": "Log Level", "choices": ["DEV", "DEBUG", "INFO", "WARN", "ERROR", "none"], "description": "Set how much logging output will be displayed when executing Tidy commands in NotePlan Plugin Console Logs (NotePlan -> Help -> Plugin Console)\n\n - DEBUG: Show All Logs\n - INFO: Only Show Info, Warnings, and Errors\n - WARN: Only Show Errors or Warnings\n - ERROR: Only Show Errors\n - none: Don't show any logs", "default": "INFO", "required": true}, {"key": "_logFunctionRE", "title": "Regex for Functions to show in debug log", "description": "Overrides the Log Level above if this regex matches the first argument in log*() calls. If not set, has no effect.", "type": "string", "default": "", "required": false}, {"key": "_logTimer", "title": "Enable Timer logging?", "description": "For plugin authors to help optimise the plugin.", "type": "bool", "default": false, "required": true}, {"type": "heading", "title": "All the rest of the Settings have been migrated to the Dashboard Window, so please make your changes there."}]}