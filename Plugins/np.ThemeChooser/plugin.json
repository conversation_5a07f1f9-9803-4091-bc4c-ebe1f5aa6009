{"COMMENT": "Details on these fields: https://help.noteplan.co/article/67-create-command-bar-plugins", "macOS.minVersion": "10.13.0", "noteplan.minAppVersion": "3.7.2", "plugin.id": "np.<PERSON><PERSON><PERSON><PERSON>r", "plugin.name": "🎨 Theme Chooser", "plugin.version": "1.9.1", "plugin.lastUpdateInfo": "1.9.1 Add support for <PERSON>'s hex colors in frontmatter.\nPreviously: Add color picker to choose colors and write to frontmatter", "plugin.description": "Choose from your favorite themes", "plugin.author": "dwer<PERSON><PERSON><PERSON>", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.url": "https://github.com/NotePlan/plugins/blob/main/np.ThemeChooser/README.md", "plugin.commands": [{"name": "Choose <PERSON>", "description": "Choose from all installed themes", "jsFunction": "chooseTheme", "alias": ["themechooser"], "arguments": ["theme name"]}, {"name": "Set Default Light/Dark Theme (for this device)", "description": "Choose from all themes", "jsFunction": "setDefaultLightDarkTheme"}, {"name": "Toggle Light/Dark", "description": "from Preset Themes", "jsFunction": "toggleTheme", "alias": ["themechooser"], "arguments": ["theme name"]}, {"name": "Choose HTML Color for Background", "description": "Choose a color and either save to the clipboard or write to frontmatter", "jsFunction": "chooseColor", "alias": ["color", "picker"], "arguments": ["default color value (e.g. selected text)"]}, {"name": "setFrontmatterColor", "description": "Command comes from the HTML Color Picker Window", "jsFunction": "setFrontmatterColor", "alias": [], "arguments": ["color to set", "key to set (e.g. 'bg-color' or 'bg-color-dark')"], "hidden": true}, {"name": "Change Theme Preset", "description": "Add/Change a theme preset to another theme", "jsFunction": "changePreset"}, {"name": "addStyle", "description": "Choose Style from Master Stylesheet", "jsFunction": "copyThemeStyle", "alias": [], "arguments": [], "hidden": true}, {"name": "removeStyle", "description": "Remove Style from Master Stylesheet", "jsFunction": "removeStyle", "alias": [], "arguments": [], "hidden": true}, {"name": "Edit a Theme Style Attribute", "description": "Choose Style from Custom Theme", "jsFunction": "editStyleAttribute", "alias": [], "arguments": ["the path (e.g. styles.editor.backgroundColor) to the style attribute you want to edit", "the input type (e.g. text, color, boolean, number)"], "hidden": true}, {"name": "Copy Currently Active Theme", "description": "", "jsFunction": "copyCurrentTheme", "alias": [], "arguments": [], "hidden": true}, {"name": "onOpenTheme", "description": "", "jsFunction": "onOpenTheme", "hidden": true}, {"name": "onOpenRefreshPage", "description": "", "jsFunction": "onOpenRefreshPage", "hidden": true}, {"name": "onEdit", "description": "", "jsFunction": "onEdit", "hidden": true}, {"name": "Customize Themes", "description": "Copy and edit themes", "jsFunction": "createThemeSamples", "alias": [], "arguments": []}, {"name": "setColor", "description": "HTML callback to set color (hidden)", "jsFunction": "setColor", "alias": [], "arguments": [], "hidden": true}, {"name": "setTheme", "description": "Change the theme based on frontmatter field 'theme'", "jsFunction": "changeThemeFromFrontmatter", "alias": ["themefromfrontmatter"], "arguments": ["frontmatter key"], "hidden": true}, {"name": "Add/Change This Note’s Theme in Frontmatter", "description": "Add frontmatter fields to have this note displayed using a specific theme", "jsFunction": "addThemeFrontmatter", "alias": [], "arguments": ["Theme name"]}, {"name": "Theme Chooser: Set Preset 01", "description": "Switch Theme", "jsFunction": "runPreset01", "isPreset": true, "hidden": true}, {"name": "Theme Chooser: Set Preset 02", "description": "Switch Theme", "jsFunction": "runPreset02", "isPreset": true, "hidden": true}, {"name": "Theme Chooser: Set Preset 03", "description": "Switch Theme", "jsFunction": "runPreset03", "isPreset": true, "hidden": true}, {"name": "Theme Chooser: Set Preset 04", "description": "Switch Theme", "jsFunction": "runPreset04", "isPreset": true, "hidden": true}, {"name": "Theme Chooser: Set Preset 05", "description": "Switch Theme", "jsFunction": "runPreset05", "isPreset": true, "hidden": true}], "plugin.settings": [{"COMMENT": "Plugin settings documentation: https://help.noteplan.co/article/123-plugin-configuration", "type": "heading", "title": "Theme Cho<PERSON><PERSON>s"}, {"key": "addAutoRefreshFM", "title": "Add Auto-Refresh", "description": "Adds Frontmatter to refresh page on open. Slows down changes, but has some benefits so you can see the changes you made.", "type": "bool", "default": true, "required": true}, {"type": "separator", "title": "Theme Cho<PERSON><PERSON>s"}, {"type": "heading", "title": "No need to change anything here. Presets can be set by running the command:"}, {"type": "heading", "title": "/Change Theme Preset"}, {"type": "heading", "title": "or"}, {"type": "heading", "title": "/Toggle Light/Dark"}, {"type": "separator", "title": "Theme Cho<PERSON><PERSON>s"}, {"key": "runPreset01", "type": "hidden", "default": "", "title": "Preset 01", "description": "Do not change this setting manually. Use the \"/Change Theme Preset\" command."}, {"key": "runPreset01", "type": "hidden", "default": "", "title": "Preset 01", "description": "Do not change this setting manually. Use the \"/Change Theme Preset\" command."}, {"key": "runPreset02", "type": "hidden", "default": "", "title": "Preset 01", "description": "Do not change this setting manually. Use the \"/Change Theme Preset\" command."}, {"key": "runPreset03", "type": "hidden", "default": "", "title": "Preset 01", "description": "Do not change this setting manually. Use the \"/Change Theme Preset\" command."}, {"key": "runPreset04", "type": "hidden", "default": "", "title": "Preset 01", "description": "Do not change this setting manually. Use the \"/Change Theme Preset\" command."}, {"key": "runPreset05", "type": "hidden", "default": "", "title": "Preset 01", "description": "Do not change this setting manually. Use the \"/Change Theme Preset\" command."}, {"NOTE": "DO NOT CHANGE THE FOLLOWING SETTINGS; ADD YOUR SETTINGS ABOVE ^^^", "type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "type": "string", "title": "Log Level", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "description": "Set how much logging output will be displayed when executing Math Solver commands in NotePlan Plugin Console Logs (NotePlan -> Help -> Plugin Console)\n\n - DEBUG: Show All Logs\n - INFO: Only Show Info, Warnings, and Errors\n - WARN: Only Show Errors or Warnings\n - ERROR: Only Show Errors\n - none: Don't show any logs", "default": "INFO", "required": true}]}