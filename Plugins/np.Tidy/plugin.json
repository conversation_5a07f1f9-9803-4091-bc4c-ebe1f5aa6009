{"COMMENT": "Details on these fields: https://help.noteplan.co/article/67-create-command-bar-plugins", "macOS.minVersion": "10.13.0", "noteplan.minAppVersion": "3.9.11", "plugin.id": "np.Tidy", "plugin.name": "🧹 Tidy Up", "plugin.author": "jgclark", "plugin.description": "Tidy up and delete various things in your NotePlan notes", "plugin.version": "0.14.7", "plugin.lastUpdateInfo": "v0.14.7: fix regression in '/generate @repeats from recent notes' command.\nv0.14.6: fix to allow top-level tasks to be run by xcallback.\nv0.14.5: fix to allow top-level tasks to be run by xcallback.\nv0.14.4: fix to allow blank Calendar notes to be removed by '/remove blank notes'.\nv0.14.3: fix unwanted popups in '/generate @repeats from recent notes' command.\nv0.14.2: add new option to file root notes.\nv0.14.1: rebuild.\nv0.14.0: new '/Generate repeats' command.\nv0.13.0: '/List conflicted notes' now clears out all copies, and offers side-by-side viewing of conflicted note versions. Also bug fixes.\nv0.12.1: '/List conflicted notes' now covers Calendar notes as well.\nv0.12.0: add more capability to '/List conflicted notes'.\nv0.11.0: new command '/find doubled notes'.\nv0.10.0: fix bug in moving top level tasks, and adds support for indented tasks.", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.url": "https://github.com/NotePlan/plugins/blob/main/np.Tidy/README.md", "plugin.changelog": "https://github.com/NotePlan/plugins/blob/main/np.Tidy/CHANGELOG.md", "plugin.commands": [{"name": "Tidy Up", "description": "Run as many of the other commands in this plugin as you have configured.", "jsFunction": "tidyUpAll", "alias": ["tua", "tidy"]}, {"name": "File root-level notes", "description": "For each root-level note, asks which folder you'd like it moved to. (There's a setting for ones to ignore.)", "jsFunction": "fileRootNotes", "alias": ["frln", "tidy"]}, {"name": "Generate @repeats in recent notes", "description": "Generate @repeats from recently-updated notes, using the extended data interval syntax from the Repeat Extensions plugin.", "jsFunction": "generateRepeatsFromRecentNotes", "alias": ["grrn", "tidy"]}, {"name": "List conflicted notes", "alias": ["conflicts"], "description": "Creates/updates a note that lists all notes on this device that have conflicts. (Requires NP v3.9.3)", "jsFunction": "listConflicts"}, {"name": "List duplicate notes", "alias": ["dupes"], "description": "Creates/updates a note that lists potentially duplicate notes because they have identical titles", "jsFunction": "listDuplicates"}, {"name": "List doubled notes", "alias": ["doubles"], "description": "Creates/updates a note that lists notes that potentially have doubled content (internal duplication)", "jsFunction": "listPotentialDoubles"}, {"name": "openCalendarNoteInSplit", "hidden": true, "description": "callback entry point for double-finder", "jsFunction": "openCalendarNoteInSplit", "arguments": ["filename", "point in file for cursor to be moved to"]}, {"name": "List stubs", "description": "Creates/updates a note that lists all your notes that have note links (wikilinks) that lead nowhere", "jsFunction": "listStubs"}, {"name": "Move top-level tasks in Editor to heading", "description": "Tasks at top of active note (prior to any heading) will be placed under a specified heading", "jsFunction": "moveTopLevelTasksInEditor", "alias": ["mtth", "Tidy: Move top-level tasks in Editor to heading"], "arguments": ["Heading name to place the tasks under (will be created if doesn't exist). If you are running this command in a template, put any non-blank text in the field below.", "Run silently (e.g. in a template). Default is false.", "Return the content of the tasks text, rather than inserting under a heading (e.g. for inserting in a tempate)", "Is this running from a template? You should set this to true if you are running this command from a template."]}, {"name": "Remove @done() markers", "description": "Remove @done() markers from recently-updated notes. Can be used with parameters from Template or Callback.", "jsFunction": "removeDoneMarkers", "alias": ["rdm", "tidy"], "arguments": ["Parameters"]}, {"name": "Remove blank notes", "description": "Remove notes that are completely empty, or just have a `#` character", "jsFunction": "removeBlankNotes", "alias": ["rbn", "tidy"], "arguments": ["Parameters"]}, {"name": "Remove orphaned blockIDs", "description": "Remove blockIDs from lines that had been sync'd, but are 'orphans' as the other copies of the blockID have since been deleted.", "jsFunction": "removeOrphanedBlockIDs", "alias": ["rob", "tidy"], "arguments": ["Parameters"]}, {"name": "Remove time parts from @done() dates", "description": "Remove time parts of @done(date time) from recently-updated notes. Can be used with parameters from Template or Callback.", "jsFunction": "removeDoneTimeParts", "alias": ["rtp", "tidy"], "arguments": ["Parameters"]}, {"name": "Remove section from recent notes", "description": "Remove a given section (both the heading and its content) from recently-changed notes.\nCan be used with parameters from Template or x-callback.", "jsFunction": "removeSectionFromRecentNotes", "alias": ["rsrn", "tidy"], "arguments": ["Parameters"]}, {"name": "Remove section from all notes", "description": "Remove a given section (both the heading and its content) from all notes.\nCan be used with parameters from Template or x-callback.", "jsFunction": "removeSectionFromAllNotes", "alias": ["rsan", "tidy"], "arguments": ["Parameters"]}, {"name": "Remove >today tags from completed todos", "description": "Remove Completed todos that have a >today tag. Can be used with parameters from Template or Callback.", "jsFunction": "removeTodayTagsFromCompletedTodos", "alias": ["rmt", "remove<PERSON><PERSON>y"]}, {"name": "Remove triggers from recent calendar notes", "description": "Remove one or more triggers from recent (but past) calendar notes.\nCan be used with parameters from Template or x-callback.", "jsFunction": "removeTriggersFromRecentCalendarNotes", "alias": ["rtcn", "tidy"], "arguments": ["Parameters"]}, {"name": "resolveConflictWithCurrentVersion", "hidden": true, "description": "x-callback entry for resolveConflictWithCurrentVersion", "jsFunction": "resolveConflictWithCurrentVersion", "arguments": ["noteType", "filename"]}, {"name": "resolveConflictWithOtherVersion", "hidden": true, "description": "x-callback entry for resolveConflictWithOtherVersion", "jsFunction": "resolveConflictWithOtherVersion", "arguments": ["noteType", "filename"]}, {"name": "openConflictSideBySide", "hidden": true, "description": "x-callback entry for openConflictSideBySide", "jsFunction": "openConflictSideBySide", "arguments": ["mainFilename", "copyFilename"]}, {"name": "Log notes changed in interval", "description": "Write a list of notes changed in the last interval of days to the plugin console log. It will default to the 'Default Recent Time Interval' setting unless passed as a parameter.", "jsFunction": "logNotesChangedInInterval", "alias": ["lncii", "tidy"], "arguments": ["Parameters"]}, {"name": "Update plugin settings", "description": "Settings interface (even for iOS)", "jsFunction": "updateSettings"}], "plugin.commands_disabled": [{"name": "onOpen", "description": "Trigger, not a user command", "jsFunction": "onOpen", "hidden": true}, {"name": "onEditorWillSave", "description": "Trigger, not a user command", "jsFunction": "onEditorWillSave", "hidden": true}], "plugin.settings": [{"type": "heading", "title": "'/File root-level notes' command settings"}, {"title": "Which root notes to ignore?", "key": "rootNotesToIgnore", "description": "Comma-separated list of note titles that you need or want to leave in the root folder. These will be ignored when running the command.\nNote: the '# ' is not part of the title, so don't include it.", "type": "[string]", "default": [], "required": false}, {"type": "separator"}, {"type": "heading", "title": "'/List ... commands settings"}, {"key": "listFoldersToExclude", "title": "Folders to exclude for /List ... commands", "description": "List of folders to ignore in the '/List ...' commands, plus any sub-folders. May be empty. (Default: 'Saved Searches, @Archive, @Templates'). Notes:\n- @Trash is automatically excluded.\n- To exclude the root folder add '/' (which doesn't include its sub-folders).", "type": "[string]", "default": ["Saved Searches", "@Archive", "@Templates"], "required": false}, {"key": "conflictedNoteFilename", "title": "/List conflicted notes: Filepath for results note", "description": "The filepath (including any NP folders) of which NotePlan note to use. (Default: 'Conflicted Notes.md')", "type": "string", "default": "Conflicted Notes.md", "required": true}, {"title": "Save a copy of previous version as a separate note?", "key": "savePreviousVersion", "description": "If there's enough difference in the conflicted version, it can be difficult to reconcile differences, particularly on an iOS device. If true this will save a copy to '@Conflicted Copies' folder, to allow you to compare and edit using external editors. Note: you will need to clear up after yourself!", "type": "bool", "default": false, "required": true}, {"key": "duplicateNoteFilename", "title": "/List duplicate notes: Filepath for results", "description": "The filepath (including any NP folders) of which NotePlan note to use. (Default: 'Duplicate Notes.md')", "type": "string", "default": "Duplicate Notes.md", "required": true}, {"key": "doubledNoteFilename", "title": "/List doubled notes: Filepath for results", "description": "The filepath (including any NP folders) of which NotePlan note to use. (Default: 'Possible Doubled Notes.md')", "type": "string", "default": "Possible Doubled Notes.md", "required": true}, {"key": "stubsNoteFilename", "title": "/List stubs: filepath for results note", "description": "The filepath (including any NP folders) of which NotePlan note to use. (Default: 'Stubs.md')", "type": "string", "default": "Stubs.md", "required": true}, {"type": "separator"}, {"type": "heading", "title": "'/Remove ... from recent ...' command settings"}, {"title": "How many days count as recent?", "key": "numDays", "type": "number", "description": "The number of days to look back for 'recently changed' notes. If this is 0 or empty, then all notes will be checked.", "default": 7, "required": false}, {"key": "removeFoldersToExclude", "title": "Folders to exclude", "description": "List of folders to exclude in the '/Remove ...' commands. May be empty. (Default: 'Saved Searches, @Archive, @Templates').Notes:\n- @Trash is automatically excluded.\n- To exclude the root folder add '/' (which doesn't include its sub-folders).", "type": "[string]", "default": ["Saved Searches", "@Archive", "@Templates"], "required": false}, {"title": "Just remove @done(...) markers from checklists?", "key": "justRemoveFromChecklists", "description": "When removing @done(...) markers, remove just from done checklist items, not done tasks as well? (This is relevant when calculating heatmaps of when tasks but not checklists are completed in Summaries plugin.)", "type": "bool", "default": true, "required": true}, {"type": "heading", "title": "'/Remove section...' command settings"}, {"title": "Type of match for section headings", "key": "matchType", "description": "The 'Starts with' setting allows headings that always start the same (e.g. 'Habit Progress') to be matched, even if the end of the heading changes (e.g. 'Habit Progress for Tuesday').", "type": "string", "choices": ["Exact", "Starts with", "Contains"], "default": "Exact", "required": true}, {"type": "heading", "title": "'/Move top-level tasks in Editor...' command settings"}, {"title": "Heading name for '/Move top-level tasks in Editor to heading' command", "key": "moveTopLevelTasksHeading", "description": "When \"/Move top-level tasks in Editor to heading\" is run, this is the name of the heading to move the tasks under. If the heading doesn't exist in the current Editor note, it will be created.\nNOTE: If you leave this blank, you will be prompted for the heading to move the tasks under at run-time.", "type": "string", "default": "Tasks", "required": false}, {"type": "separator"}, {"type": "heading", "title": "'/Tidy Up' command: which commands to run?"}, {"title": "Run commands silently?", "key": "runSilently", "description": "When running commands silently, they will run entirely in the background and not pop up dialogs to check or report success. Only turn this on when you're comfortable that the commands are doing what you expect.\nNote: If you run in this mode, then details will be written to the Plugin Console at level 'INFO' instead.", "type": "bool", "default": false, "required": true}, {"title": "Run '/Generate @repeats from recent notes' command?", "key": "runGenerateRepeatsCommand", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/List conflicted notes' command?", "key": "runConflictFinderCommand", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/List duplicate notes' command?", "key": "runDuplicateFinderCommand", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/Remove blank notes' command?", "key": "runRemoveBlankNotes", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/Remove orphaned blockIDs' command?", "key": "runRemoveOrphansCommand", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/Remove @done() markers' command?", "key": "runRemoveDoneMarkersCommand", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/Remove time parts from @done() dates' command?", "key": "runRemoveDoneTimePartsCommand", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/Remove >today tags from completed todos?", "key": "removeTodayTagsFromCompletedTodos", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/Remove triggers from recent calendar notes?", "key": "removeTriggersFromRecentCalendarNotes", "description": "", "type": "bool", "default": false, "required": true}, {"title": "Run '/Move top-level tasks in Editor to heading?", "key": "moveTopLevelTasksInEditor", "description": "Note: this command does not work like the other commands inside a template (e.g. your daily template). See plugin README for how to run this command in a daily template.", "type": "bool", "default": false, "required": true, "arguments": ["Heading name to place the tasks under (will be created if doesn't exist). If you are running this command in a template, put any non-blank text in the field below.", "Run silently (e.g. in a template). Default is false.", "Return the content of the tasks text, rather than inserting under a heading (e.g. for saving to a variable and inserting into a template). This must be true if running from a template."]}, {"type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "type": "string", "title": "Log Level", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "description": "Set how much logging output will be displayed when executing Tidy commands in NotePlan Plugin Console Logs (NotePlan -> Help -> Plugin Console)\n\n - DEBUG: Show All Logs\n - INFO: Only Show Info, Warnings, and Errors\n - WARN: Only Show Errors or Warnings\n - ERROR: Only Show Errors\n - none: Don't show any logs", "default": "INFO", "required": true}], "plugin.settings_disabled": [{"title": "Run '/Remove section from recent notes' command?", "key": "runRemoveSectionFromNotesCommand", "ddescription": "Note: the '/Remove section from all notes' command is deliberately not available, given how dangerous it could be.", "type": "bool", "default": false, "required": true}]}