{"macOS.minVersion": "10.13.0", "noteplan.minAppVersion": "3.6", "plugin.id": "dwertheimer.EventAutomations", "plugin.name": "🗓 AutoTimeBlocking / Events", "plugin.description": "Various Event Automations:\n- Automatically find time in your calendar and create Time Blocks for items marked for today,\n- Write out synced copies of Today's todos (without the AutoTimeBlocking), and\n- Create calendar events for all text items under a specific heading", "plugin.author": "dwer<PERSON><PERSON><PERSON>", "plugin.version": "1.21.0", "plugin.lastUpdateInfo": "1.21.0: Add Manual Ordering mode (orders todos in today's note by the order in the note) -- feature request from @Thor\n Previously:\n- Add timeframes tags to have tasks placed in a time of day (see readme). Also Sort todos by priority then date (oldest to newest), then duration. Bug fix: Ignore backlinks which are not todos", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.url": "https://noteplan.co/n/#/1EF12392-B544-4044-AC7A-428F57EB2DFC", "plugin.changelog": "https://github.com/NotePlan/plugins/blob/main/dwertheimer.EventAutomations/CHANGELOG.md", "plugin.commands": [{"name": "Event Automations: Update Plugin Settings", "description": "Preferences", "jsFunction": "editSettings"}, {"name": "atb - Create AutoTimeBlocks for >today's Tasks", "description": "Read >today todos and insert them into today's calendar note as timeblocks", "jsFunction": "insertTodosAsTimeblocks", "alias": ["atb", "abt", "timeblocks", "block", "todoblocks"]}, {"name": "mdatb", "description": "Mark task on current line done and run /atb to re-create timeblocks", "jsFunction": "markDoneAndRecreateTimeblocks", "alias": ["done", "mark"]}, {"name": "Create AutoTimeBlocks using presets", "description": "Read >today todos and insert them into today's calendar note as timeblocks, but using presets defined in _configuration note", "jsFunction": "insertTodosAsTimeblocksWithPresets", "alias": ["atbp", "abtp", "tbp"]}, {"name": "Insert Synced Todos for Open Calendar Note", "description": "Output a list of todos for the day open in the editor (any todos in the References pane will show up -- either >dated or >today if you're on today's note)", "jsFunction": "insertSyncedCopiesOfTodayTodos", "alias": ["syncedTodos", "insertSynced"], "arguments": ["'Yes' to Pass back synced todos as text (e.g. for inserting in a template)."]}, {"name": "Remove Time Blocks for Open Calendar Note", "description": "Remove the contents of Time Blocks created by this plugin", "jsFunction": "removeTimeBlocks", "alias": ["remTimeBlocks"]}, {"name": "Remove All Previous Time Blocks in Calendar Notes Written by this Plugin", "description": "<PERSON><PERSON><PERSON> previously written Time Blocks", "jsFunction": "removePreviousTimeBlocks", "alias": ["remAllTimeBlocks"], "arguments": ["Run silently with no pop-up messages (e.g. running from a Template) - (yes/no) - yes to run silently"]}, {"name": "cevt - Create Events From Text under heading", "description": "Create calendar events by writing (natural language) text under a heading", "jsFunction": "createEvents", "alias": ["cevt", "createevents"], "arguments": ["Heading under which events are written", "Ask for confirmation on choices? yes or no for best guess", "Calendar name to write events to (leave blank to be prompted)"]}, {"name": "pevt - Prompt for Natural Language Event text", "description": "Create calendar events by writing (natural language) via prompt", "jsFunction": "createEventPrompt", "alias": ["pevt", "promptevents"], "arguments": ["Name of the heading to place the created event under"]}, {"name": "onEditorWillSave", "description": "onEditorWillSave", "jsFunction": "onEditorWillSave", "alias": [], "arguments": [], "hidden": true}], "plugin.settings": [{"type": "hidden", "key": "plugin_ID", "default": "dwertheimer.EventAutomations"}, {"type": "heading", "title": "AutoTimeBlocking Settings: General"}, {"key": "defaultDuration", "type": "number", "title": "Default time block duration (minutes)", "description": "Any task that does not have a specific duration stated (see below) will have this default length in minutes", "default": 15, "required": true}, {"key": "includeAllTodos", "title": "Include all todos in today's note", "description": "If checked, all open todos in today's note will be considered for timeblocks (time permitting). If unchecked, only todos marked with >today or >dated with today's date will be included. NOTE: if you use the todo character (e.g. '*') for your TimeBlocks leading character, this could cause issues.", "type": "bool", "default": true, "required": true}, {"key": "allowEventSplits", "type": "bool", "title": "Allow tasks to be split", "description": "Allow tasks to be split into multiple time blocks", "default": false, "required": true}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "title": "Task duration marker", "description": "The character that signifies the start of a task duration (e.g. in the task: \n* do something '2h44m\nThe duration marker is the apostrophe", "default": "'", "choices": ["'", "~", "^", ";", "%", "$", "+"], "required": true}, {"key": "todoChar", "type": "string", "validation": "^(?!(?:.*\\*){2})[\\*|\\-|\\+|#{1,}]+$", "choices": ["*", "-", "+", "#", "##", "###", "####"], "title": "Time block leading character", "description": "For NotePlan to recognize a line as a Time Block, the leading character must be one of: a '*', a '-', a '+' (for a checklist box) or one or more '#'s. AutoTimeBlocking will use this to create your timeblocks.", "default": "+", "required": true}, {"key": "checkedItemChecksOriginal", "title": "Checking a checklist item marks the original item done", "description": "Checking off a checklist AutoTimeBlocked line item will find and check the original task. To make this work, the onEditorWillSave trigger/frontmatter will be automatically added by the plugin.\nIMPORTANT NOTE: Only applies if:\n(1) you use the “+” for your Time Block Leading Character and\n(2) the 'Include links to task location in time blocks' is set to 'Pretty Links'", "type": "bool", "default": false, "required": true}, {"key": "timeBlockTag", "type": "string", "title": "Unique AutoTimeBlock #tag (cannot be empty)", "description": "The tag that will be used to identify lines created the AutoTimeBlock plugin. This tag can be #(anything), but should be not ever used by you in other places, because the plugin deletes lines with this tag before updating them.", "default": "#🕑", "required": true}, {"key": "removeDuration", "type": "bool", "title": "Remove duration from time block", "description": "The plugin uses your duration designation (e.g. '2h44m) to determine how long a task should take. But you probably don't want that duration to show on the calendar. If you do want to see it, uncheck this item.", "default": true, "required": true}, {"key": "timeBlockHeading", "type": "string", "validation": "^[^#+].*", "title": "Heading for time blocks in note", "description": "If this heading exists in the open note when running the AutoTimeBlock command, the generated time blocks will be placed under it. If you leave it blank, the time blocks will be placed at the top of the note. Text only -- should *not* include any #'s at the beginning of the heading.", "default": "[Time Blocks](noteplan://runPlugin?pluginID=dwertheimer.EventAutomations&command=atb%20-%20Create%20AutoTimeBlocks%20for%20%3Etoday%27s%20Tasks)", "required": true}, {"key": "foldTimeBlockHeading", "type": "bool", "title": "Fold created time blocks under heading", "description": "Reduce the noise on your calendar page by folding the time blocks that get created under the heading. NOTE: Due to some recent changes in NotePlan, Timeblocks under folded headings don't display. So this option is false by default.", "default": false, "required": true}, {"key": "workDayStart", "type": "string", "validation": "^\\d{2}:\\d{2}$", "title": "Start of work day", "description": "If set to anything other than 00:00, no time blocks will be placed before this time. needs to be in 24 hour format (two digits, leading zero, colon in between).", "default": "00:00", "required": true}, {"key": "workDayEnd", "type": "string", "validation": "^\\d{2}:\\d{2}$", "title": "End of work day", "description": "If set to anything other than 23:59, no time blocks will be allowed to extend past this time. needs to be in 24 hour format (two digits, leading zero, colon in between).", "default": "23:59", "required": true}, {"type": "separator"}, {"type": "heading", "title": "Synced Co<PERSON>"}, {"key": "createSyncedCopies", "type": "hidden", "title": "Automatically create synced copies of References items when ATB runs", "description": "When /atb (AutoTimeBlocking) command is run, write a synced line (copy) of the Reference item in the calendar note. Can look a little repetitive, but it will save you time when you want to edit the underlying item.", "default": false, "required": true}, {"key": "syncedCopiesTitle", "type": "string", "title": "Title/heading for synced copies of today's items", "description": "When you generate synced copies of tasks, they will be placed under this heading in the calendar note", "default": " [Today's Synced Tasks](noteplan://x-callback-url/runPlugin?pluginID=dwertheimer.EventAutomations&command=Insert%20Synced%20Todos%20for%20Open%20Calendar%20Note)", "required": true}, {"key": "foldSyncedCopiesHeading", "type": "bool", "title": "<PERSON><PERSON> created synced copies under heading", "description": "Reduce the noise on your calendar page by folding the synced copy lines that get created under the heading.", "default": false, "required": true}, {"type": "separator"}, {"type": "heading", "title": "Filters"}, {"key": "includeTasksWithText", "type": "[string]", "title": "Include any tasks that match text:", "description": "If this field is set to any text, then any task that contains this text will be included in the timeblocks. This is useful if you want to limit tasks to items with a specific #hashtag for example. This field can also contain a comma separated list, e.g. 'someRawText, #hashtag1, #hashtag2'", "default": [], "required": false}, {"key": "excludeTasksWithText", "type": "[string]", "title": "Exclude any tasks that match text:", "description": "If this field is set to any text, then any task that contains this text will *not* be included in the timeblocks. This field can also contain a comma separated list, e.g. 'someRawText, #hashtag1, @hashtag2'", "default": [], "required": false}, {"type": "separator"}, {"type": "heading", "title": "Include Links"}, {"type": "string", "title": "Include links to task location in time blocks", "key": "includeLinks", "choices": ["OFF", "[[internal#links]]", "Pretty Links"], "default": "Pretty Links", "description": "Appends a link to the original location of a particular task. Can create a standard internal NotePlan link, e.g. [[internal#links]], or if you want to reduce clutter, a Pretty Link which will display only a single character (see below)."}, {"type": "string", "title": "Link text/char (if <PERSON> Links selected above)", "key": "linkText", "default": "📄", "required": true, "description": "If <PERSON> Links is on, this it the character the link will display."}, {"type": "separator"}, {"type": "heading", "title": "Advanced: Other Settings"}, {"key": "intervalMins", "type": "number", "title": "Time block start interval", "description": "Time blocks can only start every N minutes as dictated by this setting. For instance, if you want time blocks to only start every 15 minutes, set this to 15. This means you would at maximum have 4 time blocks per hour.", "default": 5, "required": true}, {"key": "mode", "type": "string", "title": "Task->Time Block processing mode", "choices": ["PRIORITY_FIRST", "LARGEST_FIRST", "BY_TIMEBLOCK_TAG", "MANUAL_ORDERING"], "description": "PRIORITY_FIRST places the highest priority (most !'s) first (if there's a slot) and then continues down the priority stack. LARGEST_FIRST tries to place the longest/largest duration item first. BY_TIMEBLOCK_TAG will try to slot items into a pre-existing timeblock that matches a tag on the task (e.g. a pre-existing timeblock called \"production\" and a task \"* do something #production\"). Will then fall back to PRIORITY_FIRST if no matching timeblock is found. MANUAL_ORDERING limits tasks to ones in today's note and orders them in the order they appear in the note.", "default": "PRIORITY_FIRST", "required": true}, {"key": "orphanTagggedTasks", "title": "What to do with orphaned tagged timeblock tasks?", "description": "(this is only relevant when the setting above is set to BY_TIMEBLOCK_TAG). When placing tasks into timeblocks of matching tag, what should happen when there are more tasks than will fit in the matching time block?", "type": "string", "default": "OUTPUT_FOR_INFO (but don't schedule them)", "required": true, "choices": ["IGNORE_THEM", "OUTPUT_FOR_INFO (but don't schedule them)", "SCHEDULE_ELSEWHERE_LAST", "SCHEDULE_ELSEWHERE_FIRST"]}, {"type": "separator"}, {"type": "heading", "title": "AutoTimeBlocking Settings: Presets"}, {"key": "presets", "type": "json", "title": "Presets to temporarily override settings above", "description": "Presets are useful for changing some of the settings above but just for certain runs of the plugin (e.g. on the weekend, etc.). Read the documentation for how to use presets. Use Jsonlint.com to check changes you make in this blank for errors.", "default": "[\n{\n\t\"label\":\"Limit Time Blocks to Work Hours\",\n\t\"workDayStart\":\"08:00\",\n\t\"workDayEnd\":\"17:59\"\n},\n{\n\t\"label\":\"Create Timeblocks on Calendar\",\n\t\"todoChar\":\"*\"\n}\n]", "required": false}, {"key": "timeframes", "type": "json", "title": "Time-of-day Timeframes", "description": "When you are operating in 'BY_TIMEBLOCK_TAG' mode, you can have a task slotted into a particular part of the day by tagging it with a timeframe matching one in the above list. Each timeframe has a name (names can be whatever you want), a start and a stop time.\nSo, if you have a task '* read book #late`, /atb will attempt to place it in the 'late' slot. Read the documentation for more on how to use this feature. Use Jsonlint.com to check changes you make in this blank for errors.", "default": "{\n\t\"early\": [\"07:00\",\"08:30\"],\n\t\"morning\": [\"08:30\",\"12:00\"],\n\t\"afternoon\": [\"12:00\",\"18:00\"],\n\t\"evening\": [\"18:00\",\"22:00\"],\n\t\"late\": [\"22:00\",\"23:00\"]\n}", "required": false}, {"type": "separator", "COMMENT": "---------- EVENT BLOCKS SETTINGS ----------"}, {"type": "heading", "title": "Event Blocks Settings"}, {"title": "Confirm when there are multiple options for what a date should be?", "key": "confirm", "type": "bool", "description": "Text you enter is processed and <PERSON><PERSON><PERSON> makes a guess at what you mean. If <PERSON><PERSON><PERSON> is not sure, it will give you choices. If you uncheck this, <PERSON><PERSON><PERSON> will just use the first choice it finds (which may or may not be what you wanted).", "default": true}, {"title": "Default event length (in mins) for items which have no end time", "key": "event<PERSON>ength", "type": "string", "choices": ["5", "15", "20", "30", "60", "90", "120"], "description": "If you enter 'Do something at 3pm', how long (in minutes) should that calendar item be?", "default": "30"}, {"title": "Remove date text from content", "key": "removeDateText", "type": "bool", "description": "If selected, the text pertaining to date/time (e.g. 'Friday at 9am') will be removed from the text line when the event is created. Leaving this unchecked and the following setting checked is a good way to double-check the events get created at the proper date/time.", "default": false}, {"title": "Show Event Created Date/Time", "key": "showResultingTimeDate", "type": "bool", "description": "If selected, the start time/date of the calendar event which was created will (temporarily) be displayed in the calendar event link. This is a good way to double-check that the computer understood what you meant in your text. Note: This text (but not the event link) goes away when the note is reloaded.", "default": true}, {"COMMENT": "Commenting this out for now because NP rewrites the text", "title": "Calendar link text", "key": "linkText", "type": "hidden", "description": "Use this text for the short link to the calendar item. Leave blank for just the date text.", "default": "🔗"}, {"type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "type": "string", "title": "Log Level", "choices": ["DEBUG", "LOG", "WARN", "ERROR", "none"], "description": "Set how much output will be displayed for this plugin the NotePlan > Help > Plugin Console. DEBUG is the most verbose; NONE is the least (silent)", "default": "LOG", "required": true}]}