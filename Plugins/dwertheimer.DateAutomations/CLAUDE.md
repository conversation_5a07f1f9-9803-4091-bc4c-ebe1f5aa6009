# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is the DateAutomations plugin for NotePlan by dwertheimer. The plugin provides various date and time automation commands for NotePlan users.

## Plugin Structure

- `plugin.json` - Plugin configuration, commands, and settings definitions
- `script.js` - Bundled JavaScript code containing all plugin functionality
- `README.md` - User documentation
- `CHANGELOG.md` - Version history and changes

## Key Commands

The plugin provides date/time insertion and automation commands such as:
- Insert dates in various formats
- Insert times and timestamps
- Create date-based links
- Calendar note automation

## NotePlan Plugin API

Key API objects used:
- **Editor** - For inserting text and manipulating notes
- **DataStore** - For accessing settings and calendar notes
- **CommandBar** - For user interaction and prompts
- **Calendar** - For date calculations and calendar integration

## Development Notes

- This is a compiled/bundled plugin - the `script.js` is generated from source
- Settings are stored in `../data/dwertheimer.DateAutomations/settings.json`
- The plugin follows NotePlan's command-based architecture
- Commands are registered in `plugin.json` and implemented in the bundled JavaScript