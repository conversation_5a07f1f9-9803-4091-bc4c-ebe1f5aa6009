# dwertheimer.DatAutomations Changelog

## Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.3.3] - 2024-04-26 (@dwertheimer)

- bump just to re-release same plugin

## [1.3.2] 2022-05-17 @dwertheimer (thx to @mattcito)
- Fix en-GB in settings instructions

## [1.3.1] 2022-04-16 @dwertheimer (thx to @oschrenk)
- Fixed bug in /now-8601 (wasn't showing the time)
- Added more to the readme

## [1.3.0] 2022-04-15 @dwertheimer (thx to @oschrenk)
- Removed reliance on _configuration file
- Added plugin-specific settings

## [1.2.0] - 2021-09-03 (dwerthe<PERSON><PERSON>) added date8601 back in for templates -- got dropped along the way

## [1.1.1] - 2021-08-22 (mike<PERSON><PERSON>)

### Fixed
- fixed `/formatted` direct call, was forcing default instead of using `_configuration`

## [1.1.0] - 2021-08-21 (mikeerickson)

### Added
- Added `formatted` option (using /formatted) and selecting `formatted` from `/dp` choice

## 1.0.9    Now compiled for macOS versions back to 10.13.0.

## 1.0.1    Removing "macOS.minVersion" - no longer necessary with transpiling

## 1.0.0    Adding /now - ISO 8601 standard
            Fix bug in /time

## 0.0.2    Adding 	"macOS.minVersion": "10.15.7"

## 0.0.1    Initial version
