{"noteplan.minAppVersion": "3.6.1", "macOS.minVersion": "10.13.0", "plugin.id": "jgclark.QuickCapture", "plugin.name": "⚡️ Quick Capture", "plugin.description": "Commands to more quickly add tasks/todos or general text to NotePlan notes. See website for configuration of special Inbox note, and how to use from other apps through x-callback calls.", "plugin.icon": "", "plugin.author": "<PERSON>", "plugin.url": "https://github.com/NotePlan/plugins/tree/main/jgclark.QuickCapture/", "plugin.version": "0.16.1", "plugin.lastUpdateInfo": "0.16.1: fix to display of relative dates in commmand bar; commands now work when adding items to future calendar notes that don't yet exist.\n0.16.0: new \"/jot\" and \"/quick add checklist under heading\" commands. Some bug fixes.\n0.15.2: bug fix.\n0.15.1: new x-callback args when creating headings. Bug fixes.\n0.15.0: new \"/quick add to this month's journal\" and \"/quick add to this year's journal\" commands. Speeding up some /quick... commands, and a bug fix.\n0.14.1: bug fixes\n0.14.0: allow relative dates in '/quick append task/line under heading' and other commands.", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.isRemote": "false", "plugin.commands": [{"name": "quick add text to inbox", "alias": ["jot", "inj"], "description": "Quickly 'jot' (add some text) to your chosen Inbox note", "jsFunction": "addJotToInbox", "arguments": ["text to add", "title of destination note (or 'Daily' or 'Weekly')"]}, {"name": "quick add task to inbox", "alias": ["int"], "description": "Quickly add a task to your chosen Inbox note", "jsFunction": "addTaskToInbox", "arguments": ["text to add", "title of destination note (or 'Daily' or 'Weekly')"]}, {"name": "quick append task to note", "alias": ["qat"], "description": "Quickly append a task to a chosen project note", "jsFunction": "appendTaskToNote", "arguments": ["note title", "task to append"]}, {"name": "quick add checklist under heading", "alias": ["qach"], "description": "Quickly add a checklist to a chosen note's section heading", "jsFunction": "addChecklistToNoteHeading", "arguments": ["note title (or YYYYMMDD, YYYY-MM-DD, YYYY-Wnn, or relative date for an existing calendar note)", "note section heading to add checklist under", "text to add", "heading level (1-5) (defaults to 2)"]}, {"name": "quick add task under heading", "alias": ["qath"], "description": "Quickly add a task to a chosen note's section heading", "jsFunction": "addTaskToNoteHeading", "arguments": ["note title (or YYYYMMDD, YYYY-MM-DD, YYYY-Wnn, or relative date for an existing calendar note)", "note section heading to add task under", "text to add", "heading level (1-5) (defaults to 2)"]}, {"name": "quick add line under heading", "alias": ["qalh"], "description": "Quickly add text to a chosen note's section heading", "jsFunction": "addTextToNoteHeading", "arguments": ["note title (or YYYYMMDD, YYYY-MM-DD, YYYY-Wnn, or relative date for an existing calendar note)", "note section heading to add text under", "text to add", "heading level (1-5)"]}, {"name": "quick add to calendar note", "alias": ["qac", "qad", "append"], "description": "Quickly append a task to a chosen calendar note", "jsFunction": "appendTaskToCalendarNote", "arguments": ["note date: e.g. YYYYMMDD, YYYY-MM-DD, YYYY-Wnn, or relative date ('tomorrow', 'next week' etc.)", "text to add"]}, {"name": "quick add to today's journal", "alias": ["qajd", "add", "today"], "description": "Quickly append text to the Journal in today's note", "jsFunction": "appendTextToDailyJournal", "arguments": ["text to add"]}, {"name": "quick add to journal this week", "alias": ["qajw"], "description": "Quickly append text to the Journal in this week's note", "jsFunction": "appendTextToWeeklyJournal", "arguments": ["text to add"]}, {"name": "quick add to this month's journal", "alias": ["qajm"], "description": "Quickly append text to the Journal in this month's note", "jsFunction": "appendTextToMonthlyJournal", "arguments": ["text to add"]}, {"name": "quick add to this year's journal", "alias": ["qajm"], "description": "Quickly append text to the Journal in this year's note", "jsFunction": "appendTextToYearlyJournal", "arguments": ["text to add"]}, {"name": "quick prepend task to calendar note", "alias": ["qpd", "qpc"], "description": "Quickly prepend a task to a chosen calendar note", "jsFunction": "prependTaskToCalendarNote", "arguments": ["note date (YYYY-MM-DD, YYYYMMDD or YYYY-Wnn etc.)", "text to add"]}, {"name": "quick prepend task to note", "alias": ["qpt"], "description": "Quickly prepend a task to a chosen project note", "jsFunction": "prependTaskToNote", "arguments": ["note title", "task to append"]}, {"name": "QuickCapture: update plugin settings", "description": "Settings interface (even for iOS)", "jsFunction": "updateSettings"}], "plugin.commands_disabled": [{"name": "quick add to weekly note", "alias": ["qaw", "week", "append"], "description": "Quickly append a task to a chosen Weekly note", "jsFunction": "appendTaskToWeeklyNote", "arguments": ["note week (YYYY-Wnn)", "text to add"]}, {"name": "test: tempAddParaTest", "description": "tempAddParaTest", "jsFunction": "tempAddParaTest"}], "plugin.settings": [{"type": "heading", "title": "Inbox settings"}, {"key": "inboxLocation", "title": "Where is your Inbox note?", "description": "Select 'Daily' or 'Weekly' to use whatever is the current daily or weekly note.\nOr  choose 'Fixed' and then add the note title in the next setting.", "type": "string", "choices": ["Daily", "Weekly", "Fixed"], "default": "Weekly note", "required": true}, {"key": "inboxTitle", "title": "Inbox note title", "description": "If the previous setting is set to 'Fixed', this is where you set the Title of that note. Default \"📥 Inbox\"", "type": "string", "default": "📥 Inbox", "required": false}, {"key": "textToAppendToJots", "type": "string", "title": "Text to append to new inbox jots", "description": "Optional text (that can include hashtags, mentions or emojis) that will be appended to all new text jots captured to the inbox.", "default": "💡", "required": false}, {"key": "textToAppendToTasks", "type": "string", "title": "Text to append to new inbox tasks", "description": "Optional text (that can include hashtags, mentions or emojis) that will be appended to all new tasks captured to the inbox.", "default": "", "required": false}, {"type": "heading", "title": "Other settings"}, {"key": "addInboxPosition", "title": "Where to add in notes?", "description": "Where to add in the selected note (or section of a note): start (prepend) or end (append)?", "type": "string", "choices": ["append", "prepend"], "default": "prepend", "required": true}, {"key": "headingLevel", "title": "Heading level for new Headings", "description": "Heading level (1-5) to use when adding new headings in notes", "type": "number", "default": 2, "required": true}, {"key": "journalHeading", "type": "string", "title": "Heading for your Journal entries", "description": "Optional heading to add your journal entries under with /quick add to journal ... commands", "default": "Journal", "required": false}, {"type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "title": "Log Level", "description": "Set how much output will be displayed for this plugin in the NotePlan > Help > Plugin Console. DEBUG is the most verbose; NONE is the least (silent).", "type": "string", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "default": "INFO", "required": true}]}