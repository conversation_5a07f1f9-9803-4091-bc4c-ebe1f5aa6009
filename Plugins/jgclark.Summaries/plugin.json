{"noteplan.minAppVersion": "3.3.2", "macOS.minVersion": "10.13.0", "plugin.id": "jgclark.Summaries", "plugin.name": "⏱ Habits and Summaries", "plugin.description": "Generate summaries from notes for a given time period and saves to notes; show heatmap of when tasks were completed. Click link for more details and settings.", "plugin.icon": "", "plugin.author": "<PERSON>", "plugin.url": "https://github.com/NotePlan/plugins/tree/main/jgclark.Summaries/", "plugin.changelog": "https://github.com/NotePlan/plugins/blob/main/jgclark.Summaries/CHANGELOG.md", "plugin.version": "0.22.1", "plugin.lastUpdateInfo": "0.22.1: fix empty lines appearing in template output from `progressUpdate(...)` calls.\n0.22.0: Add support for checklist progress.\n0.21.0: Add Mermaid charting command. Fix to simple weekly CSV generation.\n0.20.3: Bug fix for progressUpdate() in templates.\n0.20.2: Added x-callback options for /periodStats.\n0.20.1: fix refresh after '/append progress update' command. Logging change.\n0.20.0: add new '/today progress' and '/heatmap for tag' commands, and add refresh button to /periodStats output.\n0.19.3: bug fixes on 'weekly stats generation' commands.\n0.19.2: change date library.\n0.19.1: bug fix\n. 0.19.0: adds totals and averages for hashtags as well. Improve output of averages.", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.isRemote": "false", "plugin.commands": [{"name": "appendProgressUpdate", "alias": ["ipu", "insertProgressUpdate", "apu", "progress", "habitTracker", "track"], "description": "Append weekly/monthly habit and progress update", "jsFunction": "makeProgressUpdate", "arguments": ["JSON5-formatted parameter list"]}, {"hidden": true, "name": "progressUpdate", "description": "template entry point to appendProgressUpdate command", "jsFunction": "progressUpdate", "arguments": ["JSON5-formatted parameter list", "Source ('command' or 'template')"]}, {"hidden": true, "name": "todayProgressFromTemplate", "description": "template entry point to makeTodayProgress command", "jsFunction": "todayProgressFromTemplate", "arguments": ["comma-separated list of items to count", "heading (optional)"]}, {"name": "today progress", "alias": ["tp"], "description": "insert today's progress update", "jsFunction": "todayProgress", "arguments": ["comma-separated list of items to count", "heading (optional)"]}, {"name": "periodStats", "alias": ["stp", "period", "stats", "count"], "description": "Generate counts (and other stats) of tags and mentions for a time period", "jsFunction": "statsPeriod", "arguments": ["calendar period (year, quarter, month, week, today, or an YYYY-MM-DD date)", "number within the calendar type (ignored for today and YYYY-MM-DD)", "year (ignored for YYYY-MM-DD)"]}, {"name": "heatmap for tag", "alias": [], "description": "Show a heatmap for a given tag or mention", "jsFunction": "showTagHeatmap", "arguments": ["HeatmapDefinition object (stringified and encoded)"]}, {"name": "heatmap for task completion", "alias": [], "description": "Show a heatmap for completion of tasks", "jsFunction": "showTaskCompletionHeatmap"}, {"name": "Weekly Stats as CSV", "alias": ["week", "stats"], "description": "Generate weekly stats for tags and mentions, and write CSV to a file", "jsFunction": "weeklyStatsCSV"}, {"name": "Weekly Stats for Mermaid", "alias": ["week", "stats"], "description": "Generate weekly stats for tags and mentions, and write to a file ready to chart in Mermaid", "jsFunction": "weeklyStatsMermaid"}, {"name": "Habits+Summaries: update plugin settings", "description": "Settings interface (even for iOS)", "jsFunction": "updateSettings"}, {"hidden": false, "name": "test:GenTaskStats", "description": "test task gen stats", "jsFunction": "testTaskGenStats"}, {"hidden": false, "name": "test:GenTagStats", "description": "test tag gen stats", "jsFunction": "testTagGenStats"}, {"hidden": false, "name": "test:JGCHeatmaps", "description": "Test JGC Heatmaps", "jsFunction": "testJGCHeatmaps"}], "plugin.commands-disabled": [{"name": "test:update Summaries plugin settings", "description": "Summaries: test update settings", "jsFunction": "testUpdate"}, {"name": "test:HeatMapGeneration1", "description": "test heatmap gen 1", "jsFunction": "testHeatMapGeneration1"}, {"name": "test:HeatMapGeneration2", "description": "test heatmap gen 2", "jsFunction": "testHeatMapGeneration2"}, {"name": "test:HeatMapGeneration3", "description": "test heatmap gen 3", "jsFunction": "testHeatMapGeneration3"}], "plugin.settings": [{"type": "heading", "title": "Habits and Summaries common settings"}, {"key": "foldersToExclude", "title": "Folders to exclude", "description": "List of folders to exclude in these commands. May be empty. (Note that @Trash, @Templates and @Archive are always excluded.)", "type": "[string]", "default": ["Summaries", "Saved Searches"], "required": false}, {"key": "headingLevel", "title": "Heading level", "description": "Heading level (1-5) to use when writing headings to notes", "type": "number", "default": 3, "required": true}, {"key": "progressYesNoChars", "title": "Yes/No characters", "description": "Pair of characters to use as Yes and No in output of Yes/No progress items. The first is for Yes, the second for No.\nHere are some you might like to use for Yes: ✓✔■⧫▉ and for No: ·◦✕✖◌□.\nYou can use emojis, such as ✅🟢❌👎🔴, but they are likely to mess up the horizontal spacing.\nDo NOT but a comma between them: this is just the two characters.", "type": "string", "default": "✓·", "required": false}, {"key": "excludeToday", "title": "Exclude today's entries?", "description": "Whether to exclude today's entries in summaries. Can be enabled if you tend to run the commands as part of a start-of-day routine, and only add the updates later in the day.", "type": "bool", "default": false, "required": true}, {"type": "separator"}, {"type": "heading", "title": "appendProgressUpdate command settings"}, {"key": "progressPeriod", "title": "What time period should the Progress update cover?", "description": "Pick one of the options: 'wtd' (week to date), 'userwtd' (user's week to date), 'last7d' (last 7 days), 'mtd' (month to date), 'last2w' (last 2 weeks), 'last4w' (last 4 weeks).\n(This can be overriden when called from a Template by setting the relevant parameter.)", "type": "string", "choices": ["wtd", "userwtd", "last7d", "mtd", "last2w", "last4w"], "default": "last7d", "required": true}, {"key": "progressDestination", "title": "Where to write the progress update?", "description": "Append to 'current' note, or to the current 'daily' or 'weekly' note.\n(If the progress update section already exists, it will be updated, rather than be repeated.)", "type": "string", "choices": ["current", "daily", "weekly"], "default": "current", "required": true}, {"key": "progressHeading", "title": "Progress heading", "description": "Heading to go before this output, to which is added the period that's covered. However, if it contains the string {{PERIOD}}, then the covered period will be inserted in place of this string wherever you want in the heading. If this is left blank, then no heading will be added.", "type": "string", "default": "Progress Update", "required": false}, {"key": "showSparklines", "title": "Include sparkline graphs?", "description": "Where appropriate, this adds basic ASCII-art sparklines for each item, reflecting each day's data in the period.\nNote: Sparklines won't be shown where the summarised time period is more than a month.", "type": "bool", "default": true, "required": true}, {"key": "progressYesNo", "title": "Yes/No items", "description": "Comma-separated list of #hashtags and/or @mentions to track by 'did I do it this day or not?'.\n(Note: you need to include the @ or # on the front.)", "type": "[string]", "default": [], "required": false}, {"key": "progressHashtags", "title": "#hashtags to count", "description": "List of simple #hashtags to include in Progress updates. If this list is empty, no hashtags will be included.\n(Note: you need to include the # of the #hashtag.)", "type": "[string]", "default": [], "required": false}, {"key": "progressHashtagsAverage", "title": "#hashtags to average", "description": "Optional list of #hashtag/<number> to include in Progress updates, presented as an average.\n(Note: you need to include the # of the #hashtag.)", "type": "[string]", "default": [], "required": false}, {"key": "progressHashtagsTotal", "title": "#hashtags to total", "description": "Optional list of #hashtag/<number> to include in Progress updates, presented as a total.\n(Note: you need to include the # of the #hashtag.)", "type": "[string]", "default": [], "required": false}, {"key": "progressMentions", "title": "@mentions to count", "description": "Optional list of simple @mentions to include in Progress updates. If this list is empty, no mentions will be included.\n(Note: you need to include the @ of the @mention.)", "type": "[string]", "default": [], "required": false}, {"key": "progressMentionsAverage", "title": "@mentions to average", "description": "Optional list of @mention(number) to include in Progress updates, presented as an average.\n(Note: you need to include the @ of the @mention.)", "type": "[string]", "default": [], "required": false}, {"key": "progressMentionsTotal", "title": "@mentions to total", "description": "Optional list of @mention(number)s to include in Progress updates, presented as a total.\n(Note: you need to include the @ of the @mention.)", "type": "[string]", "default": [], "required": false}, {"key": "progressChecklistReferenceNote", "title": "Title of Reference note for checklist items", "description": "Title of the note to use as a reference for checklist items. If this is left blank, then no reference note will be used.", "type": "string", "default": "", "required": false}, {"type": "separator"}, {"type": "heading", "title": "/today progress command settings"}, {"key": "todayProgressItems", "title": "#hashtags and @mentions to total", "description": "List of #hashtags and @mentions to include in Today Progress updates.", "type": "[string]", "default": [], "required": false}, {"key": "todayProgressHeading", "title": "Today Progress heading", "description": "Heading to go before this output. If this is left blank, then no heading will be added (and no refresh button).", "type": "string", "default": "Progress Update", "required": false}, {"type": "separator"}, {"type": "heading", "title": "/periodStats command settings"}, {"key": "folderToStore", "title": "Folder for output", "description": "Name of the Folder to store the summaries in.\nNote: from NotePlan v3.7.2, the first option will be to use the built-in daily/weekly/monthly/quarterly or yearly notes instead. That will also be used if this is empty.", "type": "string", "default": "Summaries", "required": false}, {"key": "statsHeading", "title": "Stats heading", "description": "Heading to go before the output section. The plugin adds to it the period that the stats covers.", "type": "string", "default": "Period Stats", "required": true}, {"key": "hashtagCountsHeading", "title": "Hashtag counts heading", "description": "(Optional) Heading to go before section of #hashtag stats", "type": "hidden", "default": "", "required": false}, {"key": "mentionCountsHeading", "title": "Mention counts heading", "description": "(Optional) Heading to go before section of @mention stats", "type": "hidden", "default": "", "required": false}, {"key": "periodStatsShowSparklines", "title": "Include sparkline graphs?", "description": "Show basic ASCII-art sparklines for each item, reflecting each day's data in the period.\nNote: Sparklines won't be shown where the summarised time period is more than a month.", "type": "bool", "default": false, "required": true}, {"key": "showAsHashtagOrMention", "title": "Show hashtag or mention as links?", "description": "Whether to show the # or @ symbols, or hide them to stop them being active links. (Beware double counting if you turn this on and save results in daily notes.)", "type": "bool", "default": true, "required": true}, {"key": "periodStatsYesNo", "title": "Yes/No items", "description": "Comma-separated list of #hashtags and/or @mentions to track by 'did I do it this day or not?'.\n(Note: you need to include the @ or # on the front.)", "type": "[string]", "default": [], "required": false}, {"key": "includeHashtags", "title": "#hashtags to count", "description": "List of #hashtags to include in counts (e.g. '#holiday', '#jog', '#commute', '#webinar').", "_description": "List of #hashtags to include in counts (e.g. '#holiday', '#jog', '#commute', '#webinar'). These take precedence over any excluded hashtags (below). If this list is empty, all hashtags will be included.", "type": "[string]", "default": [], "required": false}, {"key": "excludeHashtags", "hidden": true, "title": "#hashtags to exclude", "description": "List of #hashtags to exclude in counts. If empty, none will be excluded.", "default": [], "required": false}, {"key": "periodStatsHashtagsAverage", "title": "#hashtags to average", "description": "Optional list of #hashtag(number)s to include in Period Stats updates, presented as an average.\n(Note: you need to include the # of the #hashtag.)", "type": "[string]", "default": [], "required": false}, {"key": "periodStatsHashtagsTotal", "title": "#hashtags to total", "description": "Optional list of #hashtag(number)s to include in Period Stats updates, presented as a total.\n(Note: you need to include the # of the #hashtag.)", "type": "[string]", "default": [], "required": false}, {"key": "periodStatsMentions", "title": "@mentions to count", "description": "List of @mentions to include in counts (e.g. '@gym', '@sleepOnTime').", "_description": "List of @mentions to include in counts (e.g. '@gym', '@sleepOnTime'). These take precedence over any excluded mentions (below). If this list is empty, all mentions will be included.", "type": "[string]", "default": [], "required": false}, {"key": "excludeMentions", "hidden": true, "title": "@mentions to exclude", "description": "List of @mentions to exclude in counts. If empty, none will be excluded.", "default": ["@done", "@repeat"], "required": false}, {"key": "periodStatsMentionsAverage", "title": "@mentions to average", "description": "Optional list of @mention(number)s to include in Period Stats updates, presented as an average.\n(Note: you need to include the @ of the @mention.)", "type": "[string]", "default": [], "required": false}, {"key": "periodStatsMentionsTotal", "title": "@mentions to total", "description": "Optional list of @mention(number)s to include in Period Stats updates, presented as a total.\n(Note: you need to include the @ of the @mention.)", "type": "[string]", "default": [], "required": false}, {"type": "separator"}, {"type": "heading", "title": "Chart command settings"}, {"key": "weeklyStatsItems", "title": "Items to Chart", "description": "List of @mentions or #hashtags to generate stats ready to chart.", "type": "[string]", "required": false, "default": []}, {"key": "weeklyStatsDuration", "title": "Chart Duration (in weeks)", "description": "Number of weeks to look back when generating stats (including heatmaps), not including sparklines.", "type": "number", "required": true, "default": 26}, {"key": "weeklyStatsIncludeCurrentWeek", "title": "Include current week?", "description": "Whether this include the (probably incomplete) current week, or only completed weeks.", "type": "bool", "required": true, "default": false}, {"type": "separator"}, {"type": "heading", "title": "Debugging"}, {"key": "_logLevel", "title": "Log Level", "description": "Set how much output will be displayed for this plugin in the NotePlan > Help > Plugin Console. DEBUG is the most verbose; NONE is the least (silent).", "type": "string", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "default": "INFO", "required": true}]}