{"noteplan.minAppVersion": "3.6.0", "macOS.minVersion": "10.13.0", "plugin.id": "jgclark.SearchExtensions", "plugin.name": "🔎 Search Extensions", "plugin.description": "This plugin allows more powerful search operators, searches to be saved and refreshed with a single click, and to replace text across multiple notes.", "plugin.icon": "", "plugin.author": "<PERSON>", "plugin.url": "https://github.com/NotePlan/plugins/tree/main/jgclark.SearchExtensions/", "plugin.changelog": "https://github.com/NotePlan/plugins/blob/main/jgclark.SearchExtensions/CHANGELOG.md", "plugin.version": "2.0.0", "plugin.lastUpdateInfo": "2.0.0: Adds '/replace' commands. Other improvements. Note: there are breaking changes to x-callback arguments.\n1.4.0: Adds case sensitive searching. Adds full-word searching. Tweaks and fixes.\n1.3.0: Adds `*` and `?` wildcard operators. Adds auto-refresh capability. Other improvements. (Please see documentation for details.)\n1.2.4: Fix /flexiSearch issues on iOS\n1.2.3: change to allow /quickSearch from x-callback without search term.\n1.2.2: ability to run FlexiSearch without closing the Dashboard and Project list windows from other plugins.\n1.2.1: fix bug in /searchInPeriod. 1.2.0: multi-word search terms.\n1.1: New 'flexiSearch' command. Adds limits to very large search results, to prevent overwhelming the app. Deals with 'twitter.com' case. Lots of other polish.\n1.0: Major new release with more powerful search syntax, a new display style, sync-ing open tasks and more. Please see the README to learn more!", "plugin.dependencies": [], "plugin.script": "script.js", "plugin.isRemote": "false", "plugin.requiredFiles": ["flexiSearch.css"], "plugin.requiredSharedFiles": ["fontawesome.css", "regular.min.flat4NP.css", "solid.min.flat4NP.css", "fa-regular-400.woff2", "fa-solid-900.woff2"], "plugin.commands": [{"name": "quickSearch", "alias": ["qs", "save", "search"], "description": "quick Search over all notes, showing results in a fixed Quick Search results note", "jsFunction": "quickSearch", "arguments": ["search term(s) (separated by commas)", "terms to filter by paragraph type (separated by commas)", "noteTypesToInclude either 'project','calendar' or 'both'", "(optional) destination: either 'current', 'newnote' or 'quick'"]}, {"name": "search", "alias": ["ss", "save"], "description": "Save results from a search over all notes", "jsFunction": "searchOverAll", "arguments": ["search term(s) (separated by commas)", "terms to filter by paragraph type (separated by commas)", "ignored placeholder", "(optional) destination: either 'current', 'newnote' or 'quick'"]}, {"name": "searchOpenTasks", "alias": ["sot", "search", "sync"], "description": "Save results from a search over all open tasks in all notes", "jsFunction": "searchOpenTasks", "arguments": ["search term(s) (separated by commas)", "paragraph type filter terms (optional; separated by commas)", "ignored placeholder", "(optional) destination: either 'current', 'newnote' or 'quick'"]}, {"name": "searchInPeriod", "alias": ["sip", "save", "calendar", "search", "daily", "weekly"], "description": "Save results from a search of specified tags or mentions over Calendar notes from a time period", "jsFunction": "searchPeriod", "arguments": ["search term(s) (separated by commas)", "paragraph type filter terms (optional; separated by commas)", "noteTypesToInclude either 'project','calendar' or 'both'", "(optional) destination: either 'current', 'newnote' or 'quick'", "start date to search over (YYYYMMDD or YYYY-MM-DD). If not given, then defaults to 3 months ago", "end date to search over (YYYYMMDD or YYYY-MM-DD). If not given, then defaults to today"]}, {"name": "searchOverCalendar", "alias": ["soc", "save", "period", "search", "daily", "weekly"], "description": "Save results from a search of specified tags or mentions over all Calendar notes", "jsFunction": "searchOverCalendar", "arguments": ["search term(s) (separated by commas)", "paragraph type filter terms (optional; separated by commas)", "ignored placeholder", "(optional) destination: either 'current', 'newnote' or 'quick'"]}, {"name": "searchOverNotes", "alias": ["son", "save", "search"], "description": "Save results from a search over all project notes", "jsFunction": "searchOverNotes", "arguments": ["search term(s) (separated by commas)", "paragraph type filter terms (optional; separated by commas)", "ignored placeholder", "(optional) destination: either 'current', 'newnote' or 'quick'"]}, {"name": "flexiSearch", "alias": ["fs", "save", "search"], "description": "Save results from a search with the most flexible of options", "jsFunction": "showFlexiSearchDialog", "arguments": [], "comment": "Note: no arguments possible"}, {"name": "replace over all notes", "alias": ["repl"], "description": "Search and Replace over all notes", "jsFunction": "replaceOverAll", "arguments": ["search term(s) (separated by commas)", "replace expression", "paragraph types (separated by commas)"]}, {"name": "replace over Regular notes", "alias": ["replreg"], "description": "Search and Replace over Project notes", "jsFunction": "replaceOverNotes", "arguments": ["search term(s) (separated by commas)", "replace expression", "paragraph types (separated by commas)"]}, {"name": "replace over Calendar notes", "alias": ["replcal"], "description": "Search and Replace over Calendar notes", "jsFunction": "replaceOverCalendar", "arguments": ["search term(s) (separated by commas)", "replace expression", "paragraph types (separated by commas)"]}, {"hidden": true, "name": "refreshSavedSearch", "description": "Trigger to refresh a saved search on note open", "jsFunction": "refreshSavedSearch", "arguments": []}, {"hidden": true, "name": "flexiSearchHandler", "description": "Called by flexiSearch dialog", "jsFunction": "flexiSearchHandler", "arguments": ["searchTerms", "noteTypesToInclude", "paraTypes"]}, {"hidden": true, "name": "closeDialogWindow", "description": "Called by flexiSearch dialog", "jsFunction": "closeDialogWindow", "arguments": ["customId"]}, {"hidden": true, "name": "getPluginPreference", "description": "Called by flexiSearch dialog", "jsFunction": "getPluginPreference", "arguments": ["key"]}, {"hidden": true, "name": "savePluginPreference", "description": "Called by flexiSearch dialog", "jsFunction": "savePluginPreference", "arguments": ["key", "value"]}, {"name": "Search: update plugin settings", "description": "Settings interface (even for iOS)", "jsFunction": "updateSettings"}], "plugin.commands_disabled": [{"comment": "??? I think this was for testing refresh. Can it be removed now?", "hidden": true, "name": "refreshSavedSearch", "description": "onOpen", "jsFunction": "refreshSavedSearch"}, {"name": "test:updateSearchPlugin", "description": "test:updateSearchPlugin", "jsFunction": "onUpdateOrInstall"}], "plugin.settings": [{"type": "heading", "title": "Search Extensions settings"}, {"key": "caseSensitiveSearching", "title": "Case Sensitive searching?", "description": "By default searches in NotePlan ignore the case (capitalisation) of characters. This plugin will use case sensitive searching if this is turned on.", "type": "bool", "default": false, "required": true}, {"key": "fullWordSearching", "title": "Match only on full words?", "description": "By default search terms in NotePlan matches on parts of longer words. Turn this on to restrict searches to matching full words only.", "type": "bool", "default": false, "required": true}, {"key": "foldersToExclude", "title": "Folders to exclude", "description": "Optional list of folders to exclude in these commands. If a folder is listed, then sub-folders are also excluded.\nTo exclude the top-level folder, use '/'. (The special Trash folder is always excluded.)", "type": "[string]", "default": ["Summaries", "Saved Searches", "@Templates"], "required": false}, {"type": "separator"}, {"type": "heading", "title": "Result saving options"}, {"key": "autoSave", "title": "Automatically save", "description": "If true, will save to an automatically-named note in the configured folder, starting with the search terms. \nThis always applies for /quickSearch, but can be turned off for other commands.", "type": "bool", "default": false, "required": true}, {"key": "folderToStore", "title": "Folder name", "description": "Folder to store output files in.", "type": "string", "default": "Saved Searches", "required": true}, {"key": "quickSearchResultsTitle", "title": "/quickSearch note title", "description": "Note title for /quickSearch results.", "type": "string", "default": "Quick Search Results", "required": true}, {"type": "separator"}, {"type": "heading", "title": "Output options"}, {"key": "resultStyle", "title": "Display style for search result lines", "description": "Choose the style to use:\n- Normal 'NotePlan' styling, showing tasks, bullets and quotes, tweaked slightly for matching headings. You need to use this if you want to use the power of sync'd lines with the /searchOpenTasks command.\n- Use 'Simplified' text, more like web search engine results.\nNote: this affects some of the following settings.", "type": "string", "choices": ["NotePlan", "Simplified"], "default": "NotePlan-style", "required": true}, {"key": "resultLimit", "title": "Result set size limit", "description": "Result set size limit: if the search produces more than this, it will only return the first ones up to this limit.", "type": "number", "default": 500, "required": true}, {"key": "headingLevel", "title": "Heading level", "description": "Heading level (1-5) to use when writing section headings in output.", "type": "number", "default": 2, "required": true}, {"key": "searchHeading", "title": "Saved Search heading", "description": "Text to append to headings in search results (optional).", "type": "string", "default": "(Search Results)", "required": false}, {"key": "sortOrder", "title": "Sort order for results", "description": "This controls the order that the results are displayed in.", "type": "string", "choices": ["note title", "folder name then note title", "created (newest note first)", "created (oldest note first)", "updated (most recent note first)", "updated (least recent note first)"], "default": "updated (most recent first)", "required": true}, {"key": "groupResultsByNote", "title": "Group results by Note?", "description": "This controls how results are displayed. If true, matches found within the same note are grouped together. If false, every match is shown with a note link at the end of the match.", "type": "bool", "default": true, "required": true}, {"key": "resultPrefix", "title": "Prefix for search result lines", "description": "String to put at the start of each search result line (where display style is 'Simplified'). Default is '- '. Can also be empty.", "type": "string", "default": "- ", "required": false}, {"key": "resultQ<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Result quote length", "description": "Length of matching line to quote in the search results. To always quote the full line set this to 0. Note: this only applies in the 'Simplified' display style.", "type": "number", "default": 100, "required": true}, {"key": "highlightResults", "title": "Highlight matching search terms?", "description": "Whether to ==highlight== the matches in the result lines. (Works best when using a theme with highlighting.)", "type": "bool", "default": true, "required": true}, {"key": "dateStyle", "title": "Date style", "description": "Where the match is in a calendar note, choose where that link is shown as\n- a 'date' using your locale\n- an NP date 'link' (e.g. [[2022-06-30]])\n- an 'at' date (e.g. @2022-06-30), or\n- a 'scheduled' date (e.g. >2022-06-30).", "type": "string", "choices": ["at", "date", "link", "scheduled"], "default": "link", "required": true}, {"type": "separator"}, {"type": "heading", "title": "For Debugging"}, {"key": "defaultSearchTerms", "title": "Default Search terms", "description": "Optional list of search terms to use to pre-populate the search term box.", "type": "[string]", "default": ["idea", "@review", "#question"], "required": false}, {"key": "_logLevel", "title": "Log Level", "description": "Set how much output will be displayed for this plugin in the NotePlan > Help > Plugin Console. DEBUG is the most verbose; NONE is the least (silent).", "type": "string", "choices": ["DEBUG", "INFO", "WARN", "ERROR", "none"], "default": "INFO", "required": true}, {"key": "_logTimer", "title": "Enable Timer logging?", "description": "For plugin authors to help optimise the plugin.", "type": "bool", "default": false, "required": true}]}